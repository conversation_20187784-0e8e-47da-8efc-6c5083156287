#!/bin/bash

# LiveKit + Pipecat Demo Setup Script
set -e

echo "🚀 Setting up LiveKit + Pipecat Demo..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 is required but not installed.${NC}"
    exit 1
fi

# Check if Docker is installed (for local LiveKit option)
if ! command -v docker &> /dev/null; then
    echo -e "${YELLOW}⚠️  Docker not found. You'll need to use LiveKit Cloud.${NC}"
fi

# Create agent directory if it doesn't exist
mkdir -p agent
mkdir -p client

# Setup Python virtual environment
echo -e "${BLUE}📦 Setting up Python environment...${NC}"
cd agent

if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo -e "${GREEN}✅ Created virtual environment${NC}"
fi

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
echo -e "${BLUE}📦 Installing Python dependencies...${NC}"
pip install --upgrade pip
pip install -r requirements.txt

echo -e "${GREEN}✅ Python environment ready${NC}"

# Check for configuration
if [ ! -f "config.py" ]; then
    echo -e "${YELLOW}⚠️  config.py not found. Creating template...${NC}"
    cp config.py.template config.py
    echo -e "${RED}❌ Please edit agent/config.py with your credentials before running the demo${NC}"
    exit 1
fi

cd ..

echo -e "${GREEN}🎉 Setup complete!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Edit agent/config.py with your LiveKit and OpenAI credentials"
echo "2. Start the agent: cd agent && python spawn_agent.py"
echo "3. Open client/index.html in your browser"
echo ""
echo -e "${YELLOW}For local LiveKit server:${NC}"
echo "docker-compose up -d"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
