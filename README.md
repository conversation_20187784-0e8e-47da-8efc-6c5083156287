# LiveKit + Pipecat Integration Demo

A real-time voice conversation demo showcasing LiveKit's WebRTC infrastructure with Pipecat's AI agent orchestration.

## 🎯 What This Demo Does

- **Real-time Voice Chat**: User speaks to an AI agent through LiveKit's WebRTC infrastructure
- **Echo + Response**: Agent echoes user input with "...got it" suffix using STT/TTS pipeline
- **Barge-in Support**: User can interrupt the agent mid-response
- **Latency Testing**: Built-in tools to measure round-trip audio latency
- **Production-Ready**: Uses LiveKit Cloud for reliable WebRTC transport

## 🏗️ Architecture

```
User Mic → LiveKit Client → LiveKit Server → Pipecat Agent → LiveKit Server → User Speaker
```

### Components:
- **LiveKit**: WebRTC media routing and real-time transport
- **Pipecat**: AI agent orchestration with STT/TTS pipeline
- **Frontend**: Vanilla HTML/JS with LiveKit JS SDK
- **Backend**: Python agent with OpenAI integration

## 🚀 Quick Start

1. **Setup Environment**:
   ```bash
   ./run.sh
   ```

2. **Configure Credentials** (see setup section below)

3. **Start the Demo**:
   - Agent will automatically join the room
   - Open `client/index.html` in your browser
   - Click "Join Room" and start talking!

## 📋 Setup Instructions

### 1. LiveKit Credentials

You have two options:

#### Option A: LiveKit Cloud (Recommended)
1. Sign up at [livekit.io](https://livekit.io)
2. Create a new project
3. Copy your credentials to `agent/config.py`:
   ```python
   LIVEKIT_URL = "wss://your-project.livekit.cloud"
   LIVEKIT_API_KEY = "your-api-key"
   LIVEKIT_API_SECRET = "your-api-secret"
   ```

#### Option B: Local LiveKit Server
```bash
docker-compose up -d
```

### 2. AI Service Credentials

Add to `agent/config.py`:
```python
OPENAI_API_KEY = "your-openai-key"  # For STT/TTS
```

### 3. Install Dependencies

```bash
cd agent
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

## 🎮 Usage

1. **Start the Agent**:
   ```bash
   cd agent
   python spawn_agent.py
   ```

2. **Open Web Client**:
   - Open `client/index.html` in your browser
   - Click "Join Room"
   - Start speaking!

3. **Test Latency**:
   - Click "Send Beep" to measure round-trip latency
   - Check browser console for timing logs

## 🔧 Testing Scenarios

- **Basic Echo**: Say "Hello" → Agent responds "Hello...got it"
- **Barge-in**: Interrupt agent while it's speaking
- **Latency**: Use beep test to measure round-trip time
- **Connection**: Test reconnection after network issues

## 📊 Performance Targets

- **Latency**: < 600ms round-trip (goal: < 400ms)
- **Barge-in**: < 200ms interruption detection
- **Reliability**: Stable connection for 10+ minute conversations

## 🏗️ Project Structure

```
livekit-pipecat-demo/
├── docker-compose.yml          # Local LiveKit server
├── client/
│   ├── index.html             # Web client UI
│   ├── client.js              # LiveKit connection logic  
│   └── latency-test.js        # Beep/echo measurement
├── agent/
│   ├── requirements.txt       # Python dependencies
│   ├── spawn_agent.py         # Main Pipecat agent
│   └── config.py             # Credentials & config
├── run.sh                     # One-click setup
└── README.md                 # This file
```

## 🤔 Reflection: LiveKit vs Alternatives

### LiveKit Pros:
- ✅ Production-ready WebRTC infrastructure
- ✅ Excellent latency (< 100ms transport)
- ✅ Built-in scaling and reliability
- ✅ Great developer experience
- ✅ Multi-platform SDKs

### LiveKit Cons:
- ❌ Additional service dependency
- ❌ Learning curve for WebRTC concepts
- ❌ Cost for hosted solution

### Pipecat Pros:
- ✅ Excellent agent orchestration
- ✅ Built-in STT/TTS pipeline management
- ✅ Easy barge-in handling
- ✅ Modular architecture

### Pipecat Cons:
- ❌ Newer ecosystem (fewer examples)
- ❌ Python-only (for now)
- ❌ Requires understanding of pipeline concepts

## 🚀 Production Recommendations

For production deployment:

1. **Use LiveKit Cloud** for reliability and scaling
2. **Implement proper authentication** (JWT tokens)
3. **Add error handling** and reconnection logic
4. **Monitor latency** and connection quality
5. **Consider edge deployment** for global users
6. **Implement rate limiting** and abuse prevention

## 🐛 Troubleshooting

- **No audio**: Check microphone permissions
- **High latency**: Test network connection, try different regions
- **Agent not responding**: Check Python logs and API credentials
- **Connection issues**: Verify LiveKit server status

## 📝 License

MIT License - Feel free to use this demo as a starting point for your own projects!
