<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit SDK Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 LiveKit SDK Test</h1>
    
    <div id="test-results">
        <div class="test-result info">Testing LiveKit SDK loading...</div>
    </div>

    <!-- Try multiple CDN sources -->
    <script src="https://cdn.jsdelivr.net/npm/livekit-client@1.15.13/dist/livekit-client.umd.js" 
            onerror="tryAlternativeCDN()"></script>
    
    <script>
        let cdnAttempts = 0;
        const cdnSources = [
            'https://unpkg.com/livekit-client@1.15.13/dist/livekit-client.umd.js',
            'https://cdn.skypack.dev/livekit-client@1.15.13',
            'https://esm.sh/livekit-client@1.15.13'
        ];

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function tryAlternativeCDN() {
            if (cdnAttempts < cdnSources.length) {
                addTestResult(`❌ CDN ${cdnAttempts + 1} failed, trying alternative...`, 'error');
                const script = document.createElement('script');
                script.src = cdnSources[cdnAttempts];
                script.onerror = tryAlternativeCDN;
                script.onload = testLiveKit;
                document.head.appendChild(script);
                cdnAttempts++;
            } else {
                addTestResult('❌ All CDN sources failed. LiveKit SDK could not be loaded.', 'error');
                addTestResult('💡 Try refreshing the page or check your internet connection.', 'info');
            }
        }

        function testLiveKit() {
            addTestResult('✅ LiveKit SDK script loaded successfully', 'success');
            
            // Test if LiveKit object is available
            if (typeof LiveKit !== 'undefined') {
                addTestResult('✅ LiveKit object is available', 'success');
                
                // Test LiveKit components
                const components = ['Room', 'Track', 'RemoteParticipant', 'LocalParticipant'];
                components.forEach(component => {
                    if (LiveKit[component]) {
                        addTestResult(`✅ LiveKit.${component} is available`, 'success');
                    } else {
                        addTestResult(`❌ LiveKit.${component} is missing`, 'error');
                    }
                });

                // Show LiveKit version if available
                if (LiveKit.version) {
                    addTestResult(`📦 LiveKit version: ${LiveKit.version}`, 'info');
                }

                // Test Room creation
                try {
                    const room = new LiveKit.Room();
                    addTestResult('✅ Can create LiveKit Room instance', 'success');
                    
                    // Show available methods
                    const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(room))
                        .filter(name => typeof room[name] === 'function' && name !== 'constructor')
                        .slice(0, 10); // Show first 10 methods
                    
                    addTestResult(`📋 Available Room methods: ${methods.join(', ')}...`, 'info');
                    
                } catch (error) {
                    addTestResult(`❌ Error creating Room: ${error.message}`, 'error');
                }

            } else {
                addTestResult('❌ LiveKit object is not available after script load', 'error');
                addTestResult('🔍 This might be a compatibility issue or the script failed to execute', 'info');
            }
        }

        // Test when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof LiveKit !== 'undefined') {
                    testLiveKit();
                } else {
                    addTestResult('⏳ LiveKit not loaded yet, trying alternative CDNs...', 'info');
                    tryAlternativeCDN();
                }
            }, 500);
        });

        // Also test immediately if already loaded
        if (typeof LiveKit !== 'undefined') {
            testLiveKit();
        }
    </script>

    <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 5px;">
        <h3>🔧 Troubleshooting</h3>
        <ul>
            <li><strong>If LiveKit fails to load:</strong> Check your internet connection</li>
            <li><strong>If components are missing:</strong> Try a different browser (Chrome/Firefox recommended)</li>
            <li><strong>If Room creation fails:</strong> Check browser console for detailed errors</li>
        </ul>
        
        <p style="margin-top: 15px;">
            <a href="index.html" style="color: #007bff;">← Back to Demo</a> |
            <a href="status.html" style="color: #007bff;">Setup Status</a>
        </p>
    </div>
</body>
</html>
