<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit + Pipecat Demo - Simple Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        h1 {
            color: #4a5568;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .status {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 600;
        }

        .status.success {
            background: #c6f6d5;
            color: #2f855a;
            border: 2px solid #38a169;
        }

        .status.error {
            background: #fed7d7;
            color: #c53030;
            border: 2px solid #e53e3e;
        }

        .status.warning {
            background: #fef5e7;
            color: #d69e2e;
            border: 2px solid #d69e2e;
        }

        .status.info {
            background: #ebf8ff;
            color: #2b6cb0;
            border: 2px solid #4299e1;
        }

        .controls {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 120px;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #38a169;
            color: white;
        }

        .btn-success:hover {
            background: #2f855a;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover {
            background: #c53030;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .audio-controls {
            margin: 1rem 0;
        }

        .volume-meter {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .volume-bar {
            height: 100%;
            background: linear-gradient(90deg, #38a169, #d69e2e, #e53e3e);
            width: 0%;
            transition: width 0.1s;
        }

        .logs {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
            text-align: left;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
        }

        .setup-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }

        .command {
            background: #1a202c;
            color: #e2e8f0;
            padding: 0.5rem;
            border-radius: 5px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ LiveKit + Pipecat Demo</h1>
        <p style="margin-bottom: 2rem; color: #718096;">
            Real-time AI voice conversation with echo responses
        </p>

        <div id="status" class="status info">
            🔄 Initializing demo...
        </div>

        <div class="controls">
            <button id="connectBtn" class="btn btn-primary" onclick="connectToRoom()">
                🚀 Connect to Room
            </button>
            <button id="disconnectBtn" class="btn btn-danger" onclick="disconnectFromRoom()" disabled>
                🔌 Disconnect
            </button>
            <button id="testBtn" class="btn btn-success" onclick="testAudio()">
                🔊 Test Audio
            </button>
        </div>

        <div class="audio-controls">
            <div>🎤 Microphone Level:</div>
            <div class="volume-meter">
                <div id="volumeBar" class="volume-bar"></div>
            </div>
        </div>

        <div class="setup-info">
            <h3>🔧 Setup Requirements</h3>
            <p><strong>1. Start LiveKit Server:</strong></p>
            <div class="command">docker-compose up -d</div>
            
            <p><strong>2. Start AI Agent:</strong></p>
            <div class="command">cd agent && source venv/bin/activate && python simple_agent.py</div>
            
            <p><strong>3. Grant Microphone Permission</strong> when prompted by browser</p>
        </div>

        <div class="logs" id="logs">
            <div>📋 Demo logs will appear here...</div>
        </div>

        <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #e2e8f0;">
            <a href="test.html" style="color: #4299e1; margin-right: 1rem;">🧪 Test LiveKit SDK</a>
            <a href="status.html" style="color: #4299e1; margin-right: 1rem;">📊 Setup Status</a>
            <a href="index.html" style="color: #4299e1;">🎯 Full Demo</a>
        </div>
    </div>

    <script>
        let audioContext;
        let microphone;
        let analyser;
        let isConnected = false;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.innerHTML = message;
        }

        async function testAudio() {
            log('🎤 Testing microphone access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    } 
                });
                
                log('✅ Microphone access granted');
                updateStatus('✅ Microphone working! Ready to connect.', 'success');
                
                // Set up audio visualization
                setupAudioVisualization(stream);
                
                // Stop the test stream after 5 seconds
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    log('🔇 Microphone test completed');
                }, 5000);
                
            } catch (error) {
                log(`❌ Microphone test failed: ${error.message}`, 'error');
                updateStatus('❌ Microphone access denied. Please grant permission.', 'error');
            }
        }

        function setupAudioVisualization(stream) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            microphone = audioContext.createMediaStreamSource(stream);
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
            
            microphone.connect(analyser);
            
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            
            function updateVolume() {
                analyser.getByteFrequencyData(dataArray);
                const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
                const percentage = (average / 255) * 100;
                
                document.getElementById('volumeBar').style.width = percentage + '%';
                
                if (stream.active) {
                    requestAnimationFrame(updateVolume);
                }
            }
            
            updateVolume();
        }

        async function connectToRoom() {
            log('🚀 Attempting to connect to LiveKit room...');
            updateStatus('🔄 Connecting to room...', 'info');
            
            // Check if LiveKit server is accessible
            try {
                const response = await fetch('http://localhost:7880/rtc', { method: 'HEAD' });
                log('✅ LiveKit server is accessible');
            } catch (error) {
                log('❌ Cannot reach LiveKit server. Is it running?', 'error');
                updateStatus('❌ LiveKit server not running. Please start: docker-compose up -d', 'error');
                return;
            }
            
            // Simulate connection (since we don't have LiveKit SDK loaded)
            log('⚠️ This is a simplified demo. LiveKit SDK integration pending.');
            updateStatus('⚠️ Simplified demo mode. Start LiveKit server and agent for full functionality.', 'warning');
            
            document.getElementById('connectBtn').disabled = true;
            document.getElementById('disconnectBtn').disabled = false;
            isConnected = true;
            
            // Test microphone
            await testAudio();
        }

        function disconnectFromRoom() {
            log('🔌 Disconnecting from room...');
            updateStatus('🔌 Disconnected', 'info');
            
            document.getElementById('connectBtn').disabled = false;
            document.getElementById('disconnectBtn').disabled = true;
            isConnected = false;
            
            if (audioContext) {
                audioContext.close();
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('🎯 Simple demo loaded. Click "Test Audio" to check microphone.');
            updateStatus('🎯 Ready to test! Click "Test Audio" to check your microphone.', 'info');
        });
    </script>
</body>
</html>
