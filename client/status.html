<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit + Pipecat Demo - Setup Status</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }

        h1 {
            color: #4a5568;
            margin-bottom: 1rem;
            font-size: 2rem;
            text-align: center;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .status-card {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            background: #f7fafc;
        }

        .status-card.success {
            border-color: #38a169;
            background: #c6f6d5;
        }

        .status-card.warning {
            border-color: #d69e2e;
            background: #fef5e7;
        }

        .status-card.error {
            border-color: #e53e3e;
            background: #fed7d7;
        }

        .status-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-description {
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .command {
            background: #1a202c;
            color: #e2e8f0;
            padding: 0.75rem;
            border-radius: 5px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            margin: 0.5rem 0;
            overflow-x: auto;
        }

        .next-steps {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .next-steps h3 {
            color: #2b6cb0;
            margin-bottom: 1rem;
        }

        .step {
            margin: 1rem 0;
            padding-left: 1.5rem;
            position: relative;
        }

        .step::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: #4299e1;
            color: white;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .next-steps ol {
            counter-reset: step-counter;
            list-style: none;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #3182ce;
        }

        .btn-success {
            background: #38a169;
        }

        .btn-success:hover {
            background: #2f855a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LiveKit + Pipecat Demo Setup</h1>
        
        <div class="status-grid">
            <div class="status-card success">
                <div class="status-title">
                    ✅ Web Client
                </div>
                <div class="status-description">
                    The web interface is running and ready to connect.
                </div>
                <a href="index.html" class="btn btn-success">Open Demo</a>
            </div>

            <div class="status-card success">
                <div class="status-title">
                    ✅ Python Environment
                </div>
                <div class="status-description">
                    Virtual environment created with all dependencies installed.
                </div>
            </div>

            <div class="status-card success">
                <div class="status-title">
                    ✅ Configuration
                </div>
                <div class="status-description">
                    OpenAI API key configured and tested successfully.
                </div>
            </div>

            <div class="status-card warning">
                <div class="status-title">
                    ⚠️ LiveKit Server
                </div>
                <div class="status-description">
                    Docker containers need to be started for the LiveKit server.
                </div>
                <div class="command">docker-compose up -d</div>
            </div>

            <div class="status-card warning">
                <div class="status-title">
                    ⚠️ AI Agent
                </div>
                <div class="status-description">
                    Python agent is ready but waiting for LiveKit server.
                </div>
                <div class="command">cd agent && source venv/bin/activate && python simple_agent.py</div>
            </div>
        </div>

        <div class="next-steps">
            <h3>🎯 Complete the Setup</h3>
            <ol>
                <li class="step">
                    <strong>Start LiveKit Server</strong><br>
                    Open a terminal with Docker access and run:
                    <div class="command">cd /Users/<USER>/repos/livekit-pipecat-demo<br>docker-compose up -d</div>
                </li>
                <li class="step">
                    <strong>Start the AI Agent</strong><br>
                    In the project directory:
                    <div class="command">cd agent<br>source venv/bin/activate<br>python simple_agent.py</div>
                </li>
                <li class="step">
                    <strong>Test the Demo</strong><br>
                    <a href="index.html" class="btn">Open Demo Interface</a><br>
                    Click "Join Room" and start speaking!
                </li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e2e8f0;">
            <p style="color: #718096; margin-bottom: 1rem;">
                <strong>Demo Features:</strong> Real-time voice chat • AI echo responses • Latency testing • Barge-in support
            </p>
            <a href="index.html" class="btn btn-success">🎙️ Start Demo</a>
            <a href="../README.md" class="btn">📖 Documentation</a>
        </div>
    </div>

    <script>
        // Auto-refresh status every 5 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);

        // Test LiveKit connection
        async function testConnection() {
            try {
                const response = await fetch('ws://localhost:7880');
                console.log('LiveKit server is running');
            } catch (error) {
                console.log('LiveKit server not accessible:', error.message);
            }
        }

        testConnection();
    </script>
</body>
</html>
