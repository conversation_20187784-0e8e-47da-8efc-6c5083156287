!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).LivekitClient={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(i){if("default"!==i&&!(i in e)){var n=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(e,i,n.get?n:{enumerable:!0,get:function(){return t[i]}})}}))})),Object.freeze(e)}var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s={exports:{}};!function(e){var t,n;t=i,n=function(){var e=function(){},t="undefined",i=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),n=["trace","debug","info","warn","error"];function s(e,t){var i=e[t];if("function"==typeof i.bind)return i.bind(e);try{return Function.prototype.bind.call(i,e)}catch(t){return function(){return Function.prototype.apply.apply(i,[e,arguments])}}}function r(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function o(t,i){for(var s=0;s<n.length;s++){var r=n[s];this[r]=s<t?e:this.methodFactory(r,t,i)}this.log=this.debug}function a(e,i,n){return function(){typeof console!==t&&(o.call(this,i,n),this[e].apply(this,arguments))}}function c(n,o,c){return function(n){return"debug"===n&&(n="log"),typeof console!==t&&("trace"===n&&i?r:void 0!==console[n]?s(console,n):void 0!==console.log?s(console,"log"):e)}(n)||a.apply(this,arguments)}function d(e,i,s){var r,a=this;i=null==i?"WARN":i;var d="loglevel";function l(){var e;if(typeof window!==t&&d){try{e=window.localStorage[d]}catch(e){}if(typeof e===t)try{var i=window.document.cookie,n=i.indexOf(encodeURIComponent(d)+"=");-1!==n&&(e=/^([^;]+)/.exec(i.slice(n))[1])}catch(e){}return void 0===a.levels[e]&&(e=void 0),e}}"string"==typeof e?d+=":"+e:"symbol"==typeof e&&(d=void 0),a.name=e,a.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},a.methodFactory=s||c,a.getLevel=function(){return r},a.setLevel=function(i,s){if("string"==typeof i&&void 0!==a.levels[i.toUpperCase()]&&(i=a.levels[i.toUpperCase()]),!("number"==typeof i&&i>=0&&i<=a.levels.SILENT))throw"log.setLevel() called with invalid level: "+i;if(r=i,!1!==s&&function(e){var i=(n[e]||"silent").toUpperCase();if(typeof window!==t&&d){try{return void(window.localStorage[d]=i)}catch(e){}try{window.document.cookie=encodeURIComponent(d)+"="+i+";"}catch(e){}}}(i),o.call(a,i,e),typeof console===t&&i<a.levels.SILENT)return"No console available for logging"},a.setDefaultLevel=function(e){i=e,l()||a.setLevel(e,!1)},a.resetLevel=function(){a.setLevel(i,!1),function(){if(typeof window!==t&&d){try{return void window.localStorage.removeItem(d)}catch(e){}try{window.document.cookie=encodeURIComponent(d)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}()},a.enableAll=function(e){a.setLevel(a.levels.TRACE,e)},a.disableAll=function(e){a.setLevel(a.levels.SILENT,e)};var u=l();null==u&&(u=i),a.setLevel(u,!1)}var l=new d,u={};l.getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=u[e];return t||(t=u[e]=new d(e,l.getLevel(),l.methodFactory)),t};var h=typeof window!==t?window.log:void 0;return l.noConflict=function(){return typeof window!==t&&window.log===l&&(window.log=h),l},l.getLoggers=function(){return u},l.default=l,l},e.exports?e.exports=n():t.log=n()}(s);var r,o,a=s.exports;e.LogLevel=void 0,(r=e.LogLevel||(e.LogLevel={}))[r.trace=0]="trace",r[r.debug=1]="debug",r[r.info=2]="info",r[r.warn=3]="warn",r[r.error=4]="error",r[r.silent=5]="silent",function(e){e.Default="livekit",e.Room="livekit-room",e.Participant="livekit-participant",e.Track="livekit-track",e.Publication="livekit-track-publication",e.Engine="livekit-engine",e.Signal="livekit-signal",e.PCManager="livekit-pc-manager",e.PCTransport="livekit-pc-transport",e.E2EE="lk-e2ee"}(o||(o={}));let c=a.getLogger("livekit");function d(e){const t=a.getLogger(e);return t.setDefaultLevel(c.getLevel()),t}function l(e,t){if(!e)throw new Error(t)}c.setDefaultLevel(e.LogLevel.info),a.getLogger("lk-e2ee");const u=34028234663852886e22,h=-34028234663852886e22,p=4294967295,m=2147483647,g=-2147483648;function f(e){if("number"!=typeof e)throw new Error("invalid int 32: "+typeof e);if(!Number.isInteger(e)||e>m||e<g)throw new Error("invalid int 32: "+e)}function v(e){if("number"!=typeof e)throw new Error("invalid uint 32: "+typeof e);if(!Number.isInteger(e)||e>p||e<0)throw new Error("invalid uint 32: "+e)}function y(e){if("number"!=typeof e)throw new Error("invalid float 32: "+typeof e);if(Number.isFinite(e)&&(e>u||e<h))throw new Error("invalid float 32: "+e)}const k=Symbol("@bufbuild/protobuf/enum-type");function b(e){const t=e[k];return l(t,"missing enum type on enum object"),t}function T(e,t,i,n){e[k]=S(t,i.map((t=>({no:t.no,name:t.name,localName:e[t.no]}))))}function S(e,t,i){const n=Object.create(null),s=Object.create(null),r=[];for(const e of t){const t=E(e);r.push(t),n[e.name]=t,s[e.no]=t}return{typeName:e,values:r,findName:e=>n[e],findNumber:e=>s[e]}}function C(e,t,i){const n={};for(const e of t){const t=E(e);n[t.localName]=t.no,n[t.no]=t.localName}return T(n,e,t),n}function E(e){return"localName"in e?e:Object.assign(Object.assign({},e),{localName:e.name})}class w{equals(e){return this.getType().runtime.util.equals(this.getType(),this,e)}clone(){return this.getType().runtime.util.clone(this)}fromBinary(e,t){const i=this.getType().runtime.bin,n=i.makeReadOptions(t);return i.readMessage(this,n.readerFactory(e),e.byteLength,n),this}fromJson(e,t){const i=this.getType(),n=i.runtime.json,s=n.makeReadOptions(t);return n.readMessage(i,e,s,this),this}fromJsonString(e,t){let i;try{i=JSON.parse(e)}catch(e){throw new Error("cannot decode ".concat(this.getType().typeName," from JSON: ").concat(e instanceof Error?e.message:String(e)))}return this.fromJson(i,t)}toBinary(e){const t=this.getType().runtime.bin,i=t.makeWriteOptions(e),n=i.writerFactory();return t.writeMessage(this,n,i),n.finish()}toJson(e){const t=this.getType().runtime.json,i=t.makeWriteOptions(e);return t.writeMessage(this,i)}toJsonString(e){var t;const i=this.toJson(e);return JSON.stringify(i,null,null!==(t=null==e?void 0:e.prettySpaces)&&void 0!==t?t:0)}toJSON(){return this.toJson({emitDefaultValues:!0})}getType(){return Object.getPrototypeOf(this).constructor}}var P,R;function I(){let e=0,t=0;for(let i=0;i<28;i+=7){let n=this.buf[this.pos++];if(e|=(127&n)<<i,0==(128&n))return this.assertBounds(),[e,t]}let i=this.buf[this.pos++];if(e|=(15&i)<<28,t=(112&i)>>4,0==(128&i))return this.assertBounds(),[e,t];for(let i=3;i<=31;i+=7){let n=this.buf[this.pos++];if(t|=(127&n)<<i,0==(128&n))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function O(e,t,i){for(let n=0;n<28;n+=7){const s=e>>>n,r=!(s>>>7==0&&0==t),o=255&(r?128|s:s);if(i.push(o),!r)return}const n=e>>>28&15|(7&t)<<4,s=!(t>>3==0);if(i.push(255&(s?128|n:n)),s){for(let e=3;e<31;e+=7){const n=t>>>e,s=!(n>>>7==0),r=255&(s?128|n:n);if(i.push(r),!s)return}i.push(t>>>31&1)}}!function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(P||(P={})),function(e){e[e.BIGINT=0]="BIGINT",e[e.STRING=1]="STRING"}(R||(R={}));const D=4294967296;function x(e){const t="-"===e[0];t&&(e=e.slice(1));const i=1e6;let n=0,s=0;function r(t,r){const o=Number(e.slice(t,r));s*=i,n=n*i+o,n>=D&&(s+=n/D|0,n%=D)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),t?M(n,s):_(n,s)}function N(e,t){if(({lo:e,hi:t}=function(e,t){return{lo:e>>>0,hi:t>>>0}}(e,t)),t<=2097151)return String(D*t+e);const i=16777215&(e>>>24|t<<8),n=t>>16&65535;let s=(16777215&e)+6777216*i+6710656*n,r=i+8147497*n,o=2*n;const a=1e7;return s>=a&&(r+=Math.floor(s/a),s%=a),r>=a&&(o+=Math.floor(r/a),r%=a),o.toString()+L(r)+L(s)}function _(e,t){return{lo:0|e,hi:0|t}}function M(e,t){return t=~t,e?e=1+~e:t+=1,_(e,t)}const L=e=>{const t=String(e);return"0000000".slice(t.length)+t};function A(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let i=0;i<9;i++)t.push(127&e|128),e>>=7;t.push(1)}}function U(){let e=this.buf[this.pos++],t=127&e;if(0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,0==(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let t=5;0!=(128&e)&&t<10;t++)e=this.buf[this.pos++];if(0!=(128&e))throw new Error("invalid varint");return this.assertBounds(),t>>>0}const j=function(){const e=new DataView(new ArrayBuffer(8));if("function"==typeof BigInt&&"function"==typeof e.getBigInt64&&"function"==typeof e.getBigUint64&&"function"==typeof e.setBigInt64&&"function"==typeof e.setBigUint64&&("object"!=typeof process||"object"!=typeof process.env||"1"!==process.env.BUF_BIGINT_DISABLE)){const t=BigInt("-9223372036854775808"),i=BigInt("9223372036854775807"),n=BigInt("0"),s=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(e){const n="bigint"==typeof e?e:BigInt(e);if(n>i||n<t)throw new Error("int64 invalid: ".concat(e));return n},uParse(e){const t="bigint"==typeof e?e:BigInt(e);if(t>s||t<n)throw new Error("uint64 invalid: ".concat(e));return t},enc(t){return e.setBigInt64(0,this.parse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(t){return e.setBigInt64(0,this.uParse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(t,i)=>(e.setInt32(0,t,!0),e.setInt32(4,i,!0),e.getBigInt64(0,!0)),uDec:(t,i)=>(e.setInt32(0,t,!0),e.setInt32(4,i,!0),e.getBigUint64(0,!0))}}const t=e=>l(/^-?[0-9]+$/.test(e),"int64 invalid: ".concat(e)),i=e=>l(/^[0-9]+$/.test(e),"uint64 invalid: ".concat(e));return{zero:"0",supported:!1,parse:e=>("string"!=typeof e&&(e=e.toString()),t(e),e),uParse:e=>("string"!=typeof e&&(e=e.toString()),i(e),e),enc:e=>("string"!=typeof e&&(e=e.toString()),t(e),x(e)),uEnc:e=>("string"!=typeof e&&(e=e.toString()),i(e),x(e)),dec:(e,t)=>function(e,t){let i=_(e,t);const n=2147483648&i.hi;n&&(i=M(i.lo,i.hi));const s=N(i.lo,i.hi);return n?"-"+s:s}(e,t),uDec:(e,t)=>N(e,t)}}();var B;!function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"}(B||(B={}));class F{constructor(e){this.stack=[],this.textEncoder=null!=e?e:new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let t=0;t<this.chunks.length;t++)e+=this.chunks[t].length;let t=new Uint8Array(e),i=0;for(let e=0;e<this.chunks.length;e++)t.set(this.chunks[e],i),i+=this.chunks[e].length;return this.chunks=[],t}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),t=this.stack.pop();if(!t)throw new Error("invalid state, fork stack empty");return this.chunks=t.chunks,this.buf=t.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,t){return this.uint32((e<<3|t)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(v(e);e>127;)this.buf.push(127&e|128),e>>>=7;return this.buf.push(e),this}int32(e){return f(e),A(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let t=this.textEncoder.encode(e);return this.uint32(t.byteLength),this.raw(t)}float(e){y(e);let t=new Uint8Array(4);return new DataView(t.buffer).setFloat32(0,e,!0),this.raw(t)}double(e){let t=new Uint8Array(8);return new DataView(t.buffer).setFloat64(0,e,!0),this.raw(t)}fixed32(e){v(e);let t=new Uint8Array(4);return new DataView(t.buffer).setUint32(0,e,!0),this.raw(t)}sfixed32(e){f(e);let t=new Uint8Array(4);return new DataView(t.buffer).setInt32(0,e,!0),this.raw(t)}sint32(e){return f(e),A(e=(e<<1^e>>31)>>>0,this.buf),this}sfixed64(e){let t=new Uint8Array(8),i=new DataView(t.buffer),n=j.enc(e);return i.setInt32(0,n.lo,!0),i.setInt32(4,n.hi,!0),this.raw(t)}fixed64(e){let t=new Uint8Array(8),i=new DataView(t.buffer),n=j.uEnc(e);return i.setInt32(0,n.lo,!0),i.setInt32(4,n.hi,!0),this.raw(t)}int64(e){let t=j.enc(e);return O(t.lo,t.hi,this.buf),this}sint64(e){let t=j.enc(e),i=t.hi>>31;return O(t.lo<<1^i,(t.hi<<1|t.lo>>>31)^i,this.buf),this}uint64(e){let t=j.uEnc(e);return O(t.lo,t.hi,this.buf),this}}class J{constructor(e,t){this.varint64=I,this.uint32=U,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=null!=t?t:new TextDecoder}tag(){let e=this.uint32(),t=e>>>3,i=7&e;if(t<=0||i<0||i>5)throw new Error("illegal tag: field no "+t+" wire type "+i);return[t,i]}skip(e){let t=this.pos;switch(e){case B.Varint:for(;128&this.buf[this.pos++];);break;case B.Bit64:this.pos+=4;case B.Bit32:this.pos+=4;break;case B.LengthDelimited:let t=this.uint32();this.pos+=t;break;case B.StartGroup:let i;for(;(i=this.tag()[1])!==B.EndGroup;)this.skip(i);break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(t,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return j.dec(...this.varint64())}uint64(){return j.uDec(...this.varint64())}sint64(){let[e,t]=this.varint64(),i=-(1&e);return e=(e>>>1|(1&t)<<31)^i,t=t>>>1^i,j.dec(e,t)}bool(){let[e,t]=this.varint64();return 0!==e||0!==t}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return j.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return j.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),t=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(t,t+e)}string(){return this.textDecoder.decode(this.bytes())}}function V(e,t){return t instanceof w||!e.fieldWrapper?t:e.fieldWrapper.wrapField(t)}function q(e,t,i){if(t===i)return!0;if(e==P.BYTES){if(!(t instanceof Uint8Array&&i instanceof Uint8Array))return!1;if(t.length!==i.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==i[e])return!1;return!0}switch(e){case P.UINT64:case P.FIXED64:case P.INT64:case P.SFIXED64:case P.SINT64:return t==i}return!1}function K(e,t){switch(e){case P.BOOL:return!1;case P.UINT64:case P.FIXED64:case P.INT64:case P.SFIXED64:case P.SINT64:return 0==t?j.zero:"0";case P.DOUBLE:case P.FLOAT:return 0;case P.BYTES:return new Uint8Array(0);case P.STRING:return"";default:return 0}}function W(e,t){const i=void 0===t;let n=B.Varint,s=0===t;switch(e){case P.STRING:s=i||!t.length,n=B.LengthDelimited;break;case P.BOOL:s=!1===t;break;case P.DOUBLE:n=B.Bit64;break;case P.FLOAT:n=B.Bit32;break;case P.INT64:case P.UINT64:s=i||0==t;break;case P.FIXED64:s=i||0==t,n=B.Bit64;break;case P.BYTES:s=i||!t.byteLength,n=B.LengthDelimited;break;case P.FIXED32:case P.SFIXED32:n=B.Bit32;break;case P.SFIXED64:s=i||0==t,n=B.Bit64;break;case P.SINT64:s=i||0==t}return[n,P[e].toLowerCase(),i||s]}P.DOUBLE,P.FLOAT,P.INT64,P.UINT64,P.INT32,P.UINT32,P.BOOL,P.STRING,P.BYTES;const G=Symbol("@bufbuild/protobuf/unknown-fields"),H={readUnknownFields:!0,readerFactory:e=>new J(e)},z={writeUnknownFields:!0,writerFactory:()=>new F};function Q(e){return e?Object.assign(Object.assign({},H),e):H}function Y(e){return e?Object.assign(Object.assign({},z),e):z}function X(e,t,i){return t.getType().runtime.bin.readMessage(t,e,e.uint32(),i),t}function Z(e,t,i){const n=t.uint32(),s=t.pos+n;let r,o;for(;t.pos<s;){let[n]=t.tag();switch(n){case 1:r=ee(t,e.K);break;case 2:switch(e.V.kind){case"scalar":o=ee(t,e.V.T);break;case"enum":o=t.int32();break;case"message":o=X(t,new e.V.T,i)}}}if(void 0===r){let t=K(e.K,R.BIGINT);r=e.K==P.BOOL?t.toString():t}if("string"!=typeof r&&"number"!=typeof r&&(r=r.toString()),void 0===o)switch(e.V.kind){case"scalar":o=K(e.V.T,R.BIGINT);break;case"enum":o=0;break;case"message":o=new e.V.T}return[r,o]}function $(e,t){const i=ee(e,t);return"bigint"==typeof i?i.toString():i}function ee(e,t){switch(t){case P.STRING:return e.string();case P.BOOL:return e.bool();case P.DOUBLE:return e.double();case P.FLOAT:return e.float();case P.INT32:return e.int32();case P.INT64:return e.int64();case P.UINT64:return e.uint64();case P.FIXED64:return e.fixed64();case P.BYTES:return e.bytes();case P.FIXED32:return e.fixed32();case P.SFIXED32:return e.sfixed32();case P.SFIXED64:return e.sfixed64();case P.SINT64:return e.sint64();case P.UINT32:return e.uint32();case P.SINT32:return e.sint32()}}function te(e,t,i,n,s){e.tag(i.no,B.LengthDelimited),e.fork();let r=n;switch(i.K){case P.INT32:case P.FIXED32:case P.UINT32:case P.SFIXED32:case P.SINT32:r=Number.parseInt(n);break;case P.BOOL:l("true"==n||"false"==n),r="true"==n}switch(ne(e,i.K,1,r,!0),i.V.kind){case"scalar":ne(e,i.V.T,2,s,!0);break;case"enum":ne(e,P.INT32,2,s,!0);break;case"message":ie(e,t,i.V.T,2,s)}e.join()}function ie(e,t,i,n,s){if(void 0!==s){const r=V(i,s);e.tag(n,B.LengthDelimited).bytes(r.toBinary(t))}}function ne(e,t,i,n,s){let[r,o,a]=W(t,n);a&&!s||e.tag(i,r)[o](n)}function se(e,t,i,n){if(!n.length)return;e.tag(i,B.LengthDelimited).fork();let[,s]=W(t);for(let t=0;t<n.length;t++)e[s](n[t]);e.join()}let re="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),oe=[];for(let e=0;e<re.length;e++)oe[re[e].charCodeAt(0)]=e;oe["-".charCodeAt(0)]=re.indexOf("+"),oe["_".charCodeAt(0)]=re.indexOf("/");const ae={dec(e){let t=3*e.length/4;"="==e[e.length-2]?t-=2:"="==e[e.length-1]&&(t-=1);let i,n=new Uint8Array(t),s=0,r=0,o=0;for(let t=0;t<e.length;t++){if(i=oe[e.charCodeAt(t)],void 0===i)switch(e[t]){case"=":r=0;case"\n":case"\r":case"\t":case" ":continue;default:throw Error("invalid base64 string.")}switch(r){case 0:o=i,r=1;break;case 1:n[s++]=o<<2|(48&i)>>4,o=i,r=2;break;case 2:n[s++]=(15&o)<<4|(60&i)>>2,o=i,r=3;break;case 3:n[s++]=(3&o)<<6|i,r=0}}if(1==r)throw Error("invalid base64 string.");return n.subarray(0,s)},enc(e){let t,i="",n=0,s=0;for(let r=0;r<e.length;r++)switch(t=e[r],n){case 0:i+=re[t>>2],s=(3&t)<<4,n=1;break;case 1:i+=re[s|t>>4],s=(15&t)<<2,n=2;break;case 2:i+=re[s|t>>6],i+=re[63&t],n=0}return n&&(i+=re[s],i+="=",1==n&&(i+="=")),i}},ce={ignoreUnknownFields:!1},de={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0};function le(e){return e?Object.assign(Object.assign({},ce),e):ce}function ue(e){return e?Object.assign(Object.assign({},de),e):de}function he(e){if(null===e)return"null";switch(typeof e){case"object":return Array.isArray(e)?"array":"object";case"string":return e.length>100?"string":'"'.concat(e.split('"').join('\\"'),'"');default:return String(e)}}function pe(e,t,i){switch(e){case P.DOUBLE:case P.FLOAT:if(null===t)return 0;if("NaN"===t)return Number.NaN;if("Infinity"===t)return Number.POSITIVE_INFINITY;if("-Infinity"===t)return Number.NEGATIVE_INFINITY;if(""===t)break;if("string"==typeof t&&t.trim().length!==t.length)break;if("string"!=typeof t&&"number"!=typeof t)break;const n=Number(t);if(Number.isNaN(n))break;if(!Number.isFinite(n))break;return e==P.FLOAT&&y(n),n;case P.INT32:case P.FIXED32:case P.SFIXED32:case P.SINT32:case P.UINT32:if(null===t)return 0;let s;if("number"==typeof t?s=t:"string"==typeof t&&t.length>0&&t.trim().length===t.length&&(s=Number(t)),void 0===s)break;return e==P.UINT32?v(s):f(s),s;case P.INT64:case P.SFIXED64:case P.SINT64:if(null===t)return j.zero;if("number"!=typeof t&&"string"!=typeof t)break;const r=j.parse(t);return i?r.toString():r;case P.FIXED64:case P.UINT64:if(null===t)return j.zero;if("number"!=typeof t&&"string"!=typeof t)break;const o=j.uParse(t);return i?o.toString():o;case P.BOOL:if(null===t)return!1;if("boolean"!=typeof t)break;return t;case P.STRING:if(null===t)return"";if("string"!=typeof t)break;try{encodeURIComponent(t)}catch(e){throw new Error("invalid UTF8")}return t;case P.BYTES:if(null===t||""===t)return new Uint8Array(0);if("string"!=typeof t)break;return ae.dec(t)}throw new Error}function me(e,t,i){if(null===t)return 0;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const n=e.findName(t);if(n||i)return null==n?void 0:n.no}throw new Error("cannot decode enum ".concat(e.typeName," from JSON: ").concat(he(t)))}function ge(e,t,i,n){var s;if(void 0===t)return t;if(0===t&&!i)return;if(n)return t;if("google.protobuf.NullValue"==e.typeName)return null;const r=e.findNumber(t);return null!==(s=null==r?void 0:r.name)&&void 0!==s?s:t}function fe(e,t,i){if(void 0!==t)switch(e){case P.INT32:case P.SFIXED32:case P.SINT32:case P.FIXED32:case P.UINT32:return l("number"==typeof t),0!=t||i?t:void 0;case P.FLOAT:case P.DOUBLE:return l("number"==typeof t),Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":0!==t||i?t:void 0;case P.STRING:return l("string"==typeof t),t.length>0||i?t:void 0;case P.BOOL:return l("boolean"==typeof t),t||i?t:void 0;case P.UINT64:case P.FIXED64:case P.INT64:case P.SFIXED64:case P.SINT64:return l("bigint"==typeof t||"string"==typeof t||"number"==typeof t),i||0!=t?t.toString(10):void 0;case P.BYTES:return l(t instanceof Uint8Array),i||t.byteLength>0?ae.enc(t):void 0}}function ve(e){if(void 0===e)return e;if(e instanceof w)return e.clone();if(e instanceof Uint8Array){const t=new Uint8Array(e.byteLength);return t.set(e),t}return e}function ye(e){return e instanceof Uint8Array?e:new Uint8Array(e)}class ke{constructor(e,t){this._fields=e,this._normalizer=t}findJsonName(e){if(!this.jsonNames){const e={};for(const t of this.list())e[t.jsonName]=e[t.name]=t;this.jsonNames=e}return this.jsonNames[e]}find(e){if(!this.numbers){const e={};for(const t of this.list())e[t.no]=t;this.numbers=e}return this.numbers[e]}list(){return this.all||(this.all=this._normalizer(this._fields)),this.all}byNumber(){return this.numbersAsc||(this.numbersAsc=this.list().concat().sort(((e,t)=>e.no-t.no))),this.numbersAsc}byMember(){if(!this.members){this.members=[];const e=this.members;let t;for(const i of this.list())i.oneof?i.oneof!==t&&(t=i.oneof,e.push(t)):e.push(i)}return this.members}}function be(e,t){const i=Se(e);return t?i:Re(Pe(i))}const Te=Se;function Se(e){let t=!1;const i=[];for(let n=0;n<e.length;n++){let s=e.charAt(n);switch(s){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":i.push(s),t=!1;break;default:t&&(t=!1,s=s.toUpperCase()),i.push(s)}}return i.join("")}const Ce=new Set(["constructor","toString","toJSON","valueOf"]),Ee=new Set(["getType","clone","equals","fromBinary","fromJson","fromJsonString","toBinary","toJson","toJsonString","toObject"]),we=e=>"".concat(e,"$"),Pe=e=>Ee.has(e)?we(e):e,Re=e=>Ce.has(e)?we(e):e;class Ie{constructor(e){this.kind="oneof",this.repeated=!1,this.packed=!1,this.opt=!1,this.default=void 0,this.fields=[],this.name=e,this.localName=be(e,!1)}addField(e){l(e.oneof===this,"field ".concat(e.name," not one of ").concat(this.name)),this.fields.push(e)}findField(e){if(!this._lookup){this._lookup=Object.create(null);for(let e=0;e<this.fields.length;e++)this._lookup[this.fields[e].localName]=this.fields[e]}return this._lookup[e]}}const Oe=(De="proto3",xe=function(e){const t=e(ge,fe);return{makeReadOptions:le,makeWriteOptions:ue,readMessage(e,t,i,n){if(null==t||Array.isArray(t)||"object"!=typeof t)throw new Error("cannot decode message ".concat(e.typeName," from JSON: ").concat(this.debug(t)));n=null!=n?n:new e;const s={};for(const[r,o]of Object.entries(t)){const t=e.fields.findJsonName(r);if(!t){if(!i.ignoreUnknownFields)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: key "').concat(r,'" is unknown'));continue}let a=t.localName,c=n;if(t.oneof){if(null===o&&"scalar"==t.kind)continue;const i=s[t.oneof.localName];if(i)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: multiple keys for oneof "').concat(t.oneof.name,'" present: "').concat(i,'", "').concat(r,'"'));s[t.oneof.localName]=r,c=c[t.oneof.localName]={case:a},a="value"}if(t.repeated){if(null===o)continue;if(!Array.isArray(o))throw new Error("cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(o)));const n=c[a];for(const s of o){if(null===s)throw new Error("cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(s)));let r;switch(t.kind){case"message":r=t.T.fromJson(s,i);break;case"enum":if(r=me(t.T,s,i.ignoreUnknownFields),void 0===r)continue;break;case"scalar":try{r=pe(t.T,s,t.L)}catch(i){let n="cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(s));throw i instanceof Error&&i.message.length>0&&(n+=": ".concat(i.message)),new Error(n)}}n.push(r)}}else if("map"==t.kind){if(null===o)continue;if(Array.isArray(o)||"object"!=typeof o)throw new Error("cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(o)));const n=c[a];for(const[s,r]of Object.entries(o)){if(null===r)throw new Error("cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: map value null"));let a;switch(t.V.kind){case"message":a=t.V.T.fromJson(r,i);break;case"enum":if(a=me(t.V.T,r,i.ignoreUnknownFields),void 0===a)continue;break;case"scalar":try{a=pe(t.V.T,r,R.BIGINT)}catch(i){let n="cannot decode map value for field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(o));throw i instanceof Error&&i.message.length>0&&(n+=": ".concat(i.message)),new Error(n)}}try{n[pe(t.K,t.K==P.BOOL?"true"==s||"false"!=s&&s:s,R.BIGINT).toString()]=a}catch(i){let n="cannot decode map key for field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(o));throw i instanceof Error&&i.message.length>0&&(n+=": ".concat(i.message)),new Error(n)}}}else switch(t.kind){case"message":const n=t.T;if(null===o&&"google.protobuf.Value"!=n.typeName){if(t.oneof)throw new Error("cannot decode field ".concat(e.typeName,".").concat(t.name,' from JSON: null is invalid for oneof field "').concat(r,'"'));continue}c[a]instanceof w?c[a].fromJson(o,i):(c[a]=n.fromJson(o,i),n.fieldWrapper&&!t.oneof&&(c[a]=n.fieldWrapper.unwrapField(c[a])));break;case"enum":const s=me(t.T,o,i.ignoreUnknownFields);void 0!==s&&(c[a]=s);break;case"scalar":try{c[a]=pe(t.T,o,t.L)}catch(i){let n="cannot decode field ".concat(e.typeName,".").concat(t.name," from JSON: ").concat(this.debug(o));throw i instanceof Error&&i.message.length>0&&(n+=": ".concat(i.message)),new Error(n)}}}return n},writeMessage(e,i){const n=e.getType(),s={};let r;try{for(const o of n.fields.byMember()){let n;if("oneof"==o.kind){const s=e[o.localName];if(void 0===s.value)continue;if(r=o.findField(s.case),!r)throw"oneof case not found: "+s.case;n=t(r,s.value,i)}else r=o,n=t(r,e[r.localName],i);void 0!==n&&(s[i.useProtoFieldName?r.name:r.jsonName]=n)}}catch(e){const t=r?"cannot encode field ".concat(n.typeName,".").concat(r.name," to JSON"):"cannot encode message ".concat(n.typeName," to JSON"),i=e instanceof Error?e.message:String(e);throw new Error(t+(i.length>0?": ".concat(i):""))}return s},readScalar:pe,writeScalar:fe,debug:he}}(((e,t)=>function(i,n,s){if("map"==i.kind){const r={};switch(i.V.kind){case"scalar":for(const[e,s]of Object.entries(n)){const n=t(i.V.T,s,!0);l(void 0!==n),r[e.toString()]=n}break;case"message":for(const[e,t]of Object.entries(n))r[e.toString()]=t.toJson(s);break;case"enum":const o=i.V.T;for(const[t,i]of Object.entries(n)){l(void 0===i||"number"==typeof i);const n=e(o,i,!0,s.enumAsInteger);l(void 0!==n),r[t.toString()]=n}}return s.emitDefaultValues||Object.keys(r).length>0?r:void 0}if(i.repeated){const r=[];switch(i.kind){case"scalar":for(let e=0;e<n.length;e++)r.push(t(i.T,n[e],!0));break;case"enum":for(let t=0;t<n.length;t++)r.push(e(i.T,n[t],!0,s.enumAsInteger));break;case"message":for(let e=0;e<n.length;e++)r.push(V(i.T,n[e]).toJson(s))}return s.emitDefaultValues||r.length>0?r:void 0}switch(i.kind){case"scalar":return t(i.T,n,!!i.oneof||i.opt||s.emitDefaultValues);case"enum":return e(i.T,n,!!i.oneof||i.opt||s.emitDefaultValues,s.enumAsInteger);case"message":return void 0!==n?V(i.T,n).toJson(s):void 0}})),Ne=Object.assign(Object.assign({},{makeReadOptions:Q,makeWriteOptions:Y,listUnknownFields(e){var t;return null!==(t=e[G])&&void 0!==t?t:[]},discardUnknownFields(e){delete e[G]},writeUnknownFields(e,t){const i=e[G];if(i)for(const e of i)t.tag(e.no,e.wireType).raw(e.data)},onUnknownField(e,t,i,n){const s=e;Array.isArray(s[G])||(s[G]=[]),s[G].push({no:t,wireType:i,data:n})},readMessage(e,t,i,n){const s=e.getType(),r=void 0===i?t.len:t.pos+i;for(;t.pos<r;){const[i,r]=t.tag(),o=s.fields.find(i);if(!o){const s=t.skip(r);n.readUnknownFields&&this.onUnknownField(e,i,r,s);continue}let a=e,c=o.repeated,d=o.localName;switch(o.oneof&&(a=a[o.oneof.localName],a.case!=d&&delete a.value,a.case=d,d="value"),o.kind){case"scalar":case"enum":const e="enum"==o.kind?P.INT32:o.T;let i=ee;if("scalar"==o.kind&&o.L>0&&(i=$),c){let n=a[d];if(r==B.LengthDelimited&&e!=P.STRING&&e!=P.BYTES){let s=t.uint32()+t.pos;for(;t.pos<s;)n.push(i(t,e))}else n.push(i(t,e))}else a[d]=i(t,e);break;case"message":const s=o.T;c?a[d].push(X(t,new s,n)):a[d]instanceof w?X(t,a[d],n):(a[d]=X(t,new s,n),!s.fieldWrapper||o.oneof||o.repeated||(a[d]=s.fieldWrapper.unwrapField(a[d])));break;case"map":let[l,u]=Z(o,t,n);a[d][l]=u}}}}),{writeMessage(e,t,i){const n=e.getType();for(const s of n.fields.byNumber()){let n,r=s.repeated,o=s.localName;if(s.oneof){const t=e[s.oneof.localName];if(t.case!==o)continue;n=t.value}else n=e[o];switch(s.kind){case"scalar":case"enum":let e="enum"==s.kind?P.INT32:s.T;if(r)if(s.packed)se(t,e,s.no,n);else for(const i of n)ne(t,e,s.no,i,!0);else void 0!==n&&ne(t,e,s.no,n,!!s.oneof||s.opt);break;case"message":if(r)for(const e of n)ie(t,i,s.T,s.no,e);else ie(t,i,s.T,s.no,n);break;case"map":for(const[e,r]of Object.entries(n))te(t,i,s,e,r)}}return i.writeUnknownFields&&this.writeUnknownFields(e,t),t}}),_e=Object.assign(Object.assign({},{setEnumType:T,initPartial(e,t){if(void 0===e)return;const i=t.getType();for(const n of i.fields.byMember()){const i=n.localName,s=t,r=e;if(void 0!==r[i])switch(n.kind){case"oneof":const e=r[i].case;if(void 0===e)continue;const t=n.findField(e);let o=r[i].value;!t||"message"!=t.kind||o instanceof t.T?t&&"scalar"===t.kind&&t.T===P.BYTES&&(o=ye(o)):o=new t.T(o),s[i]={case:e,value:o};break;case"scalar":case"enum":let a=r[i];n.T===P.BYTES&&(a=n.repeated?a.map(ye):ye(a)),s[i]=a;break;case"map":switch(n.V.kind){case"scalar":case"enum":if(n.V.T===P.BYTES)for(const[e,t]of Object.entries(r[i]))s[i][e]=ye(t);else Object.assign(s[i],r[i]);break;case"message":const e=n.V.T;for(const t of Object.keys(r[i])){let n=r[i][t];e.fieldWrapper||(n=new e(n)),s[i][t]=n}}break;case"message":const c=n.T;if(n.repeated)s[i]=r[i].map((e=>e instanceof c?e:new c(e)));else if(void 0!==r[i]){const e=r[i];c.fieldWrapper?"google.protobuf.BytesValue"===c.typeName?s[i]=ye(e):s[i]=e:s[i]=e instanceof c?e:new c(e)}}}},equals:(e,t,i)=>t===i||!(!t||!i)&&e.fields.byMember().every((e=>{const n=t[e.localName],s=i[e.localName];if(e.repeated){if(n.length!==s.length)return!1;switch(e.kind){case"message":return n.every(((t,i)=>e.T.equals(t,s[i])));case"scalar":return n.every(((t,i)=>q(e.T,t,s[i])));case"enum":return n.every(((e,t)=>q(P.INT32,e,s[t])))}throw new Error("repeated cannot contain ".concat(e.kind))}switch(e.kind){case"message":return e.T.equals(n,s);case"enum":return q(P.INT32,n,s);case"scalar":return q(e.T,n,s);case"oneof":if(n.case!==s.case)return!1;const t=e.findField(n.case);if(void 0===t)return!0;switch(t.kind){case"message":return t.T.equals(n.value,s.value);case"enum":return q(P.INT32,n.value,s.value);case"scalar":return q(t.T,n.value,s.value)}throw new Error("oneof cannot contain ".concat(t.kind));case"map":const i=Object.keys(n).concat(Object.keys(s));switch(e.V.kind){case"message":const t=e.V.T;return i.every((e=>t.equals(n[e],s[e])));case"enum":return i.every((e=>q(P.INT32,n[e],s[e])));case"scalar":const r=e.V.T;return i.every((e=>q(r,n[e],s[e])))}}})),clone(e){const t=e.getType(),i=new t,n=i;for(const i of t.fields.byMember()){const t=e[i.localName];let s;if(i.repeated)s=t.map(ve);else if("map"==i.kind){s=n[i.localName];for(const[e,i]of Object.entries(t))s[e]=ve(i)}else s="oneof"==i.kind?i.findField(t.case)?{case:t.case,value:ve(t.value)}:{case:void 0}:ve(t);n[i.localName]=s}return i}}),{newFieldList:e=>new ke(e,We),initFields(e){for(const t of e.getType().fields.byMember()){if(t.opt)continue;const i=t.localName,n=e;if(t.repeated)n[i]=[];else switch(t.kind){case"oneof":n[i]={case:void 0};break;case"enum":n[i]=0;break;case"map":n[i]={};break;case"scalar":n[i]=K(t.T,t.L)}}}}),{syntax:De,json:xe,bin:Ne,util:_e,makeMessageType(e,t,i){return function(e,t,i,n){var s;const r=null!==(s=null==n?void 0:n.localName)&&void 0!==s?s:t.substring(t.lastIndexOf(".")+1),o={[r]:function(t){e.util.initFields(this),e.util.initPartial(t,this)}}[r];return Object.setPrototypeOf(o.prototype,new w),Object.assign(o,{runtime:e,typeName:t,fields:e.util.newFieldList(i),fromBinary:(e,t)=>(new o).fromBinary(e,t),fromJson:(e,t)=>(new o).fromJson(e,t),fromJsonString:(e,t)=>(new o).fromJsonString(e,t),equals:(t,i)=>e.util.equals(o,t,i)}),o}(this,e,t,i)},makeEnum:C,makeEnumType:S,getEnumType:b});var De,xe,Ne,_e,Me,Le,Ae,Ue,je,Be,Fe,Je,Ve,qe,Ke;function We(e){var t,i,n,s;const r=[];let o;for(const a of"function"==typeof e?e():e){const e=a;if(e.localName=be(a.name,void 0!==a.oneof),e.jsonName=null!==(t=a.jsonName)&&void 0!==t?t:Te(a.name),e.repeated=null!==(i=a.repeated)&&void 0!==i&&i,"scalar"==a.kind&&(e.L=null!==(n=a.L)&&void 0!==n?n:R.BIGINT),e.packed=null!==(s=a.packed)&&void 0!==s?s:"enum"==a.kind||"scalar"==a.kind&&a.T!=P.BYTES&&a.T!=P.STRING,void 0!==a.oneof){const t="string"==typeof a.oneof?a.oneof:a.oneof.name;o&&o.name==t||(o=new Ie(t)),e.oneof=o,o.addField(e)}r.push(e)}return r}class Ge extends w{constructor(e){super(),this.seconds=j.zero,this.nanos=0,Oe.util.initPartial(e,this)}fromJson(e,t){if("string"!=typeof e)throw new Error("cannot decode google.protobuf.Timestamp from JSON: ".concat(Oe.json.debug(e)));const i=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!i)throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");const n=Date.parse(i[1]+"-"+i[2]+"-"+i[3]+"T"+i[4]+":"+i[5]+":"+i[6]+(i[8]?i[8]:"Z"));if(Number.isNaN(n))throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");if(n<Date.parse("0001-01-01T00:00:00Z")||n>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");return this.seconds=j.parse(n/1e3),this.nanos=0,i[7]&&(this.nanos=parseInt("1"+i[7]+"0".repeat(9-i[7].length))-1e9),this}toJson(e){const t=1e3*Number(this.seconds);if(t<Date.parse("0001-01-01T00:00:00Z")||t>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");if(this.nanos<0)throw new Error("cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative");let i="Z";if(this.nanos>0){const e=(this.nanos+1e9).toString().substring(1);i="000000"===e.substring(3)?"."+e.substring(0,3)+"Z":"000"===e.substring(6)?"."+e.substring(0,6)+"Z":"."+e+"Z"}return new Date(t).toISOString().replace(".000Z",i)}toDate(){return new Date(1e3*Number(this.seconds)+Math.ceil(this.nanos/1e6))}static now(){return Ge.fromDate(new Date)}static fromDate(e){const t=e.getTime();return new Ge({seconds:j.parse(Math.floor(t/1e3)),nanos:t%1e3*1e6})}static fromBinary(e,t){return(new Ge).fromBinary(e,t)}static fromJson(e,t){return(new Ge).fromJson(e,t)}static fromJsonString(e,t){return(new Ge).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Ge,e,t)}}Ge.runtime=Oe,Ge.typeName="google.protobuf.Timestamp",Ge.fields=Oe.util.newFieldList((()=>[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}])),function(e){e[e.DEFAULT_AC=0]="DEFAULT_AC",e[e.OPUS=1]="OPUS",e[e.AAC=2]="AAC"}(Me||(Me={})),Oe.util.setEnumType(Me,"livekit.AudioCodec",[{no:0,name:"DEFAULT_AC"},{no:1,name:"OPUS"},{no:2,name:"AAC"}]),function(e){e[e.DEFAULT_VC=0]="DEFAULT_VC",e[e.H264_BASELINE=1]="H264_BASELINE",e[e.H264_MAIN=2]="H264_MAIN",e[e.H264_HIGH=3]="H264_HIGH",e[e.VP8=4]="VP8"}(Le||(Le={})),Oe.util.setEnumType(Le,"livekit.VideoCodec",[{no:0,name:"DEFAULT_VC"},{no:1,name:"H264_BASELINE"},{no:2,name:"H264_MAIN"},{no:3,name:"H264_HIGH"},{no:4,name:"VP8"}]),function(e){e[e.IC_DEFAULT=0]="IC_DEFAULT",e[e.IC_JPEG=1]="IC_JPEG"}(Ae||(Ae={})),Oe.util.setEnumType(Ae,"livekit.ImageCodec",[{no:0,name:"IC_DEFAULT"},{no:1,name:"IC_JPEG"}]),function(e){e[e.AUDIO=0]="AUDIO",e[e.VIDEO=1]="VIDEO",e[e.DATA=2]="DATA"}(Ue||(Ue={})),Oe.util.setEnumType(Ue,"livekit.TrackType",[{no:0,name:"AUDIO"},{no:1,name:"VIDEO"},{no:2,name:"DATA"}]),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.CAMERA=1]="CAMERA",e[e.MICROPHONE=2]="MICROPHONE",e[e.SCREEN_SHARE=3]="SCREEN_SHARE",e[e.SCREEN_SHARE_AUDIO=4]="SCREEN_SHARE_AUDIO"}(je||(je={})),Oe.util.setEnumType(je,"livekit.TrackSource",[{no:0,name:"UNKNOWN"},{no:1,name:"CAMERA"},{no:2,name:"MICROPHONE"},{no:3,name:"SCREEN_SHARE"},{no:4,name:"SCREEN_SHARE_AUDIO"}]),e.VideoQuality=void 0,(Be=e.VideoQuality||(e.VideoQuality={}))[Be.LOW=0]="LOW",Be[Be.MEDIUM=1]="MEDIUM",Be[Be.HIGH=2]="HIGH",Be[Be.OFF=3]="OFF",Oe.util.setEnumType(e.VideoQuality,"livekit.VideoQuality",[{no:0,name:"LOW"},{no:1,name:"MEDIUM"},{no:2,name:"HIGH"},{no:3,name:"OFF"}]),function(e){e[e.POOR=0]="POOR",e[e.GOOD=1]="GOOD",e[e.EXCELLENT=2]="EXCELLENT",e[e.LOST=3]="LOST"}(Fe||(Fe={})),Oe.util.setEnumType(Fe,"livekit.ConnectionQuality",[{no:0,name:"POOR"},{no:1,name:"GOOD"},{no:2,name:"EXCELLENT"},{no:3,name:"LOST"}]),function(e){e[e.UNSET=0]="UNSET",e[e.DISABLED=1]="DISABLED",e[e.ENABLED=2]="ENABLED"}(Je||(Je={})),Oe.util.setEnumType(Je,"livekit.ClientConfigSetting",[{no:0,name:"UNSET"},{no:1,name:"DISABLED"},{no:2,name:"ENABLED"}]),e.DisconnectReason=void 0,(Ve=e.DisconnectReason||(e.DisconnectReason={}))[Ve.UNKNOWN_REASON=0]="UNKNOWN_REASON",Ve[Ve.CLIENT_INITIATED=1]="CLIENT_INITIATED",Ve[Ve.DUPLICATE_IDENTITY=2]="DUPLICATE_IDENTITY",Ve[Ve.SERVER_SHUTDOWN=3]="SERVER_SHUTDOWN",Ve[Ve.PARTICIPANT_REMOVED=4]="PARTICIPANT_REMOVED",Ve[Ve.ROOM_DELETED=5]="ROOM_DELETED",Ve[Ve.STATE_MISMATCH=6]="STATE_MISMATCH",Ve[Ve.JOIN_FAILURE=7]="JOIN_FAILURE",Oe.util.setEnumType(e.DisconnectReason,"livekit.DisconnectReason",[{no:0,name:"UNKNOWN_REASON"},{no:1,name:"CLIENT_INITIATED"},{no:2,name:"DUPLICATE_IDENTITY"},{no:3,name:"SERVER_SHUTDOWN"},{no:4,name:"PARTICIPANT_REMOVED"},{no:5,name:"ROOM_DELETED"},{no:6,name:"STATE_MISMATCH"},{no:7,name:"JOIN_FAILURE"}]),function(e){e[e.RR_UNKNOWN=0]="RR_UNKNOWN",e[e.RR_SIGNAL_DISCONNECTED=1]="RR_SIGNAL_DISCONNECTED",e[e.RR_PUBLISHER_FAILED=2]="RR_PUBLISHER_FAILED",e[e.RR_SUBSCRIBER_FAILED=3]="RR_SUBSCRIBER_FAILED",e[e.RR_SWITCH_CANDIDATE=4]="RR_SWITCH_CANDIDATE"}(qe||(qe={})),Oe.util.setEnumType(qe,"livekit.ReconnectReason",[{no:0,name:"RR_UNKNOWN"},{no:1,name:"RR_SIGNAL_DISCONNECTED"},{no:2,name:"RR_PUBLISHER_FAILED"},{no:3,name:"RR_SUBSCRIBER_FAILED"},{no:4,name:"RR_SWITCH_CANDIDATE"}]),function(e){e[e.SE_UNKNOWN=0]="SE_UNKNOWN",e[e.SE_CODEC_UNSUPPORTED=1]="SE_CODEC_UNSUPPORTED",e[e.SE_TRACK_NOTFOUND=2]="SE_TRACK_NOTFOUND"}(Ke||(Ke={})),Oe.util.setEnumType(Ke,"livekit.SubscriptionError",[{no:0,name:"SE_UNKNOWN"},{no:1,name:"SE_CODEC_UNSUPPORTED"},{no:2,name:"SE_TRACK_NOTFOUND"}]);let He=class e extends w{constructor(e){super(),this.sid="",this.name="",this.emptyTimeout=0,this.maxParticipants=0,this.creationTime=j.zero,this.turnPassword="",this.enabledCodecs=[],this.metadata="",this.numParticipants=0,this.numPublishers=0,this.activeRecording=!1,Oe.util.initPartial(e,this)}static fromBinary(t,i){return(new e).fromBinary(t,i)}static fromJson(t,i){return(new e).fromJson(t,i)}static fromJsonString(t,i){return(new e).fromJsonString(t,i)}static equals(t,i){return Oe.util.equals(e,t,i)}};He.runtime=Oe,He.typeName="livekit.Room",He.fields=Oe.util.newFieldList((()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"empty_timeout",kind:"scalar",T:13},{no:4,name:"max_participants",kind:"scalar",T:13},{no:5,name:"creation_time",kind:"scalar",T:3},{no:6,name:"turn_password",kind:"scalar",T:9},{no:7,name:"enabled_codecs",kind:"message",T:ze,repeated:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"num_participants",kind:"scalar",T:13},{no:11,name:"num_publishers",kind:"scalar",T:13},{no:10,name:"active_recording",kind:"scalar",T:8}]));class ze extends w{constructor(e){super(),this.mime="",this.fmtpLine="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ze).fromBinary(e,t)}static fromJson(e,t){return(new ze).fromJson(e,t)}static fromJsonString(e,t){return(new ze).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ze,e,t)}}ze.runtime=Oe,ze.typeName="livekit.Codec",ze.fields=Oe.util.newFieldList((()=>[{no:1,name:"mime",kind:"scalar",T:9},{no:2,name:"fmtp_line",kind:"scalar",T:9}]));class Qe extends w{constructor(e){super(),this.enabled=!1,this.min=0,this.max=0,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Qe).fromBinary(e,t)}static fromJson(e,t){return(new Qe).fromJson(e,t)}static fromJsonString(e,t){return(new Qe).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Qe,e,t)}}Qe.runtime=Oe,Qe.typeName="livekit.PlayoutDelay",Qe.fields=Oe.util.newFieldList((()=>[{no:1,name:"enabled",kind:"scalar",T:8},{no:2,name:"min",kind:"scalar",T:13},{no:3,name:"max",kind:"scalar",T:13}]));class Ye extends w{constructor(e){super(),this.canSubscribe=!1,this.canPublish=!1,this.canPublishData=!1,this.canPublishSources=[],this.hidden=!1,this.recorder=!1,this.canUpdateMetadata=!1,this.agent=!1,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Ye).fromBinary(e,t)}static fromJson(e,t){return(new Ye).fromJson(e,t)}static fromJsonString(e,t){return(new Ye).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Ye,e,t)}}Ye.runtime=Oe,Ye.typeName="livekit.ParticipantPermission",Ye.fields=Oe.util.newFieldList((()=>[{no:1,name:"can_subscribe",kind:"scalar",T:8},{no:2,name:"can_publish",kind:"scalar",T:8},{no:3,name:"can_publish_data",kind:"scalar",T:8},{no:9,name:"can_publish_sources",kind:"enum",T:Oe.getEnumType(je),repeated:!0},{no:7,name:"hidden",kind:"scalar",T:8},{no:8,name:"recorder",kind:"scalar",T:8},{no:10,name:"can_update_metadata",kind:"scalar",T:8},{no:11,name:"agent",kind:"scalar",T:8}]));class Xe extends w{constructor(e){super(),this.sid="",this.identity="",this.state=Ze.JOINING,this.tracks=[],this.metadata="",this.joinedAt=j.zero,this.name="",this.version=0,this.region="",this.isPublisher=!1,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Xe).fromBinary(e,t)}static fromJson(e,t){return(new Xe).fromJson(e,t)}static fromJsonString(e,t){return(new Xe).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Xe,e,t)}}var Ze,$e,et,tt,it;Xe.runtime=Oe,Xe.typeName="livekit.ParticipantInfo",Xe.fields=Oe.util.newFieldList((()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"identity",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:Oe.getEnumType(Ze)},{no:4,name:"tracks",kind:"message",T:rt,repeated:!0},{no:5,name:"metadata",kind:"scalar",T:9},{no:6,name:"joined_at",kind:"scalar",T:3},{no:9,name:"name",kind:"scalar",T:9},{no:10,name:"version",kind:"scalar",T:13},{no:11,name:"permission",kind:"message",T:Ye},{no:12,name:"region",kind:"scalar",T:9},{no:13,name:"is_publisher",kind:"scalar",T:8}])),function(e){e[e.JOINING=0]="JOINING",e[e.JOINED=1]="JOINED",e[e.ACTIVE=2]="ACTIVE",e[e.DISCONNECTED=3]="DISCONNECTED"}(Ze||(Ze={})),Oe.util.setEnumType(Ze,"livekit.ParticipantInfo.State",[{no:0,name:"JOINING"},{no:1,name:"JOINED"},{no:2,name:"ACTIVE"},{no:3,name:"DISCONNECTED"}]);class nt extends w{constructor(e){super(),Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new nt).fromBinary(e,t)}static fromJson(e,t){return(new nt).fromJson(e,t)}static fromJsonString(e,t){return(new nt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(nt,e,t)}}nt.runtime=Oe,nt.typeName="livekit.Encryption",nt.fields=Oe.util.newFieldList((()=>[])),function(e){e[e.NONE=0]="NONE",e[e.GCM=1]="GCM",e[e.CUSTOM=2]="CUSTOM"}($e||($e={})),Oe.util.setEnumType($e,"livekit.Encryption.Type",[{no:0,name:"NONE"},{no:1,name:"GCM"},{no:2,name:"CUSTOM"}]);class st extends w{constructor(e){super(),this.mimeType="",this.mid="",this.cid="",this.layers=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new st).fromBinary(e,t)}static fromJson(e,t){return(new st).fromJson(e,t)}static fromJsonString(e,t){return(new st).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(st,e,t)}}st.runtime=Oe,st.typeName="livekit.SimulcastCodecInfo",st.fields=Oe.util.newFieldList((()=>[{no:1,name:"mime_type",kind:"scalar",T:9},{no:2,name:"mid",kind:"scalar",T:9},{no:3,name:"cid",kind:"scalar",T:9},{no:4,name:"layers",kind:"message",T:ot,repeated:!0}]));class rt extends w{constructor(e){super(),this.sid="",this.type=Ue.AUDIO,this.name="",this.muted=!1,this.width=0,this.height=0,this.simulcast=!1,this.disableDtx=!1,this.source=je.UNKNOWN,this.layers=[],this.mimeType="",this.mid="",this.codecs=[],this.stereo=!1,this.disableRed=!1,this.encryption=$e.NONE,this.stream="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new rt).fromBinary(e,t)}static fromJson(e,t){return(new rt).fromJson(e,t)}static fromJsonString(e,t){return(new rt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(rt,e,t)}}rt.runtime=Oe,rt.typeName="livekit.TrackInfo",rt.fields=Oe.util.newFieldList((()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"type",kind:"enum",T:Oe.getEnumType(Ue)},{no:3,name:"name",kind:"scalar",T:9},{no:4,name:"muted",kind:"scalar",T:8},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"simulcast",kind:"scalar",T:8},{no:8,name:"disable_dtx",kind:"scalar",T:8},{no:9,name:"source",kind:"enum",T:Oe.getEnumType(je)},{no:10,name:"layers",kind:"message",T:ot,repeated:!0},{no:11,name:"mime_type",kind:"scalar",T:9},{no:12,name:"mid",kind:"scalar",T:9},{no:13,name:"codecs",kind:"message",T:st,repeated:!0},{no:14,name:"stereo",kind:"scalar",T:8},{no:15,name:"disable_red",kind:"scalar",T:8},{no:16,name:"encryption",kind:"enum",T:Oe.getEnumType($e)},{no:17,name:"stream",kind:"scalar",T:9}]));class ot extends w{constructor(t){super(),this.quality=e.VideoQuality.LOW,this.width=0,this.height=0,this.bitrate=0,this.ssrc=0,Oe.util.initPartial(t,this)}static fromBinary(e,t){return(new ot).fromBinary(e,t)}static fromJson(e,t){return(new ot).fromJson(e,t)}static fromJsonString(e,t){return(new ot).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ot,e,t)}}ot.runtime=Oe,ot.typeName="livekit.VideoLayer",ot.fields=Oe.util.newFieldList((()=>[{no:1,name:"quality",kind:"enum",T:Oe.getEnumType(e.VideoQuality)},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"ssrc",kind:"scalar",T:13}]));class at extends w{constructor(t){super(),this.kind=e.DataPacket_Kind.RELIABLE,this.value={case:void 0},Oe.util.initPartial(t,this)}static fromBinary(e,t){return(new at).fromBinary(e,t)}static fromJson(e,t){return(new at).fromJson(e,t)}static fromJsonString(e,t){return(new at).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(at,e,t)}}at.runtime=Oe,at.typeName="livekit.DataPacket",at.fields=Oe.util.newFieldList((()=>[{no:1,name:"kind",kind:"enum",T:Oe.getEnumType(e.DataPacket_Kind)},{no:2,name:"user",kind:"message",T:lt,oneof:"value"},{no:3,name:"speaker",kind:"message",T:ct,oneof:"value"}])),e.DataPacket_Kind=void 0,(et=e.DataPacket_Kind||(e.DataPacket_Kind={}))[et.RELIABLE=0]="RELIABLE",et[et.LOSSY=1]="LOSSY",Oe.util.setEnumType(e.DataPacket_Kind,"livekit.DataPacket.Kind",[{no:0,name:"RELIABLE"},{no:1,name:"LOSSY"}]);class ct extends w{constructor(e){super(),this.speakers=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ct).fromBinary(e,t)}static fromJson(e,t){return(new ct).fromJson(e,t)}static fromJsonString(e,t){return(new ct).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ct,e,t)}}ct.runtime=Oe,ct.typeName="livekit.ActiveSpeakerUpdate",ct.fields=Oe.util.newFieldList((()=>[{no:1,name:"speakers",kind:"message",T:dt,repeated:!0}]));class dt extends w{constructor(e){super(),this.sid="",this.level=0,this.active=!1,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new dt).fromBinary(e,t)}static fromJson(e,t){return(new dt).fromJson(e,t)}static fromJsonString(e,t){return(new dt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(dt,e,t)}}dt.runtime=Oe,dt.typeName="livekit.SpeakerInfo",dt.fields=Oe.util.newFieldList((()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"level",kind:"scalar",T:2},{no:3,name:"active",kind:"scalar",T:8}]));class lt extends w{constructor(e){super(),this.participantSid="",this.participantIdentity="",this.payload=new Uint8Array(0),this.destinationSids=[],this.destinationIdentities=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new lt).fromBinary(e,t)}static fromJson(e,t){return(new lt).fromJson(e,t)}static fromJsonString(e,t){return(new lt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(lt,e,t)}}lt.runtime=Oe,lt.typeName="livekit.UserPacket",lt.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:5,name:"participant_identity",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:12},{no:3,name:"destination_sids",kind:"scalar",T:9,repeated:!0},{no:6,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:4,name:"topic",kind:"scalar",T:9,opt:!0}]));class ut extends w{constructor(e){super(),this.participantSid="",this.trackSids=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ut).fromBinary(e,t)}static fromJson(e,t){return(new ut).fromJson(e,t)}static fromJsonString(e,t){return(new ut).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ut,e,t)}}ut.runtime=Oe,ut.typeName="livekit.ParticipantTracks",ut.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sids",kind:"scalar",T:9,repeated:!0}]));class ht extends w{constructor(e){super(),this.edition=tt.Standard,this.version="",this.protocol=0,this.region="",this.nodeId="",this.debugInfo="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ht).fromBinary(e,t)}static fromJson(e,t){return(new ht).fromJson(e,t)}static fromJsonString(e,t){return(new ht).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ht,e,t)}}ht.runtime=Oe,ht.typeName="livekit.ServerInfo",ht.fields=Oe.util.newFieldList((()=>[{no:1,name:"edition",kind:"enum",T:Oe.getEnumType(tt)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"region",kind:"scalar",T:9},{no:5,name:"node_id",kind:"scalar",T:9},{no:6,name:"debug_info",kind:"scalar",T:9}])),function(e){e[e.Standard=0]="Standard",e[e.Cloud=1]="Cloud"}(tt||(tt={})),Oe.util.setEnumType(tt,"livekit.ServerInfo.Edition",[{no:0,name:"Standard"},{no:1,name:"Cloud"}]);class pt extends w{constructor(e){super(),this.sdk=it.UNKNOWN,this.version="",this.protocol=0,this.os="",this.osVersion="",this.deviceModel="",this.browser="",this.browserVersion="",this.address="",this.network="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new pt).fromBinary(e,t)}static fromJson(e,t){return(new pt).fromJson(e,t)}static fromJsonString(e,t){return(new pt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(pt,e,t)}}pt.runtime=Oe,pt.typeName="livekit.ClientInfo",pt.fields=Oe.util.newFieldList((()=>[{no:1,name:"sdk",kind:"enum",T:Oe.getEnumType(it)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"os",kind:"scalar",T:9},{no:5,name:"os_version",kind:"scalar",T:9},{no:6,name:"device_model",kind:"scalar",T:9},{no:7,name:"browser",kind:"scalar",T:9},{no:8,name:"browser_version",kind:"scalar",T:9},{no:9,name:"address",kind:"scalar",T:9},{no:10,name:"network",kind:"scalar",T:9}])),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.JS=1]="JS",e[e.SWIFT=2]="SWIFT",e[e.ANDROID=3]="ANDROID",e[e.FLUTTER=4]="FLUTTER",e[e.GO=5]="GO",e[e.UNITY=6]="UNITY",e[e.REACT_NATIVE=7]="REACT_NATIVE",e[e.RUST=8]="RUST",e[e.PYTHON=9]="PYTHON",e[e.CPP=10]="CPP"}(it||(it={})),Oe.util.setEnumType(it,"livekit.ClientInfo.SDK",[{no:0,name:"UNKNOWN"},{no:1,name:"JS"},{no:2,name:"SWIFT"},{no:3,name:"ANDROID"},{no:4,name:"FLUTTER"},{no:5,name:"GO"},{no:6,name:"UNITY"},{no:7,name:"REACT_NATIVE"},{no:8,name:"RUST"},{no:9,name:"PYTHON"},{no:10,name:"CPP"}]);class mt extends w{constructor(e){super(),this.resumeConnection=Je.UNSET,this.forceRelay=Je.UNSET,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new mt).fromBinary(e,t)}static fromJson(e,t){return(new mt).fromJson(e,t)}static fromJsonString(e,t){return(new mt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(mt,e,t)}}mt.runtime=Oe,mt.typeName="livekit.ClientConfiguration",mt.fields=Oe.util.newFieldList((()=>[{no:1,name:"video",kind:"message",T:gt},{no:2,name:"screen",kind:"message",T:gt},{no:3,name:"resume_connection",kind:"enum",T:Oe.getEnumType(Je)},{no:4,name:"disabled_codecs",kind:"message",T:ft},{no:5,name:"force_relay",kind:"enum",T:Oe.getEnumType(Je)}]));class gt extends w{constructor(e){super(),this.hardwareEncoder=Je.UNSET,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new gt).fromBinary(e,t)}static fromJson(e,t){return(new gt).fromJson(e,t)}static fromJsonString(e,t){return(new gt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(gt,e,t)}}gt.runtime=Oe,gt.typeName="livekit.VideoConfiguration",gt.fields=Oe.util.newFieldList((()=>[{no:1,name:"hardware_encoder",kind:"enum",T:Oe.getEnumType(Je)}]));class ft extends w{constructor(e){super(),this.codecs=[],this.publish=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ft).fromBinary(e,t)}static fromJson(e,t){return(new ft).fromJson(e,t)}static fromJsonString(e,t){return(new ft).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ft,e,t)}}ft.runtime=Oe,ft.typeName="livekit.DisabledCodecs",ft.fields=Oe.util.newFieldList((()=>[{no:1,name:"codecs",kind:"message",T:ze,repeated:!0},{no:2,name:"publish",kind:"message",T:ze,repeated:!0}]));class vt extends w{constructor(e){super(),this.duration=0,this.startTimestamp=j.zero,this.endTimestamp=j.zero,this.rtpClockTicks=j.zero,this.driftSamples=j.zero,this.driftMs=0,this.clockRate=0,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new vt).fromBinary(e,t)}static fromJson(e,t){return(new vt).fromJson(e,t)}static fromJsonString(e,t){return(new vt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(vt,e,t)}}vt.runtime=Oe,vt.typeName="livekit.RTPDrift",vt.fields=Oe.util.newFieldList((()=>[{no:1,name:"start_time",kind:"message",T:Ge},{no:2,name:"end_time",kind:"message",T:Ge},{no:3,name:"duration",kind:"scalar",T:1},{no:4,name:"start_timestamp",kind:"scalar",T:4},{no:5,name:"end_timestamp",kind:"scalar",T:4},{no:6,name:"rtp_clock_ticks",kind:"scalar",T:4},{no:7,name:"drift_samples",kind:"scalar",T:3},{no:8,name:"drift_ms",kind:"scalar",T:1},{no:9,name:"clock_rate",kind:"scalar",T:1}]));class yt extends w{constructor(e){super(),this.duration=0,this.packets=0,this.packetRate=0,this.bytes=j.zero,this.headerBytes=j.zero,this.bitrate=0,this.packetsLost=0,this.packetLossRate=0,this.packetLossPercentage=0,this.packetsDuplicate=0,this.packetDuplicateRate=0,this.bytesDuplicate=j.zero,this.headerBytesDuplicate=j.zero,this.bitrateDuplicate=0,this.packetsPadding=0,this.packetPaddingRate=0,this.bytesPadding=j.zero,this.headerBytesPadding=j.zero,this.bitratePadding=0,this.packetsOutOfOrder=0,this.frames=0,this.frameRate=0,this.jitterCurrent=0,this.jitterMax=0,this.gapHistogram={},this.nacks=0,this.nackAcks=0,this.nackMisses=0,this.nackRepeated=0,this.plis=0,this.firs=0,this.rttCurrent=0,this.rttMax=0,this.keyFrames=0,this.layerLockPlis=0,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new yt).fromBinary(e,t)}static fromJson(e,t){return(new yt).fromJson(e,t)}static fromJsonString(e,t){return(new yt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(yt,e,t)}}yt.runtime=Oe,yt.typeName="livekit.RTPStats",yt.fields=Oe.util.newFieldList((()=>[{no:1,name:"start_time",kind:"message",T:Ge},{no:2,name:"end_time",kind:"message",T:Ge},{no:3,name:"duration",kind:"scalar",T:1},{no:4,name:"packets",kind:"scalar",T:13},{no:5,name:"packet_rate",kind:"scalar",T:1},{no:6,name:"bytes",kind:"scalar",T:4},{no:39,name:"header_bytes",kind:"scalar",T:4},{no:7,name:"bitrate",kind:"scalar",T:1},{no:8,name:"packets_lost",kind:"scalar",T:13},{no:9,name:"packet_loss_rate",kind:"scalar",T:1},{no:10,name:"packet_loss_percentage",kind:"scalar",T:2},{no:11,name:"packets_duplicate",kind:"scalar",T:13},{no:12,name:"packet_duplicate_rate",kind:"scalar",T:1},{no:13,name:"bytes_duplicate",kind:"scalar",T:4},{no:40,name:"header_bytes_duplicate",kind:"scalar",T:4},{no:14,name:"bitrate_duplicate",kind:"scalar",T:1},{no:15,name:"packets_padding",kind:"scalar",T:13},{no:16,name:"packet_padding_rate",kind:"scalar",T:1},{no:17,name:"bytes_padding",kind:"scalar",T:4},{no:41,name:"header_bytes_padding",kind:"scalar",T:4},{no:18,name:"bitrate_padding",kind:"scalar",T:1},{no:19,name:"packets_out_of_order",kind:"scalar",T:13},{no:20,name:"frames",kind:"scalar",T:13},{no:21,name:"frame_rate",kind:"scalar",T:1},{no:22,name:"jitter_current",kind:"scalar",T:1},{no:23,name:"jitter_max",kind:"scalar",T:1},{no:24,name:"gap_histogram",kind:"map",K:5,V:{kind:"scalar",T:13}},{no:25,name:"nacks",kind:"scalar",T:13},{no:37,name:"nack_acks",kind:"scalar",T:13},{no:26,name:"nack_misses",kind:"scalar",T:13},{no:38,name:"nack_repeated",kind:"scalar",T:13},{no:27,name:"plis",kind:"scalar",T:13},{no:28,name:"last_pli",kind:"message",T:Ge},{no:29,name:"firs",kind:"scalar",T:13},{no:30,name:"last_fir",kind:"message",T:Ge},{no:31,name:"rtt_current",kind:"scalar",T:13},{no:32,name:"rtt_max",kind:"scalar",T:13},{no:33,name:"key_frames",kind:"scalar",T:13},{no:34,name:"last_key_frame",kind:"message",T:Ge},{no:35,name:"layer_lock_plis",kind:"scalar",T:13},{no:36,name:"last_layer_lock_pli",kind:"message",T:Ge},{no:44,name:"packet_drift",kind:"message",T:vt},{no:45,name:"report_drift",kind:"message",T:vt}]));class kt extends w{constructor(e){super(),this.unixMicro=j.zero,this.ticks=0,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new kt).fromBinary(e,t)}static fromJson(e,t){return(new kt).fromJson(e,t)}static fromJsonString(e,t){return(new kt).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(kt,e,t)}}kt.runtime=Oe,kt.typeName="livekit.TimedVersion",kt.fields=Oe.util.newFieldList((()=>[{no:1,name:"unix_micro",kind:"scalar",T:3},{no:2,name:"ticks",kind:"scalar",T:5}]));const bt=7e3,Tt=[0,300,1200,2700,4800,bt,bt,bt,bt,bt];class St{constructor(e){this._retryDelays=void 0!==e?[...e]:Tt}nextRetryDelayInMs(e){if(e.retryCount>=this._retryDelays.length)return null;const t=this._retryDelays[e.retryCount];return e.retryCount<=1?t:t+1e3*Math.random()}}function Ct(e,t,i,n){return new(i||(i=Promise))((function(s,r){function o(e){try{c(n.next(e))}catch(e){r(e)}}function a(e){try{c(n.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}c((n=n.apply(e,t||[])).next())}))}function Et(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function wt(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,i=e[Symbol.asyncIterator];return i?i.call(e):(e=Et(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(i){t[i]=e[i]&&function(t){return new Promise((function(n,s){(function(e,t,i,n){Promise.resolve(n).then((function(t){e({value:t,done:i})}),t)})(n,s,(t=e[i](t)).done,t.value)}))}}}"function"==typeof SuppressedError&&SuppressedError;var Pt,Rt={exports:{}},It="object"==typeof Reflect?Reflect:null,Ot=It&&"function"==typeof It.apply?It.apply:function(e,t,i){return Function.prototype.apply.call(e,t,i)};Pt=It&&"function"==typeof It.ownKeys?It.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var Dt=Number.isNaN||function(e){return e!=e};function xt(){xt.init.call(this)}Rt.exports=xt,Rt.exports.once=function(e,t){return new Promise((function(i,n){function s(i){e.removeListener(t,r),n(i)}function r(){"function"==typeof e.removeListener&&e.removeListener("error",s),i([].slice.call(arguments))}Jt(e,t,r,{once:!0}),"error"!==t&&function(e,t,i){"function"==typeof e.on&&Jt(e,"error",t,i)}(e,s,{once:!0})}))},xt.EventEmitter=xt,xt.prototype._events=void 0,xt.prototype._eventsCount=0,xt.prototype._maxListeners=void 0;var Nt=10;function _t(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function Mt(e){return void 0===e._maxListeners?xt.defaultMaxListeners:e._maxListeners}function Lt(e,t,i,n){var s,r,o,a;if(_t(i),void 0===(r=e._events)?(r=e._events=Object.create(null),e._eventsCount=0):(void 0!==r.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),r=e._events),o=r[t]),void 0===o)o=r[t]=i,++e._eventsCount;else if("function"==typeof o?o=r[t]=n?[i,o]:[o,i]:n?o.unshift(i):o.push(i),(s=Mt(e))>0&&o.length>s&&!o.warned){o.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=o.length,a=c,console&&console.warn&&console.warn(a)}return e}function At(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Ut(e,t,i){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:i},s=At.bind(n);return s.listener=i,n.wrapFn=s,s}function jt(e,t,i){var n=e._events;if(void 0===n)return[];var s=n[t];return void 0===s?[]:"function"==typeof s?i?[s.listener||s]:[s]:i?function(e){for(var t=new Array(e.length),i=0;i<t.length;++i)t[i]=e[i].listener||e[i];return t}(s):Ft(s,s.length)}function Bt(e){var t=this._events;if(void 0!==t){var i=t[e];if("function"==typeof i)return 1;if(void 0!==i)return i.length}return 0}function Ft(e,t){for(var i=new Array(t),n=0;n<t;++n)i[n]=e[n];return i}function Jt(e,t,i,n){if("function"==typeof e.on)n.once?e.once(t,i):e.on(t,i);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function s(r){n.once&&e.removeEventListener(t,s),i(r)}))}}Object.defineProperty(xt,"defaultMaxListeners",{enumerable:!0,get:function(){return Nt},set:function(e){if("number"!=typeof e||e<0||Dt(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");Nt=e}}),xt.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},xt.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||Dt(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},xt.prototype.getMaxListeners=function(){return Mt(this)},xt.prototype.emit=function(e){for(var t=[],i=1;i<arguments.length;i++)t.push(arguments[i]);var n="error"===e,s=this._events;if(void 0!==s)n=n&&void 0===s.error;else if(!n)return!1;if(n){var r;if(t.length>0&&(r=t[0]),r instanceof Error)throw r;var o=new Error("Unhandled error."+(r?" ("+r.message+")":""));throw o.context=r,o}var a=s[e];if(void 0===a)return!1;if("function"==typeof a)Ot(a,this,t);else{var c=a.length,d=Ft(a,c);for(i=0;i<c;++i)Ot(d[i],this,t)}return!0},xt.prototype.addListener=function(e,t){return Lt(this,e,t,!1)},xt.prototype.on=xt.prototype.addListener,xt.prototype.prependListener=function(e,t){return Lt(this,e,t,!0)},xt.prototype.once=function(e,t){return _t(t),this.on(e,Ut(this,e,t)),this},xt.prototype.prependOnceListener=function(e,t){return _t(t),this.prependListener(e,Ut(this,e,t)),this},xt.prototype.removeListener=function(e,t){var i,n,s,r,o;if(_t(t),void 0===(n=this._events))return this;if(void 0===(i=n[e]))return this;if(i===t||i.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,i.listener||t));else if("function"!=typeof i){for(s=-1,r=i.length-1;r>=0;r--)if(i[r]===t||i[r].listener===t){o=i[r].listener,s=r;break}if(s<0)return this;0===s?i.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(i,s),1===i.length&&(n[e]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},xt.prototype.off=xt.prototype.removeListener,xt.prototype.removeAllListeners=function(e){var t,i,n;if(void 0===(i=this._events))return this;if(void 0===i.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==i[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete i[e]),this;if(0===arguments.length){var s,r=Object.keys(i);for(n=0;n<r.length;++n)"removeListener"!==(s=r[n])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=i[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},xt.prototype.listeners=function(e){return jt(this,e,!0)},xt.prototype.rawListeners=function(e){return jt(this,e,!1)},xt.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):Bt.call(e,t)},xt.prototype.listenerCount=Bt,xt.prototype.eventNames=function(){return this._eventsCount>0?Pt(this._events):[]};var Vt=Rt.exports;let qt=!0,Kt=!0;function Wt(e,t,i){const n=e.match(t);return n&&n.length>=i&&parseInt(n[i],10)}function Gt(e,t,i){if(!e.RTCPeerConnection)return;const n=e.RTCPeerConnection.prototype,s=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return s.apply(this,arguments);const r=e=>{const t=i(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,r),s.apply(this,[e,r])};const r=n.removeEventListener;n.removeEventListener=function(e,i){if(e!==t||!this._eventMap||!this._eventMap[t])return r.apply(this,arguments);if(!this._eventMap[t].has(i))return r.apply(this,arguments);const n=this._eventMap[t].get(i);return this._eventMap[t].delete(i),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,r.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function Ht(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(qt=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function zt(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Kt=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Qt(){if("object"==typeof window){if(qt)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function Yt(e,t){Kt&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Xt(e){return"[object Object]"===Object.prototype.toString.call(e)}function Zt(e){return Xt(e)?Object.keys(e).reduce((function(t,i){const n=Xt(e[i]),s=n?Zt(e[i]):e[i],r=n&&!Object.keys(s).length;return void 0===s||r?t:Object.assign(t,{[i]:s})}),{}):e}function $t(e,t,i){t&&!i.has(t.id)&&(i.set(t.id,t),Object.keys(t).forEach((n=>{n.endsWith("Id")?$t(e,e.get(t[n]),i):n.endsWith("Ids")&&t[n].forEach((t=>{$t(e,e.get(t),i)}))})))}function ei(e,t,i){const n=i?"outbound-rtp":"inbound-rtp",s=new Map;if(null===t)return s;const r=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&r.push(e)})),r.forEach((t=>{e.forEach((i=>{i.type===n&&i.trackId===t.id&&$t(e,i,s)}))})),s}const ti=Qt;function ii(e,t){const i=e&&e.navigator;if(!i.mediaDevices)return;const n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((i=>{if("require"===i||"advanced"===i||"mediaSource"===i)return;const n="object"==typeof e[i]?e[i]:{ideal:e[i]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);const s=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[s("min",i)]=n.ideal,t.optional.push(e),e={},e[s("max",i)]=n.ideal,t.optional.push(e)):(e[s("",i)]=n.ideal,t.optional.push(e))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[s("",i)]=n.exact):["min","max"].forEach((e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[s(e,i)]=n[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},s=function(e,s){if(t.version>=61)return s(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let r=e.video.facingMode;r=r&&("object"==typeof r?r:{ideal:r});const o=t.version<66;if(r&&("user"===r.exact||"environment"===r.exact||"user"===r.ideal||"environment"===r.ideal)&&(!i.mediaDevices.getSupportedConstraints||!i.mediaDevices.getSupportedConstraints().facingMode||o)){let t;if(delete e.video.facingMode,"environment"===r.exact||"environment"===r.ideal?t=["back","rear"]:"user"!==r.exact&&"user"!==r.ideal||(t=["front"]),t)return i.mediaDevices.enumerateDevices().then((i=>{let o=(i=i.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!o&&i.length&&t.includes("back")&&(o=i[i.length-1]),o&&(e.video.deviceId=r.exact?{exact:o.deviceId}:{ideal:o.deviceId}),e.video=n(e.video),ti("chrome: "+JSON.stringify(e)),s(e)}))}e.video=n(e.video)}return ti("chrome: "+JSON.stringify(e)),s(e)},r=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(i.getUserMedia=function(e,t,n){s(e,(e=>{i.webkitGetUserMedia(e,t,(e=>{n&&n(r(e))}))}))}.bind(i),i.mediaDevices.getUserMedia){const e=i.mediaDevices.getUserMedia.bind(i.mediaDevices);i.mediaDevices.getUserMedia=function(t){return s(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(r(e))))))}}}function ni(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function si(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(i=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===i.track.id)):{track:i.track};const s=new Event("track");s.track=i.track,s.receiver=n,s.transceiver={receiver:n},s.streams=[t.stream],this.dispatchEvent(s)})),t.stream.getTracks().forEach((i=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===i.id)):{track:i};const s=new Event("track");s.track=i,s.receiver=n,s.transceiver={receiver:n},s.streams=[t.stream],this.dispatchEvent(s)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else Gt(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function ri(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const i=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let s=i.apply(this,arguments);return s||(s=t(this,e),this._senders.push(s)),s};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],i.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function oi(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,n]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const s=function(e){const t={};return e.result().forEach((e=>{const i={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((t=>{i[t]=e.stat(t)})),t[i.id]=i})),t},r=function(e){return new Map(Object.keys(e).map((t=>[t,e[t]])))};if(arguments.length>=2){const n=function(e){i(r(s(e)))};return t.apply(this,[n,e])}return new Promise(((e,i)=>{t.apply(this,[function(t){e(r(s(t)))},i])})).then(i,n)}}function ai(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>ei(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),Gt(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>ei(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,i,n;return this.getSenders().forEach((i=>{i.track===e&&(t?n=!0:t=i)})),this.getReceivers().forEach((t=>(t.track===e&&(i?n=!0:i=t),t.track===e))),n||t&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function ci(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){if(!i)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const n=t.apply(this,arguments);return this._shimmedLocalStreams[i.id]?-1===this._shimmedLocalStreams[i.id].indexOf(n)&&this._shimmedLocalStreams[i.id].push(n):this._shimmedLocalStreams[i.id]=[i,n],n};const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();i.apply(this,arguments);const n=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(n)};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};const s=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const i=this._shimmedLocalStreams[t].indexOf(e);-1!==i&&this._shimmedLocalStreams[t].splice(i,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),s.apply(this,arguments)}}function di(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return ci(e);const i=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=i.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const i=new e.MediaStream(t.getTracks());this._streams[t.id]=i,this._reverseStreams[i.id]=t,t=i}n.apply(this,[t])};const s=e.RTCPeerConnection.prototype.removeStream;function r(e,t){let i=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],s=e._streams[n.id];i=i.replace(new RegExp(s.id,"g"),n.id)})),new RTCSessionDescription({type:t.type,sdp:i})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},s.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,i){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const s=this._streams[i.id];if(s)s.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const n=new e.MediaStream([t]);this._streams[i.id]=n,this._reverseStreams[n.id]=i,this.addStream(n)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t],n={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?i.apply(this,[t=>{const i=r(this,t);e[0].apply(null,[i])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):i.apply(this,arguments).then((e=>r(this,e)))}};e.RTCPeerConnection.prototype[t]=n[t]}));const o=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=function(e,t){let i=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],s=e._streams[n.id];i=i.replace(new RegExp(n.id,"g"),s.id)})),new RTCSessionDescription({type:t.type,sdp:i})}(this,arguments[0]),o.apply(this,arguments)):o.apply(this,arguments)};const a=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=a.get.apply(this);return""===e.type?e:r(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((i=>{this._streams[i].getTracks().find((t=>e.track===t))&&(t=this._streams[i])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function li(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}))}function ui(e,t){Gt(e,"negotiationneeded",(e=>{const i=e.target;if(!(t.version<72||i.getConfiguration&&"plan-b"===i.getConfiguration().sdpSemantics)||"stable"===i.signalingState)return e}))}var hi=Object.freeze({__proto__:null,fixNegotiationNeeded:ui,shimAddTrackRemoveTrack:di,shimAddTrackRemoveTrackWithNative:ci,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(i){return t(i).then((t=>{const n=i.video&&i.video.width,s=i.video&&i.video.height,r=i.video&&i.video.frameRate;return i.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:r||3}},n&&(i.video.mandatory.maxWidth=n),s&&(i.video.mandatory.maxHeight=s),e.navigator.mediaDevices.getUserMedia(i)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))},shimGetSendersWithDtmf:ri,shimGetStats:oi,shimGetUserMedia:ii,shimMediaStream:ni,shimOnTrack:si,shimPeerConnection:li,shimSenderReceiverGetStats:ai});function pi(e,t){const i=e&&e.navigator,n=e&&e.MediaStreamTrack;if(i.getUserMedia=function(e,t,n){Yt("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in i.mediaDevices.getSupportedConstraints())){const e=function(e,t,i){t in e&&!(i in e)&&(e[i]=e[t],delete e[t])},t=i.mediaDevices.getUserMedia.bind(i.mediaDevices);if(i.mediaDevices.getUserMedia=function(i){return"object"==typeof i&&"object"==typeof i.audio&&(i=JSON.parse(JSON.stringify(i)),e(i.audio,"autoGainControl","mozAutoGainControl"),e(i.audio,"noiseSuppression","mozNoiseSuppression")),t(i)},n&&n.prototype.getSettings){const t=n.prototype.getSettings;n.prototype.getSettings=function(){const i=t.apply(this,arguments);return e(i,"mozAutoGainControl","autoGainControl"),e(i,"mozNoiseSuppression","noiseSuppression"),i}}if(n&&n.prototype.applyConstraints){const t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(i){return"audio"===this.kind&&"object"==typeof i&&(i=JSON.parse(JSON.stringify(i)),e(i,"autoGainControl","mozAutoGainControl"),e(i,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[i])}}}}function mi(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function gi(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const i=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}));const i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,s,r]=arguments;return n.apply(this,[e||null]).then((e=>{if(t.version<53&&!s)try{e.forEach((e=>{e.type=i[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,n)=>{e.set(n,Object.assign({},t,{type:i[t.type]||t.type}))}))}return e})).then(s,r)}}function fi(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const i=e.RTCPeerConnection.prototype.addTrack;i&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=i.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function vi(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),Gt(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function yi(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){Yt("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function ki(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function bi(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]),e=[...e];const i=e.length>0;i&&e.forEach((e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const n=t.apply(this,arguments);if(i){const{sender:t}=n,i=t.getParameters();(!("encodings"in i)||1===i.encodings.length&&0===Object.keys(i.encodings[0]).length)&&(i.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(i).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return n})}function Ti(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function Si(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function Ci(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}var Ei=Object.freeze({__proto__:null,shimAddTransceiver:bi,shimCreateAnswer:Ci,shimCreateOffer:Si,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(i){if(!i||!i.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===i.video?i.video={mediaSource:t}:i.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(i)})},shimGetParameters:Ti,shimGetUserMedia:pi,shimOnTrack:mi,shimPeerConnection:gi,shimRTCDataChannel:ki,shimReceiverGetStats:vi,shimRemoveStream:yi,shimSenderGetStats:fi});function wi(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((i=>t.call(this,i,e))),e.getVideoTracks().forEach((i=>t.call(this,i,e)))},e.RTCPeerConnection.prototype.addTrack=function(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return n&&n.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const i=e.getTracks();this.getSenders().forEach((e=>{i.includes(e.track)&&this.removeTrack(e)}))})}}function Pi(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const i=new Event("addstream");i.stream=t,e.dispatchEvent(i)}))}),t.apply(e,arguments)}}}function Ri(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,i=t.createOffer,n=t.createAnswer,s=t.setLocalDescription,r=t.setRemoteDescription,o=t.addIceCandidate;t.createOffer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],s=i.apply(this,[n]);return t?(s.then(e,t),Promise.resolve()):s},t.createAnswer=function(e,t){const i=arguments.length>=2?arguments[2]:arguments[0],s=n.apply(this,[i]);return t?(s.then(e,t),Promise.resolve()):s};let a=function(e,t,i){const n=s.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n};t.setLocalDescription=a,a=function(e,t,i){const n=r.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n},t.setRemoteDescription=a,a=function(e,t,i){const n=o.apply(this,[e]);return i?(n.then(t,i),Promise.resolve()):n},t.addIceCandidate=a}function Ii(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,i=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>i(Oi(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,i,n){t.mediaDevices.getUserMedia(e).then(i,n)}.bind(t))}function Oi(e){return e&&void 0!==e.video?Object.assign({},e,{video:Zt(e.video)}):e}function Di(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,i){if(e&&e.iceServers){const t=[];for(let i=0;i<e.iceServers.length;i++){let n=e.iceServers[i];void 0===n.urls&&n.url?(Yt("RTCIceServer.url","RTCIceServer.urls"),n=JSON.parse(JSON.stringify(n)),n.urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[i])}e.iceServers=t}return new t(e,i)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function xi(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Ni(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const i=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&i?"sendrecv"===i.direction?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":"recvonly"===i.direction&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):!0!==e.offerToReceiveVideo||i||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function _i(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var Mi=Object.freeze({__proto__:null,shimAudioContext:_i,shimCallbacksAPI:Ri,shimConstraints:Oi,shimCreateOfferLegacy:Ni,shimGetUserMedia:Ii,shimLocalStreamsAPI:wi,shimRTCIceServerUrls:Di,shimRemoteStreamsAPI:Pi,shimTrackEventTransceiver:xi}),Li={exports:{}};!function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const i=t.splitSections(e);return i&&i[0]},t.getMediaSections=function(e){const i=t.splitSections(e);return i.shift(),i},t.matchPrefix=function(e,i){return t.splitLines(e).filter((e=>0===e.indexOf(i)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const i={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":i.relatedAddress=t[e+1];break;case"rport":i.relatedPort=parseInt(t[e+1],10);break;case"tcptype":i.tcpType=t[e+1];break;case"ufrag":i.ufrag=t[e+1],i.usernameFragment=t[e+1];break;default:void 0===i[t[e]]&&(i[t[e]]=t[e+1])}return i},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const i=e.component;"rtp"===i?t.push(1):"rtcp"===i?t.push(2):t.push(i),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){let t=e.substring(9).split(" ");const i={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),i.name=t[0],i.clockRate=parseInt(t[1],10),i.channels=3===t.length?parseInt(t[2],10):1,i.numChannels=i.channels,i},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const i=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==i?"/"+i:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){const t={};let i;const n=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)i=n[e].trim().split("="),t[i[0].trim()]=i[1];return t},t.writeFmtp=function(e){let t="",i=e.payloadType;if(void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const n=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)})),t+="a=fmtp:"+i+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",i=e.payloadType;return void 0!==e.preferredPayloadType&&(i=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+i+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),i={ssrc:parseInt(e.substring(7,t),10)},n=e.indexOf(":",t);return n>-1?(i.attribute=e.substring(t+1,n),i.value=e.substring(n+1)):i.attribute=e.substring(t+1),i},t.parseSsrcGroup=function(e){const t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const i=t.matchPrefix(e,"a=mid:")[0];if(i)return i.substring(6)},t.parseFingerprint=function(e){const t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,i){return{role:"auto",fingerprints:t.matchPrefix(e+i,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let i="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{i+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),i},t.parseCryptoLine=function(e){const t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,i){return t.matchPrefix(e+i,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,i){const n=t.matchPrefix(e+i,"a=ice-ufrag:")[0],s=t.matchPrefix(e+i,"a=ice-pwd:")[0];return n&&s?{usernameFragment:n.substring(12),password:s.substring(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const i={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" ");i.profile=n[2];for(let s=3;s<n.length;s++){const r=n[s],o=t.matchPrefix(e,"a=rtpmap:"+r+" ")[0];if(o){const n=t.parseRtpMap(o),s=t.matchPrefix(e,"a=fmtp:"+r+" ");switch(n.parameters=s.length?t.parseFmtp(s[0]):{},n.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+r+" ").map(t.parseRtcpFb),i.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":i.fecMechanisms.push(n.name.toUpperCase())}}}t.matchPrefix(e,"a=extmap:").forEach((e=>{i.headerExtensions.push(t.parseExtmap(e))}));const s=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return i.codecs.forEach((e=>{s.forEach((t=>{e.rtcpFeedback.find((e=>e.type===t.type&&e.parameter===t.parameter))||e.rtcpFeedback.push(t)}))})),i},t.writeRtpDescription=function(e,i){let n="";n+="m="+e+" ",n+=i.codecs.length>0?"9":"0",n+=" "+(i.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=i.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",i.codecs.forEach((e=>{n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)}));let s=0;return i.codecs.forEach((e=>{e.maxptime>s&&(s=e.maxptime)})),s>0&&(n+="a=maxptime:"+s+"\r\n"),i.headerExtensions&&i.headerExtensions.forEach((e=>{n+=t.writeExtmap(e)})),n},t.parseRtpEncodingParameters=function(e){const i=[],n=t.parseRtpParameters(e),s=-1!==n.fecMechanisms.indexOf("RED"),r=-1!==n.fecMechanisms.indexOf("ULPFEC"),o=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),a=o.length>0&&o[0].ssrc;let c;const d=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substring(17).split(" ").map((e=>parseInt(e,10)))));d.length>0&&d[0].length>1&&d[0][0]===a&&(c=d[0][1]),n.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:a,codecPayloadType:parseInt(e.parameters.apt,10)};a&&c&&(t.rtx={ssrc:c}),i.push(t),s&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:a,mechanism:r?"red+ulpfec":"red"},i.push(t))}})),0===i.length&&a&&i.push({ssrc:a});let l=t.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substring(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substring(5),10)*.95-16e3:void 0,i.forEach((e=>{e.maxBitrate=l}))),i},t.parseRtcpParameters=function(e){const i={},n=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];n&&(i.cname=n.value,i.ssrc=n.ssrc);const s=t.matchPrefix(e,"a=rtcp-rsize");i.reducedSize=s.length>0,i.compound=0===s.length;const r=t.matchPrefix(e,"a=rtcp-mux");return i.mux=r.length>0,i},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let i;const n=t.matchPrefix(e,"a=msid:");if(1===n.length)return i=n[0].substring(7).split(" "),{stream:i[0],track:i[1]};const s=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return s.length>0?(i=s[0].value.split(" "),{stream:i[0],track:i[1]}):void 0},t.parseSctpDescription=function(e){const i=t.parseMLine(e),n=t.matchPrefix(e,"a=max-message-size:");let s;n.length>0&&(s=parseInt(n[0].substring(19),10)),isNaN(s)&&(s=65536);const r=t.matchPrefix(e,"a=sctp-port:");if(r.length>0)return{port:parseInt(r[0].substring(12),10),protocol:i.fmt,maxMessageSize:s};const o=t.matchPrefix(e,"a=sctpmap:");if(o.length>0){const e=o[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:s}}},t.writeSctpDescription=function(e,t){let i=[];return i="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&i.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),i.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,i,n){let s;const r=void 0!==i?i:2;s=e||t.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+s+" "+r+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,i){const n=t.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substring(2)}return i?t.getDirection(i):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const i=t.splitLines(e)[0].substring(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},t.parseOLine=function(e){const i=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const i=t.splitLines(e);for(let e=0;e<i.length;e++)if(i[e].length<2||"="!==i[e].charAt(1))return!1;return!0},e.exports=t}(Li);var Ai=Li.exports,Ui=n(Ai),ji=t({__proto__:null,default:Ui},[Ai]);function Bi(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){const i=new t(e),n=Ui.parseCandidate(e.candidate);for(const e in n)e in i||Object.defineProperty(i,e,{value:n[e]});return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,Gt(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function Fi(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||Gt(e,"icecandidate",(e=>{if(e.candidate){const t=Ui.parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e}))}function Ji(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const i=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(function(e){if(!e||!e.sdp)return!1;const t=Ui.splitSections(e.sdp);return t.shift(),t.some((e=>{const t=Ui.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){const e=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const i=parseInt(t[1],10);return i!=i?-1:i}(arguments[0]),i=function(e){let i=65536;return"firefox"===t.browser&&(i=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),i}(e),n=function(e,i){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);const s=Ui.matchPrefix(e.sdp,"a=max-message-size:");return s.length>0?n=parseInt(s[0].substring(19),10):"firefox"===t.browser&&-1!==i&&(n=2147483637),n}(arguments[0],e);let s;s=0===i&&0===n?Number.POSITIVE_INFINITY:0===i||0===n?Math.max(i,n):Math.min(i,n);const r={};Object.defineProperty(r,"maxMessageSize",{get:()=>s}),this._sctp=r}return i.apply(this,arguments)}}function Vi(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const i=e.send;e.send=function(){const n=arguments[0],s=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&s>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return i.apply(e,arguments)}}const i=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=i.apply(this,arguments);return t(e,this),e},Gt(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function qi(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const i=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const i=new Event("connectionstatechange",e);t.dispatchEvent(i)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),i.apply(this,arguments)}}))}function Ki(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const i=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const i=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:i}):t.sdp=i}return i.apply(this,arguments)}}function Wi(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const i=e.RTCPeerConnection.prototype.addIceCandidate;i&&0!==i.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():i.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function Gi(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const i=e.RTCPeerConnection.prototype.setLocalDescription;i&&0!==i.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return i.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return i.apply(this,[e]);return("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then((e=>i.apply(this,[e])))})}var Hi=Object.freeze({__proto__:null,removeExtmapAllowMixed:Ki,shimAddIceCandidateNullOrEmpty:Wi,shimConnectionState:qi,shimMaxMessageSize:Ji,shimParameterlessSetLocalDescription:Gi,shimRTCIceCandidate:Bi,shimRTCIceCandidateRelayProtocol:Fi,shimSendThrowTypeError:Vi});!function(){let{window:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0};const i=Qt,n=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;const{navigator:i}=e;if(i.mozGetUserMedia)t.browser="firefox",t.version=Wt(i.userAgent,/Firefox\/(\d+)\./,1);else if(i.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=Wt(i.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!i.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=Wt(i.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),s={browserDetails:n,commonShim:Hi,extractVersion:Wt,disableLog:Ht,disableWarnings:zt,sdp:ji};switch(n.browser){case"chrome":if(!hi||!li||!t.shimChrome)return i("Chrome shim is not included in this adapter release."),s;if(null===n.version)return i("Chrome shim can not determine version, not shimming."),s;i("adapter.js shimming chrome."),s.browserShim=hi,Wi(e,n),Gi(e),ii(e,n),ni(e),li(e,n),si(e),di(e,n),ri(e),oi(e),ai(e),ui(e,n),Bi(e),Fi(e),qi(e),Ji(e,n),Vi(e),Ki(e,n);break;case"firefox":if(!Ei||!gi||!t.shimFirefox)return i("Firefox shim is not included in this adapter release."),s;i("adapter.js shimming firefox."),s.browserShim=Ei,Wi(e,n),Gi(e),pi(e,n),gi(e,n),mi(e),yi(e),fi(e),vi(e),ki(e),bi(e),Ti(e),Si(e),Ci(e),Bi(e),qi(e),Ji(e,n),Vi(e);break;case"safari":if(!Mi||!t.shimSafari)return i("Safari shim is not included in this adapter release."),s;i("adapter.js shimming safari."),s.browserShim=Mi,Wi(e,n),Gi(e),Di(e),Ni(e),Ri(e),wi(e),Pi(e),xi(e),Ii(e),_i(e),Bi(e),Fi(e),Ji(e,n),Vi(e),Ki(e,n);break;default:i("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});const zi="AES-GCM",Qi="lk_e2ee",Yi={sharedKey:!1,ratchetSalt:"LKFrameEncryptionKey",ratchetWindowSize:8,failureTolerance:10};var Xi,Zi;function $i(){return tn()||en()}function en(){return void 0!==window.RTCRtpScriptTransform}function tn(){return void 0!==window.RTCRtpSender&&void 0!==window.RTCRtpSender.prototype.createEncodedStreams}function nn(e){return Ct(this,void 0,void 0,(function*(){let t=new TextEncoder;return yield crypto.subtle.importKey("raw",t.encode(e),{name:"PBKDF2"},!1,["deriveBits","deriveKey"])}))}function sn(e){return Ct(this,void 0,void 0,(function*(){return yield crypto.subtle.importKey("raw",e,"HKDF",!1,["deriveBits","deriveKey"])}))}function rn(e,t){const i=(new TextEncoder).encode(t);switch(e){case"HKDF":return{name:"HKDF",salt:i,hash:"SHA-256",info:new ArrayBuffer(128)};case"PBKDF2":return{name:"PBKDF2",salt:i,hash:"SHA-256",iterations:1e5};default:throw new Error("algorithm ".concat(e," is currently unsupported"))}}e.KeyProviderEvent=void 0,(Xi=e.KeyProviderEvent||(e.KeyProviderEvent={})).SetKey="setKey",Xi.RatchetRequest="ratchetRequest",Xi.KeyRatcheted="keyRatcheted",e.KeyHandlerEvent=void 0,(e.KeyHandlerEvent||(e.KeyHandlerEvent={})).KeyRatcheted="keyRatcheted",e.EncryptionEvent=void 0,(Zi=e.EncryptionEvent||(e.EncryptionEvent={})).ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",Zi.EncryptionError="encryptionError",e.CryptorEvent=void 0,(e.CryptorEvent||(e.CryptorEvent={})).Error="cryptorError";class on extends Vt.EventEmitter{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(),this.onKeyRatcheted=(e,t)=>{c.debug("key ratcheted event received",{material:e,keyIndex:t})},this.keyInfoMap=new Map,this.options=Object.assign(Object.assign({},Yi),t),this.on(e.KeyProviderEvent.KeyRatcheted,this.onKeyRatcheted)}onSetEncryptionKey(t,i,n){const s={key:t,participantIdentity:i,keyIndex:n};this.keyInfoMap.set("".concat(null!=i?i:"shared","-").concat(null!=n?n:0),s),this.emit(e.KeyProviderEvent.SetKey,s)}getKeys(){return Array.from(this.keyInfoMap.values())}getOptions(){return this.options}ratchetKey(t,i){this.emit(e.KeyProviderEvent.RatchetRequest,t,i)}}class an extends Error{constructor(e,t){super(t||"an error has occured"),this.code=e}}class cn extends an{constructor(e,t,i){super(1,e),this.status=i,this.reason=t}}class dn extends an{constructor(e){super(21,null!=e?e:"device is unsupported")}}class ln extends an{constructor(e){super(20,null!=e?e:"track is invalid")}}class un extends an{constructor(e){super(10,null!=e?e:"unsupported server")}}class hn extends an{constructor(e){super(12,null!=e?e:"unexpected connection state")}}class pn extends an{constructor(e){super(13,null!=e?e:"unable to negotiate")}}var mn,gn,fn,vn,yn;function kn(e,t,i){var n,s,r;void 0===t&&(t=50),void 0===i&&(i={});var o=null!=(n=i.isImmediate)&&n,a=null!=(s=i.callback)&&s,c=i.maxWait,d=Date.now(),l=[];function u(){if(void 0!==c){var e=Date.now()-d;if(e+t>=c)return c-e}return t}var h=function(){var t=[].slice.call(arguments),i=this;return new Promise((function(n,s){var c=o&&void 0===r;if(void 0!==r&&clearTimeout(r),r=setTimeout((function(){if(r=void 0,d=Date.now(),!o){var n=e.apply(i,t);a&&a(n),l.forEach((function(e){return(0,e.resolve)(n)})),l=[]}}),u()),c){var h=e.apply(i,t);return a&&a(h),n(h)}l.push({resolve:n,reject:s})}))};return h.cancel=function(e){void 0!==r&&clearTimeout(r),l.forEach((function(t){return(0,t.reject)(e)})),l=[]},h}e.MediaDeviceFailure=void 0,(mn=e.MediaDeviceFailure||(e.MediaDeviceFailure={})).PermissionDenied="PermissionDenied",mn.NotFound="NotFound",mn.DeviceInUse="DeviceInUse",mn.Other="Other",function(e){e.getFailure=function(t){if(t&&"name"in t)return"NotFoundError"===t.name||"DevicesNotFoundError"===t.name?e.NotFound:"NotAllowedError"===t.name||"PermissionDeniedError"===t.name?e.PermissionDenied:"NotReadableError"===t.name||"TrackStartError"===t.name?e.DeviceInUse:e.Other}}(e.MediaDeviceFailure||(e.MediaDeviceFailure={})),e.RoomEvent=void 0,(gn=e.RoomEvent||(e.RoomEvent={})).Connected="connected",gn.Reconnecting="reconnecting",gn.Reconnected="reconnected",gn.Disconnected="disconnected",gn.ConnectionStateChanged="connectionStateChanged",gn.StateChanged="connectionStateChanged",gn.MediaDevicesChanged="mediaDevicesChanged",gn.ParticipantConnected="participantConnected",gn.ParticipantDisconnected="participantDisconnected",gn.TrackPublished="trackPublished",gn.TrackSubscribed="trackSubscribed",gn.TrackSubscriptionFailed="trackSubscriptionFailed",gn.TrackUnpublished="trackUnpublished",gn.TrackUnsubscribed="trackUnsubscribed",gn.TrackMuted="trackMuted",gn.TrackUnmuted="trackUnmuted",gn.LocalTrackPublished="localTrackPublished",gn.LocalTrackUnpublished="localTrackUnpublished",gn.LocalAudioSilenceDetected="localAudioSilenceDetected",gn.ActiveSpeakersChanged="activeSpeakersChanged",gn.ParticipantMetadataChanged="participantMetadataChanged",gn.ParticipantNameChanged="participantNameChanged",gn.RoomMetadataChanged="roomMetadataChanged",gn.DataReceived="dataReceived",gn.ConnectionQualityChanged="connectionQualityChanged",gn.TrackStreamStateChanged="trackStreamStateChanged",gn.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",gn.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",gn.AudioPlaybackStatusChanged="audioPlaybackChanged",gn.VideoPlaybackStatusChanged="videoPlaybackChanged",gn.MediaDevicesError="mediaDevicesError",gn.ParticipantPermissionsChanged="participantPermissionsChanged",gn.SignalConnected="signalConnected",gn.RecordingStatusChanged="recordingStatusChanged",gn.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",gn.EncryptionError="encryptionError",gn.DCBufferStatusChanged="dcBufferStatusChanged",gn.ActiveDeviceChanged="activeDeviceChanged",e.ParticipantEvent=void 0,(fn=e.ParticipantEvent||(e.ParticipantEvent={})).TrackPublished="trackPublished",fn.TrackSubscribed="trackSubscribed",fn.TrackSubscriptionFailed="trackSubscriptionFailed",fn.TrackUnpublished="trackUnpublished",fn.TrackUnsubscribed="trackUnsubscribed",fn.TrackMuted="trackMuted",fn.TrackUnmuted="trackUnmuted",fn.LocalTrackPublished="localTrackPublished",fn.LocalTrackUnpublished="localTrackUnpublished",fn.ParticipantMetadataChanged="participantMetadataChanged",fn.ParticipantNameChanged="participantNameChanged",fn.DataReceived="dataReceived",fn.IsSpeakingChanged="isSpeakingChanged",fn.ConnectionQualityChanged="connectionQualityChanged",fn.TrackStreamStateChanged="trackStreamStateChanged",fn.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",fn.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",fn.MediaDevicesError="mediaDevicesError",fn.AudioStreamAcquired="audioStreamAcquired",fn.ParticipantPermissionsChanged="participantPermissionsChanged",fn.PCTrackAdded="pcTrackAdded",e.EngineEvent=void 0,(vn=e.EngineEvent||(e.EngineEvent={})).TransportsCreated="transportsCreated",vn.Connected="connected",vn.Disconnected="disconnected",vn.Resuming="resuming",vn.Resumed="resumed",vn.Restarting="restarting",vn.Restarted="restarted",vn.SignalResumed="signalResumed",vn.SignalRestarted="signalRestarted",vn.Closing="closing",vn.MediaTrackAdded="mediaTrackAdded",vn.ActiveSpeakersUpdate="activeSpeakersUpdate",vn.DataPacketReceived="dataPacketReceived",vn.RTPVideoMapUpdate="rtpVideoMapUpdate",vn.DCBufferStatusChanged="dcBufferStatusChanged",vn.ParticipantUpdate="participantUpdate",vn.RoomUpdate="roomUpdate",vn.SpeakersChanged="speakersChanged",vn.StreamStateChanged="streamStateChanged",vn.ConnectionQualityUpdate="connectionQualityUpdate",vn.SubscriptionError="subscriptionError",vn.SubscriptionPermissionUpdate="subscriptionPermissionUpdate",vn.RemoteMute="remoteMute",vn.SubscribedQualityUpdate="subscribedQualityUpdate",vn.LocalTrackUnpublished="localTrackUnpublished",e.TrackEvent=void 0,(yn=e.TrackEvent||(e.TrackEvent={})).Message="message",yn.Muted="muted",yn.Unmuted="unmuted",yn.Restarted="restarted",yn.Ended="ended",yn.Subscribed="subscribed",yn.Unsubscribed="unsubscribed",yn.UpdateSettings="updateSettings",yn.UpdateSubscription="updateSubscription",yn.AudioPlaybackStarted="audioPlaybackStarted",yn.AudioPlaybackFailed="audioPlaybackFailed",yn.AudioSilenceDetected="audioSilenceDetected",yn.VisibilityChanged="visibilityChanged",yn.VideoDimensionsChanged="videoDimensionsChanged",yn.VideoPlaybackStarted="videoPlaybackStarted",yn.VideoPlaybackFailed="videoPlaybackFailed",yn.ElementAttached="elementAttached",yn.ElementDetached="elementDetached",yn.UpstreamPaused="upstreamPaused",yn.UpstreamResumed="upstreamResumed",yn.SubscriptionPermissionChanged="subscriptionPermissionChanged",yn.SubscriptionStatusChanged="subscriptionStatusChanged",yn.SubscriptionFailed="subscriptionFailed";const bn=/version\/(\d+(\.?_?\d+)+)/i;let Tn;function Sn(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0===e&&"undefined"==typeof navigator)return;const i=(null!=e?e:navigator.userAgent).toLowerCase();if(void 0===Tn||t){const e=Cn.find((e=>{let{test:t}=e;return t.test(i)}));Tn=null==e?void 0:e.describe(i)}return Tn}const Cn=[{test:/firefox|iceweasel|fxios/i,describe:e=>({name:"Firefox",version:En(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("fxios")?"iOS":void 0})},{test:/chrom|crios|crmo/i,describe:e=>({name:"Chrome",version:En(/(?:chrome|chromium|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("crios")?"iOS":void 0})},{test:/safari|applewebkit/i,describe:e=>({name:"Safari",version:En(bn,e),os:e.includes("mobile/")?"iOS":"macOS"})}];function En(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;const n=t.match(e);return n&&n.length>=i&&n[i]||""}const wn="1.15.13";class Pn{}Pn.setTimeout=function(){return setTimeout(...arguments)},Pn.setInterval=function(){return setInterval(...arguments)},Pn.clearTimeout=function(){return clearTimeout(...arguments)},Pn.clearInterval=function(){return clearInterval(...arguments)};class Rn{constructor(e,t,i,n,s){this.width=e,this.height=t,this.encoding={maxBitrate:i,maxFramerate:n,priority:s}}get resolution(){return{width:this.width,height:this.height,frameRate:this.encoding.maxFramerate,aspectRatio:this.width/this.height}}}const In=["vp8","h264"],On=["vp8","h264","vp9","av1"];function Dn(e){return!!In.find((t=>t===e))}var xn;e.AudioPresets=void 0,(xn=e.AudioPresets||(e.AudioPresets={})).telephone={maxBitrate:12e3},xn.speech={maxBitrate:2e4},xn.music={maxBitrate:32e3},xn.musicStereo={maxBitrate:48e3},xn.musicHighQuality={maxBitrate:64e3},xn.musicHighQualityStereo={maxBitrate:96e3};const Nn={h90:new Rn(160,90,9e4,20),h180:new Rn(320,180,16e4,20),h216:new Rn(384,216,18e4,20),h360:new Rn(640,360,45e4,20),h540:new Rn(960,540,8e5,25),h720:new Rn(1280,720,17e5,30),h1080:new Rn(1920,1080,3e6,30),h1440:new Rn(2560,1440,5e6,30),h2160:new Rn(3840,2160,8e6,30)},_n={h120:new Rn(160,120,7e4,20),h180:new Rn(240,180,125e3,20),h240:new Rn(320,240,14e4,20),h360:new Rn(480,360,33e4,20),h480:new Rn(640,480,5e5,20),h540:new Rn(720,540,6e5,25),h720:new Rn(960,720,13e5,30),h1080:new Rn(1440,1080,23e5,30),h1440:new Rn(1920,1440,38e5,30)},Mn={h360fps3:new Rn(640,360,2e5,3,"medium"),h360fps15:new Rn(640,360,4e5,15,"medium"),h720fps5:new Rn(1280,720,8e5,5,"medium"),h720fps15:new Rn(1280,720,15e5,15,"medium"),h720fps30:new Rn(1280,720,2e6,30,"medium"),h1080fps15:new Rn(1920,1080,25e5,15,"medium"),h1080fps30:new Rn(1920,1080,5e6,30,"medium"),original:new Rn(0,0,7e6,30,"medium")};var Ln,An,Un;!function(e){e[e.PUBLISHER=0]="PUBLISHER",e[e.SUBSCRIBER=1]="SUBSCRIBER"}(Ln||(Ln={})),Oe.util.setEnumType(Ln,"livekit.SignalTarget",[{no:0,name:"PUBLISHER"},{no:1,name:"SUBSCRIBER"}]),function(e){e[e.ACTIVE=0]="ACTIVE",e[e.PAUSED=1]="PAUSED"}(An||(An={})),Oe.util.setEnumType(An,"livekit.StreamState",[{no:0,name:"ACTIVE"},{no:1,name:"PAUSED"}]),function(e){e[e.UDP=0]="UDP",e[e.TCP=1]="TCP",e[e.TLS=2]="TLS"}(Un||(Un={})),Oe.util.setEnumType(Un,"livekit.CandidateProtocol",[{no:0,name:"UDP"},{no:1,name:"TCP"},{no:2,name:"TLS"}]);class jn extends w{constructor(e){super(),this.message={case:void 0},Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new jn).fromBinary(e,t)}static fromJson(e,t){return(new jn).fromJson(e,t)}static fromJsonString(e,t){return(new jn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(jn,e,t)}}jn.runtime=Oe,jn.typeName="livekit.SignalRequest",jn.fields=Oe.util.newFieldList((()=>[{no:1,name:"offer",kind:"message",T:zn,oneof:"message"},{no:2,name:"answer",kind:"message",T:zn,oneof:"message"},{no:3,name:"trickle",kind:"message",T:Vn,oneof:"message"},{no:4,name:"add_track",kind:"message",T:Jn,oneof:"message"},{no:5,name:"mute",kind:"message",T:qn,oneof:"message"},{no:6,name:"subscription",kind:"message",T:Yn,oneof:"message"},{no:7,name:"track_setting",kind:"message",T:Xn,oneof:"message"},{no:8,name:"leave",kind:"message",T:Zn,oneof:"message"},{no:10,name:"update_layers",kind:"message",T:$n,oneof:"message"},{no:11,name:"subscription_permission",kind:"message",T:hs,oneof:"message"},{no:12,name:"sync_state",kind:"message",T:ms,oneof:"message"},{no:13,name:"simulate",kind:"message",T:fs,oneof:"message"},{no:14,name:"ping",kind:"scalar",T:3,oneof:"message"},{no:15,name:"update_metadata",kind:"message",T:es,oneof:"message"},{no:16,name:"ping_req",kind:"message",T:vs,oneof:"message"}]));class Bn extends w{constructor(e){super(),this.message={case:void 0},Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Bn).fromBinary(e,t)}static fromJson(e,t){return(new Bn).fromJson(e,t)}static fromJsonString(e,t){return(new Bn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Bn,e,t)}}Bn.runtime=Oe,Bn.typeName="livekit.SignalResponse",Bn.fields=Oe.util.newFieldList((()=>[{no:1,name:"join",kind:"message",T:Kn,oneof:"message"},{no:2,name:"answer",kind:"message",T:zn,oneof:"message"},{no:3,name:"offer",kind:"message",T:zn,oneof:"message"},{no:4,name:"trickle",kind:"message",T:Vn,oneof:"message"},{no:5,name:"update",kind:"message",T:Qn,oneof:"message"},{no:6,name:"track_published",kind:"message",T:Gn,oneof:"message"},{no:8,name:"leave",kind:"message",T:Zn,oneof:"message"},{no:9,name:"mute",kind:"message",T:qn,oneof:"message"},{no:10,name:"speakers_changed",kind:"message",T:is,oneof:"message"},{no:11,name:"room_update",kind:"message",T:ns,oneof:"message"},{no:12,name:"connection_quality",kind:"message",T:rs,oneof:"message"},{no:13,name:"stream_state_update",kind:"message",T:as,oneof:"message"},{no:14,name:"subscribed_quality_update",kind:"message",T:ls,oneof:"message"},{no:15,name:"subscription_permission_update",kind:"message",T:ps,oneof:"message"},{no:16,name:"refresh_token",kind:"scalar",T:9,oneof:"message"},{no:17,name:"track_unpublished",kind:"message",T:Hn,oneof:"message"},{no:18,name:"pong",kind:"scalar",T:3,oneof:"message"},{no:19,name:"reconnect",kind:"message",T:Wn,oneof:"message"},{no:20,name:"pong_resp",kind:"message",T:ys,oneof:"message"},{no:21,name:"subscription_response",kind:"message",T:Ts,oneof:"message"}]));class Fn extends w{constructor(e){super(),this.codec="",this.cid="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Fn).fromBinary(e,t)}static fromJson(e,t){return(new Fn).fromJson(e,t)}static fromJsonString(e,t){return(new Fn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Fn,e,t)}}Fn.runtime=Oe,Fn.typeName="livekit.SimulcastCodec",Fn.fields=Oe.util.newFieldList((()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"cid",kind:"scalar",T:9}]));class Jn extends w{constructor(e){super(),this.cid="",this.name="",this.type=Ue.AUDIO,this.width=0,this.height=0,this.muted=!1,this.disableDtx=!1,this.source=je.UNKNOWN,this.layers=[],this.simulcastCodecs=[],this.sid="",this.stereo=!1,this.disableRed=!1,this.encryption=$e.NONE,this.stream="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Jn).fromBinary(e,t)}static fromJson(e,t){return(new Jn).fromJson(e,t)}static fromJsonString(e,t){return(new Jn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Jn,e,t)}}Jn.runtime=Oe,Jn.typeName="livekit.AddTrackRequest",Jn.fields=Oe.util.newFieldList((()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:Oe.getEnumType(Ue)},{no:4,name:"width",kind:"scalar",T:13},{no:5,name:"height",kind:"scalar",T:13},{no:6,name:"muted",kind:"scalar",T:8},{no:7,name:"disable_dtx",kind:"scalar",T:8},{no:8,name:"source",kind:"enum",T:Oe.getEnumType(je)},{no:9,name:"layers",kind:"message",T:ot,repeated:!0},{no:10,name:"simulcast_codecs",kind:"message",T:Fn,repeated:!0},{no:11,name:"sid",kind:"scalar",T:9},{no:12,name:"stereo",kind:"scalar",T:8},{no:13,name:"disable_red",kind:"scalar",T:8},{no:14,name:"encryption",kind:"enum",T:Oe.getEnumType($e)},{no:15,name:"stream",kind:"scalar",T:9}]));class Vn extends w{constructor(e){super(),this.candidateInit="",this.target=Ln.PUBLISHER,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Vn).fromBinary(e,t)}static fromJson(e,t){return(new Vn).fromJson(e,t)}static fromJsonString(e,t){return(new Vn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Vn,e,t)}}Vn.runtime=Oe,Vn.typeName="livekit.TrickleRequest",Vn.fields=Oe.util.newFieldList((()=>[{no:1,name:"candidateInit",kind:"scalar",T:9},{no:2,name:"target",kind:"enum",T:Oe.getEnumType(Ln)}]));class qn extends w{constructor(e){super(),this.sid="",this.muted=!1,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new qn).fromBinary(e,t)}static fromJson(e,t){return(new qn).fromJson(e,t)}static fromJsonString(e,t){return(new qn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(qn,e,t)}}qn.runtime=Oe,qn.typeName="livekit.MuteTrackRequest",qn.fields=Oe.util.newFieldList((()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"muted",kind:"scalar",T:8}]));class Kn extends w{constructor(e){super(),this.otherParticipants=[],this.serverVersion="",this.iceServers=[],this.subscriberPrimary=!1,this.alternativeUrl="",this.serverRegion="",this.pingTimeout=0,this.pingInterval=0,this.sifTrailer=new Uint8Array(0),Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Kn).fromBinary(e,t)}static fromJson(e,t){return(new Kn).fromJson(e,t)}static fromJsonString(e,t){return(new Kn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Kn,e,t)}}Kn.runtime=Oe,Kn.typeName="livekit.JoinResponse",Kn.fields=Oe.util.newFieldList((()=>[{no:1,name:"room",kind:"message",T:He},{no:2,name:"participant",kind:"message",T:Xe},{no:3,name:"other_participants",kind:"message",T:Xe,repeated:!0},{no:4,name:"server_version",kind:"scalar",T:9},{no:5,name:"ice_servers",kind:"message",T:ts,repeated:!0},{no:6,name:"subscriber_primary",kind:"scalar",T:8},{no:7,name:"alternative_url",kind:"scalar",T:9},{no:8,name:"client_configuration",kind:"message",T:mt},{no:9,name:"server_region",kind:"scalar",T:9},{no:10,name:"ping_timeout",kind:"scalar",T:5},{no:11,name:"ping_interval",kind:"scalar",T:5},{no:12,name:"server_info",kind:"message",T:ht},{no:13,name:"sif_trailer",kind:"scalar",T:12}]));class Wn extends w{constructor(e){super(),this.iceServers=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Wn).fromBinary(e,t)}static fromJson(e,t){return(new Wn).fromJson(e,t)}static fromJsonString(e,t){return(new Wn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Wn,e,t)}}Wn.runtime=Oe,Wn.typeName="livekit.ReconnectResponse",Wn.fields=Oe.util.newFieldList((()=>[{no:1,name:"ice_servers",kind:"message",T:ts,repeated:!0},{no:2,name:"client_configuration",kind:"message",T:mt}]));class Gn extends w{constructor(e){super(),this.cid="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Gn).fromBinary(e,t)}static fromJson(e,t){return(new Gn).fromJson(e,t)}static fromJsonString(e,t){return(new Gn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Gn,e,t)}}Gn.runtime=Oe,Gn.typeName="livekit.TrackPublishedResponse",Gn.fields=Oe.util.newFieldList((()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"track",kind:"message",T:rt}]));class Hn extends w{constructor(e){super(),this.trackSid="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Hn).fromBinary(e,t)}static fromJson(e,t){return(new Hn).fromJson(e,t)}static fromJsonString(e,t){return(new Hn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Hn,e,t)}}Hn.runtime=Oe,Hn.typeName="livekit.TrackUnpublishedResponse",Hn.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]));class zn extends w{constructor(e){super(),this.type="",this.sdp="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new zn).fromBinary(e,t)}static fromJson(e,t){return(new zn).fromJson(e,t)}static fromJsonString(e,t){return(new zn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(zn,e,t)}}zn.runtime=Oe,zn.typeName="livekit.SessionDescription",zn.fields=Oe.util.newFieldList((()=>[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"sdp",kind:"scalar",T:9}]));class Qn extends w{constructor(e){super(),this.participants=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Qn).fromBinary(e,t)}static fromJson(e,t){return(new Qn).fromJson(e,t)}static fromJsonString(e,t){return(new Qn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Qn,e,t)}}Qn.runtime=Oe,Qn.typeName="livekit.ParticipantUpdate",Qn.fields=Oe.util.newFieldList((()=>[{no:1,name:"participants",kind:"message",T:Xe,repeated:!0}]));class Yn extends w{constructor(e){super(),this.trackSids=[],this.subscribe=!1,this.participantTracks=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Yn).fromBinary(e,t)}static fromJson(e,t){return(new Yn).fromJson(e,t)}static fromJsonString(e,t){return(new Yn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Yn,e,t)}}Yn.runtime=Oe,Yn.typeName="livekit.UpdateSubscription",Yn.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:2,name:"subscribe",kind:"scalar",T:8},{no:3,name:"participant_tracks",kind:"message",T:ut,repeated:!0}]));class Xn extends w{constructor(t){super(),this.trackSids=[],this.disabled=!1,this.quality=e.VideoQuality.LOW,this.width=0,this.height=0,this.fps=0,this.priority=0,Oe.util.initPartial(t,this)}static fromBinary(e,t){return(new Xn).fromBinary(e,t)}static fromJson(e,t){return(new Xn).fromJson(e,t)}static fromJsonString(e,t){return(new Xn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Xn,e,t)}}Xn.runtime=Oe,Xn.typeName="livekit.UpdateTrackSettings",Xn.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:3,name:"disabled",kind:"scalar",T:8},{no:4,name:"quality",kind:"enum",T:Oe.getEnumType(e.VideoQuality)},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"fps",kind:"scalar",T:13},{no:8,name:"priority",kind:"scalar",T:13}]));class Zn extends w{constructor(t){super(),this.canReconnect=!1,this.reason=e.DisconnectReason.UNKNOWN_REASON,Oe.util.initPartial(t,this)}static fromBinary(e,t){return(new Zn).fromBinary(e,t)}static fromJson(e,t){return(new Zn).fromJson(e,t)}static fromJsonString(e,t){return(new Zn).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Zn,e,t)}}Zn.runtime=Oe,Zn.typeName="livekit.LeaveRequest",Zn.fields=Oe.util.newFieldList((()=>[{no:1,name:"can_reconnect",kind:"scalar",T:8},{no:2,name:"reason",kind:"enum",T:Oe.getEnumType(e.DisconnectReason)}]));class $n extends w{constructor(e){super(),this.trackSid="",this.layers=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new $n).fromBinary(e,t)}static fromJson(e,t){return(new $n).fromJson(e,t)}static fromJsonString(e,t){return(new $n).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals($n,e,t)}}$n.runtime=Oe,$n.typeName="livekit.UpdateVideoLayers",$n.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"layers",kind:"message",T:ot,repeated:!0}]));class es extends w{constructor(e){super(),this.metadata="",this.name="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new es).fromBinary(e,t)}static fromJson(e,t){return(new es).fromJson(e,t)}static fromJsonString(e,t){return(new es).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(es,e,t)}}es.runtime=Oe,es.typeName="livekit.UpdateParticipantMetadata",es.fields=Oe.util.newFieldList((()=>[{no:1,name:"metadata",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9}]));class ts extends w{constructor(e){super(),this.urls=[],this.username="",this.credential="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ts).fromBinary(e,t)}static fromJson(e,t){return(new ts).fromJson(e,t)}static fromJsonString(e,t){return(new ts).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ts,e,t)}}ts.runtime=Oe,ts.typeName="livekit.ICEServer",ts.fields=Oe.util.newFieldList((()=>[{no:1,name:"urls",kind:"scalar",T:9,repeated:!0},{no:2,name:"username",kind:"scalar",T:9},{no:3,name:"credential",kind:"scalar",T:9}]));class is extends w{constructor(e){super(),this.speakers=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new is).fromBinary(e,t)}static fromJson(e,t){return(new is).fromJson(e,t)}static fromJsonString(e,t){return(new is).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(is,e,t)}}is.runtime=Oe,is.typeName="livekit.SpeakersChanged",is.fields=Oe.util.newFieldList((()=>[{no:1,name:"speakers",kind:"message",T:dt,repeated:!0}]));class ns extends w{constructor(e){super(),Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ns).fromBinary(e,t)}static fromJson(e,t){return(new ns).fromJson(e,t)}static fromJsonString(e,t){return(new ns).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ns,e,t)}}ns.runtime=Oe,ns.typeName="livekit.RoomUpdate",ns.fields=Oe.util.newFieldList((()=>[{no:1,name:"room",kind:"message",T:He}]));class ss extends w{constructor(e){super(),this.participantSid="",this.quality=Fe.POOR,this.score=0,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ss).fromBinary(e,t)}static fromJson(e,t){return(new ss).fromJson(e,t)}static fromJsonString(e,t){return(new ss).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ss,e,t)}}ss.runtime=Oe,ss.typeName="livekit.ConnectionQualityInfo",ss.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"quality",kind:"enum",T:Oe.getEnumType(Fe)},{no:3,name:"score",kind:"scalar",T:2}]));class rs extends w{constructor(e){super(),this.updates=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new rs).fromBinary(e,t)}static fromJson(e,t){return(new rs).fromJson(e,t)}static fromJsonString(e,t){return(new rs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(rs,e,t)}}rs.runtime=Oe,rs.typeName="livekit.ConnectionQualityUpdate",rs.fields=Oe.util.newFieldList((()=>[{no:1,name:"updates",kind:"message",T:ss,repeated:!0}]));class os extends w{constructor(e){super(),this.participantSid="",this.trackSid="",this.state=An.ACTIVE,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new os).fromBinary(e,t)}static fromJson(e,t){return(new os).fromJson(e,t)}static fromJsonString(e,t){return(new os).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(os,e,t)}}os.runtime=Oe,os.typeName="livekit.StreamStateInfo",os.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:Oe.getEnumType(An)}]));class as extends w{constructor(e){super(),this.streamStates=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new as).fromBinary(e,t)}static fromJson(e,t){return(new as).fromJson(e,t)}static fromJsonString(e,t){return(new as).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(as,e,t)}}as.runtime=Oe,as.typeName="livekit.StreamStateUpdate",as.fields=Oe.util.newFieldList((()=>[{no:1,name:"stream_states",kind:"message",T:os,repeated:!0}]));class cs extends w{constructor(t){super(),this.quality=e.VideoQuality.LOW,this.enabled=!1,Oe.util.initPartial(t,this)}static fromBinary(e,t){return(new cs).fromBinary(e,t)}static fromJson(e,t){return(new cs).fromJson(e,t)}static fromJsonString(e,t){return(new cs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(cs,e,t)}}cs.runtime=Oe,cs.typeName="livekit.SubscribedQuality",cs.fields=Oe.util.newFieldList((()=>[{no:1,name:"quality",kind:"enum",T:Oe.getEnumType(e.VideoQuality)},{no:2,name:"enabled",kind:"scalar",T:8}]));class ds extends w{constructor(e){super(),this.codec="",this.qualities=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ds).fromBinary(e,t)}static fromJson(e,t){return(new ds).fromJson(e,t)}static fromJsonString(e,t){return(new ds).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ds,e,t)}}ds.runtime=Oe,ds.typeName="livekit.SubscribedCodec",ds.fields=Oe.util.newFieldList((()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"qualities",kind:"message",T:cs,repeated:!0}]));class ls extends w{constructor(e){super(),this.trackSid="",this.subscribedQualities=[],this.subscribedCodecs=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ls).fromBinary(e,t)}static fromJson(e,t){return(new ls).fromJson(e,t)}static fromJsonString(e,t){return(new ls).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ls,e,t)}}ls.runtime=Oe,ls.typeName="livekit.SubscribedQualityUpdate",ls.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"subscribed_qualities",kind:"message",T:cs,repeated:!0},{no:3,name:"subscribed_codecs",kind:"message",T:ds,repeated:!0}]));class us extends w{constructor(e){super(),this.participantSid="",this.allTracks=!1,this.trackSids=[],this.participantIdentity="",Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new us).fromBinary(e,t)}static fromJson(e,t){return(new us).fromJson(e,t)}static fromJsonString(e,t){return(new us).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(us,e,t)}}us.runtime=Oe,us.typeName="livekit.TrackPermission",us.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"all_tracks",kind:"scalar",T:8},{no:3,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:4,name:"participant_identity",kind:"scalar",T:9}]));class hs extends w{constructor(e){super(),this.allParticipants=!1,this.trackPermissions=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new hs).fromBinary(e,t)}static fromJson(e,t){return(new hs).fromJson(e,t)}static fromJsonString(e,t){return(new hs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(hs,e,t)}}hs.runtime=Oe,hs.typeName="livekit.SubscriptionPermission",hs.fields=Oe.util.newFieldList((()=>[{no:1,name:"all_participants",kind:"scalar",T:8},{no:2,name:"track_permissions",kind:"message",T:us,repeated:!0}]));class ps extends w{constructor(e){super(),this.participantSid="",this.trackSid="",this.allowed=!1,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ps).fromBinary(e,t)}static fromJson(e,t){return(new ps).fromJson(e,t)}static fromJsonString(e,t){return(new ps).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ps,e,t)}}ps.runtime=Oe,ps.typeName="livekit.SubscriptionPermissionUpdate",ps.fields=Oe.util.newFieldList((()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"allowed",kind:"scalar",T:8}]));class ms extends w{constructor(e){super(),this.publishTracks=[],this.dataChannels=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ms).fromBinary(e,t)}static fromJson(e,t){return(new ms).fromJson(e,t)}static fromJsonString(e,t){return(new ms).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ms,e,t)}}ms.runtime=Oe,ms.typeName="livekit.SyncState",ms.fields=Oe.util.newFieldList((()=>[{no:1,name:"answer",kind:"message",T:zn},{no:2,name:"subscription",kind:"message",T:Yn},{no:3,name:"publish_tracks",kind:"message",T:Gn,repeated:!0},{no:4,name:"data_channels",kind:"message",T:gs,repeated:!0},{no:5,name:"offer",kind:"message",T:zn}]));class gs extends w{constructor(e){super(),this.label="",this.id=0,this.target=Ln.PUBLISHER,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new gs).fromBinary(e,t)}static fromJson(e,t){return(new gs).fromJson(e,t)}static fromJsonString(e,t){return(new gs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(gs,e,t)}}gs.runtime=Oe,gs.typeName="livekit.DataChannelInfo",gs.fields=Oe.util.newFieldList((()=>[{no:1,name:"label",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:13},{no:3,name:"target",kind:"enum",T:Oe.getEnumType(Ln)}]));class fs extends w{constructor(e){super(),this.scenario={case:void 0},Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new fs).fromBinary(e,t)}static fromJson(e,t){return(new fs).fromJson(e,t)}static fromJsonString(e,t){return(new fs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(fs,e,t)}}fs.runtime=Oe,fs.typeName="livekit.SimulateScenario",fs.fields=Oe.util.newFieldList((()=>[{no:1,name:"speaker_update",kind:"scalar",T:5,oneof:"scenario"},{no:2,name:"node_failure",kind:"scalar",T:8,oneof:"scenario"},{no:3,name:"migration",kind:"scalar",T:8,oneof:"scenario"},{no:4,name:"server_leave",kind:"scalar",T:8,oneof:"scenario"},{no:5,name:"switch_candidate_protocol",kind:"enum",T:Oe.getEnumType(Un),oneof:"scenario"},{no:6,name:"subscriber_bandwidth",kind:"scalar",T:3,oneof:"scenario"},{no:7,name:"disconnect_signal_on_resume",kind:"scalar",T:8,oneof:"scenario"},{no:8,name:"disconnect_signal_on_resume_no_messages",kind:"scalar",T:8,oneof:"scenario"}]));class vs extends w{constructor(e){super(),this.timestamp=j.zero,this.rtt=j.zero,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new vs).fromBinary(e,t)}static fromJson(e,t){return(new vs).fromJson(e,t)}static fromJsonString(e,t){return(new vs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(vs,e,t)}}vs.runtime=Oe,vs.typeName="livekit.Ping",vs.fields=Oe.util.newFieldList((()=>[{no:1,name:"timestamp",kind:"scalar",T:3},{no:2,name:"rtt",kind:"scalar",T:3}]));class ys extends w{constructor(e){super(),this.lastPingTimestamp=j.zero,this.timestamp=j.zero,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ys).fromBinary(e,t)}static fromJson(e,t){return(new ys).fromJson(e,t)}static fromJsonString(e,t){return(new ys).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ys,e,t)}}ys.runtime=Oe,ys.typeName="livekit.Pong",ys.fields=Oe.util.newFieldList((()=>[{no:1,name:"last_ping_timestamp",kind:"scalar",T:3},{no:2,name:"timestamp",kind:"scalar",T:3}]));class ks extends w{constructor(e){super(),this.regions=[],Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new ks).fromBinary(e,t)}static fromJson(e,t){return(new ks).fromJson(e,t)}static fromJsonString(e,t){return(new ks).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(ks,e,t)}}ks.runtime=Oe,ks.typeName="livekit.RegionSettings",ks.fields=Oe.util.newFieldList((()=>[{no:1,name:"regions",kind:"message",T:bs,repeated:!0}]));class bs extends w{constructor(e){super(),this.region="",this.url="",this.distance=j.zero,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new bs).fromBinary(e,t)}static fromJson(e,t){return(new bs).fromJson(e,t)}static fromJsonString(e,t){return(new bs).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(bs,e,t)}}bs.runtime=Oe,bs.typeName="livekit.RegionInfo",bs.fields=Oe.util.newFieldList((()=>[{no:1,name:"region",kind:"scalar",T:9},{no:2,name:"url",kind:"scalar",T:9},{no:3,name:"distance",kind:"scalar",T:3}]));class Ts extends w{constructor(e){super(),this.trackSid="",this.err=Ke.SE_UNKNOWN,Oe.util.initPartial(e,this)}static fromBinary(e,t){return(new Ts).fromBinary(e,t)}static fromJson(e,t){return(new Ts).fromJson(e,t)}static fromJsonString(e,t){return(new Ts).fromJsonString(e,t)}static equals(e,t){return Oe.util.equals(Ts,e,t)}}Ts.runtime=Oe,Ts.typeName="livekit.SubscriptionResponse",Ts.fields=Oe.util.newFieldList((()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"err",kind:"enum",T:Oe.getEnumType(Ke)}]));const Ss=[];class Cs extends Vt.EventEmitter{constructor(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var n;super(),this.attachedElements=[],this.isMuted=!1,this.streamState=Cs.StreamState.Active,this.isInBackground=!1,this._currentBitrate=0,this.log=c,this.appVisibilityChangedListener=()=>{this.backgroundTimeout&&clearTimeout(this.backgroundTimeout),"hidden"===document.visibilityState?this.backgroundTimeout=setTimeout((()=>this.handleAppVisibilityChanged()),5e3):this.handleAppVisibilityChanged()},this.log=d(null!==(n=i.loggerName)&&void 0!==n?n:o.Track),this.loggerContextCb=i.loggerContextCb,this.setMaxListeners(100),this.kind=t,this._mediaStreamTrack=e,this._mediaStreamID=e.id,this.source=Cs.Source.Unknown}get logContext(){var e;return Object.assign(Object.assign({},null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this)),_s(this))}get currentBitrate(){return this._currentBitrate}get mediaStreamTrack(){return this._mediaStreamTrack}get mediaStreamID(){return this._mediaStreamID}attach(t){let i="audio";this.kind===Cs.Kind.Video&&(i="video"),0===this.attachedElements.length&&Cs.Kind.Video&&this.addAppVisibilityListener(),t||("audio"===i&&(Ss.forEach((e=>{null!==e.parentElement||t||(t=e)})),t&&Ss.splice(Ss.indexOf(t),1)),t||(t=document.createElement(i))),this.attachedElements.includes(t)||this.attachedElements.push(t),Es(this.mediaStreamTrack,t);const n=t.srcObject.getTracks(),s=n.some((e=>"audio"===e.kind));return t.play().then((()=>{this.emit(s?e.TrackEvent.AudioPlaybackStarted:e.TrackEvent.VideoPlaybackStarted)})).catch((i=>{"NotAllowedError"===i.name?this.emit(s?e.TrackEvent.AudioPlaybackFailed:e.TrackEvent.VideoPlaybackFailed,i):"AbortError"===i.name?c.debug("".concat(s?"audio":"video"," playback aborted, likely due to new play request")):c.warn("could not playback ".concat(s?"audio":"video"),i),s&&t&&n.some((e=>"video"===e.kind))&&"NotAllowedError"===i.name&&(t.muted=!0,t.play().catch((()=>{})))})),this.emit(e.TrackEvent.ElementAttached,t),t}detach(t){try{if(t){ws(this.mediaStreamTrack,t);const i=this.attachedElements.indexOf(t);return i>=0&&(this.attachedElements.splice(i,1),this.recycleElement(t),this.emit(e.TrackEvent.ElementDetached,t)),t}const i=[];return this.attachedElements.forEach((t=>{ws(this.mediaStreamTrack,t),i.push(t),this.recycleElement(t),this.emit(e.TrackEvent.ElementDetached,t)})),this.attachedElements=[],i}finally{0===this.attachedElements.length&&this.removeAppVisibilityListener()}}stop(){this.stopMonitor(),this._mediaStreamTrack.stop()}enable(){this._mediaStreamTrack.enabled=!0}disable(){this._mediaStreamTrack.enabled=!1}stopMonitor(){this.monitorInterval&&clearInterval(this.monitorInterval)}updateLoggerOptions(e){e.loggerName&&(this.log=d(e.loggerName)),e.loggerContextCb&&(this.loggerContextCb=e.loggerContextCb)}recycleElement(e){if(e instanceof HTMLAudioElement){let t=!0;e.pause(),Ss.forEach((e=>{e.parentElement||(t=!1)})),t&&Ss.push(e)}}handleAppVisibilityChanged(){return Ct(this,void 0,void 0,(function*(){this.isInBackground="hidden"===document.visibilityState}))}addAppVisibilityListener(){Hs()?(this.isInBackground="hidden"===document.visibilityState,document.addEventListener("visibilitychange",this.appVisibilityChangedListener)):this.isInBackground=!1}removeAppVisibilityListener(){Hs()&&document.removeEventListener("visibilitychange",this.appVisibilityChangedListener)}}function Es(e,t){let i,n;i=t.srcObject instanceof MediaStream?t.srcObject:new MediaStream,n="audio"===e.kind?i.getAudioTracks():i.getVideoTracks(),n.includes(e)||(n.forEach((e=>{i.removeTrack(e)})),i.addTrack(e)),Ks()&&t instanceof HTMLVideoElement||(t.autoplay=!0),t.muted=0===i.getAudioTracks().length,t instanceof HTMLVideoElement&&(t.playsInline=!0),t.srcObject!==i&&(t.srcObject=i,(Ks()||qs())&&t instanceof HTMLVideoElement&&setTimeout((()=>{t.srcObject=i,t.play().catch((()=>{}))}),0))}function ws(e,t){if(t.srcObject instanceof MediaStream){const i=t.srcObject;i.removeTrack(e),i.getTracks().length>0?t.srcObject=i:t.srcObject=null}}function Ps(e,t,i){var n;const s=null!==(n=function(e){if(void 0!==e)return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e))&&void 0!==n?n:{};return!0===s.audio&&(s.audio={}),!0===s.video&&(s.video={}),s.audio&&Rs(s.audio,t),s.video&&Rs(s.video,i),s}function Rs(e,t){return Object.keys(t).forEach((i=>{void 0===e[i]&&(e[i]=t[i])})),e}function Is(e){const t={};if(e.video)if("object"==typeof e.video){const i={},n=i,s=e.video;Object.keys(s).forEach((e=>{if("resolution"===e)Rs(n,s.resolution);else n[e]=s[e]})),t.video=i}else t.video=e.video;else t.video=!1;return e.audio?"object"==typeof e.audio?t.audio=e.audio:t.audio=!0:t.audio=!1,t}function Os(){const e="undefined"!=typeof window&&(window.AudioContext||window.webkitAudioContext);if(e)return new e({latencyHint:"interactive"})}function Ds(e){var t,i;let n=null===(t=e.video)||void 0===t||t;return e.resolution&&e.resolution.width>0&&e.resolution.height>0&&(n="boolean"==typeof n?{}:n,n=Ks()?Object.assign(Object.assign({},n),{width:{max:e.resolution.width},height:{max:e.resolution.height},frameRate:e.resolution.frameRate}):Object.assign(Object.assign({},n),{width:{ideal:e.resolution.width},height:{ideal:e.resolution.height},frameRate:e.resolution.frameRate})),{audio:null!==(i=e.audio)&&void 0!==i&&i,video:n,controller:e.controller,selfBrowserSurface:e.selfBrowserSurface,surfaceSwitching:e.surfaceSwitching,systemAudio:e.systemAudio}}function xs(e){const t=e.split("/")[1].toLowerCase();if(!On.includes(t))throw Error("Video codec not supported: ".concat(t));return t}function Ns(e){const t=[];return e.forEach((e=>{void 0!==e.track&&t.push(new Gn({cid:e.track.mediaStreamID,track:e.trackInfo}))})),t}function _s(e){return e instanceof Cs?{trackSid:e.sid,trackSource:e.source,trackMuted:e.isMuted,trackEnabled:e.mediaStreamTrack.enabled,trackKind:e.kind}:{trackSid:e.trackSid,trackName:e.trackName,track:e.track?_s(e.track):void 0,trackEnabled:e.isEnabled,trackEncrypted:e.isEncrypted,trackMimeType:e.mimeType}}!function(e){let t,i,n;!function(e){e.Audio="audio",e.Video="video",e.Unknown="unknown"}(t=e.Kind||(e.Kind={})),function(e){e.Camera="camera",e.Microphone="microphone",e.ScreenShare="screen_share",e.ScreenShareAudio="screen_share_audio",e.Unknown="unknown"}(i=e.Source||(e.Source={})),function(e){e.Active="active",e.Paused="paused",e.Unknown="unknown"}(n=e.StreamState||(e.StreamState={})),e.kindToProto=function(e){switch(e){case t.Audio:return Ue.AUDIO;case t.Video:return Ue.VIDEO;default:return Ue.DATA}},e.kindFromProto=function(e){switch(e){case Ue.AUDIO:return t.Audio;case Ue.VIDEO:return t.Video;default:return t.Unknown}},e.sourceToProto=function(e){switch(e){case i.Camera:return je.CAMERA;case i.Microphone:return je.MICROPHONE;case i.ScreenShare:return je.SCREEN_SHARE;case i.ScreenShareAudio:return je.SCREEN_SHARE_AUDIO;default:return je.UNKNOWN}},e.sourceFromProto=function(e){switch(e){case je.CAMERA:return i.Camera;case je.MICROPHONE:return i.Microphone;case je.SCREEN_SHARE:return i.ScreenShare;case je.SCREEN_SHARE_AUDIO:return i.ScreenShareAudio;default:return i.Unknown}},e.streamStateFromProto=function(e){switch(e){case An.ACTIVE:return n.Active;case An.PAUSED:return n.Paused;default:return n.Unknown}}}(Cs||(Cs={}));const Ms="https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension";function Ls(e){return Ct(this,void 0,void 0,(function*(){return new Promise((t=>Pn.setTimeout(t,e)))}))}function As(){return"addTransceiver"in RTCPeerConnection.prototype}function Us(){return"addTrack"in RTCPeerConnection.prototype}function js(){if(!("getCapabilities"in RTCRtpSender))return!1;if(Ks())return!1;const e=RTCRtpSender.getCapabilities("video");let t=!1;if(e)for(const i of e.codecs)if("video/AV1"===i.mimeType){t=!0;break}return t}function Bs(){if(!("getCapabilities"in RTCRtpSender))return!1;if(qs())return!1;if(Ks()){const e=Sn();if((null==e?void 0:e.version)&&$s(e.version,"16")<0)return!1}const e=RTCRtpSender.getCapabilities("video");let t=!1;if(e)for(const i of e.codecs)if("video/VP9"===i.mimeType){t=!0;break}return t}function Fs(e){return"av1"===e||"vp9"===e}function Js(e){return!!document&&(e||(e=document.createElement("audio")),"setSinkId"in e)}const Vs={Chrome:"100",Safari:"15",Firefox:"100"};function qs(){var e;return"Firefox"===(null===(e=Sn())||void 0===e?void 0:e.name)}function Ks(){var e;return"Safari"===(null===(e=Sn())||void 0===e?void 0:e.name)}function Ws(){const e=Sn();return"Safari"===(null==e?void 0:e.name)&&e.version.startsWith("17.")}function Gs(){return!!Hs()&&/Tablet|iPad|Mobile|Android|BlackBerry/.test(navigator.userAgent)}function Hs(){return"undefined"!=typeof document}function zs(){return"ReactNative"==navigator.product}function Qs(e){return e.hostname.endsWith(".livekit.cloud")||e.hostname.endsWith(".livekit.run")}function Ys(){if(global&&global.LiveKitReactNativeGlobal)return global.LiveKitReactNativeGlobal}function Xs(){if(!zs())return;let e=Ys();return e?e.platform:void 0}function Zs(){if(Hs())return window.devicePixelRatio;if(zs()){let e=Ys();if(e)return e.devicePixelRatio}return 1}function $s(e,t){const i=e.split("."),n=t.split("."),s=Math.min(i.length,n.length);for(let e=0;e<s;++e){const t=parseInt(i[e],10),r=parseInt(n[e],10);if(t>r)return 1;if(t<r)return-1;if(e===s-1&&t===r)return 0}return""===e&&""!==t?-1:""===t?1:i.length==n.length?0:i.length<n.length?-1:1}function er(e){for(const t of e)t.target.handleResize(t)}function tr(e){for(const t of e)t.target.handleVisibilityChanged(t)}let ir=null;const nr=()=>(ir||(ir=new ResizeObserver(er)),ir);let sr=null;const rr=()=>(sr||(sr=new IntersectionObserver(tr,{root:null,rootMargin:"0px"})),sr);let or,ar;function cr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=document.createElement("canvas");s.width=e,s.height=t;const r=s.getContext("2d");null==r||r.fillRect(0,0,s.width,s.height),n&&r&&(r.beginPath(),r.arc(e/2,t/2,50,0,2*Math.PI,!0),r.closePath(),r.fillStyle="grey",r.fill());const o=s.captureStream(),[a]=o.getTracks();if(!a)throw Error("Could not get empty media stream video track");return a.enabled=i,a}function dr(){if(!ar){const e=new AudioContext,t=e.createOscillator(),i=e.createGain();i.gain.setValueAtTime(0,0);const n=e.createMediaStreamDestination();if(t.connect(i),i.connect(n),t.start(),[ar]=n.stream.getAudioTracks(),!ar)throw Error("Could not get empty media stream audio track");ar.enabled=!1}return ar.clone()}class lr{constructor(e,t){this.onFinally=t,this.promise=new Promise(((t,i)=>Ct(this,void 0,void 0,(function*(){this.resolve=t,this.reject=i,e&&(yield e(t,i))})))).finally((()=>{var e;return null===(e=this.onFinally)||void 0===e?void 0:e.call(this)}))}}class ur{constructor(){this._locking=Promise.resolve(),this._locks=0}isLocked(){return this._locks>0}lock(){let e;this._locks+=1;const t=new Promise((t=>e=()=>{this._locks-=1,t()})),i=this._locking.then((()=>e));return this._locking=this._locking.then((()=>t)),i}}function hr(e){if("string"==typeof e)return e;if(Array.isArray(e))return e[0];if(e.exact)return Array.isArray(e.exact)?e.exact[0]:e.exact;if(e.ideal)return Array.isArray(e.ideal)?e.ideal[0]:e.ideal;throw Error("could not unwrap constraint")}function pr(e){return e.startsWith("ws")?e.replace(/^(ws)/,"http"):e}const mr="default";class gr{static getInstance(){return void 0===this.instance&&(this.instance=new gr),this.instance}getDevices(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var i;return Ct(this,void 0,void 0,(function*(){if((null===(i=gr.userMediaPromiseMap)||void 0===i?void 0:i.size)>0){c.debug("awaiting getUserMedia promise");try{e?yield gr.userMediaPromiseMap.get(e):yield Promise.all(gr.userMediaPromiseMap.values())}catch(e){c.warn("error waiting for media permissons")}}let n=yield navigator.mediaDevices.enumerateDevices();if(t&&(!Ks()||!this.hasDeviceInUse(e))){if(0===n.length||n.some((t=>{const i=""===t.label,n=!e||t.kind===e;return i&&n}))){const t={video:"audioinput"!==e&&"audiooutput"!==e,audio:"videoinput"!==e},i=yield navigator.mediaDevices.getUserMedia(t);n=yield navigator.mediaDevices.enumerateDevices(),i.getTracks().forEach((e=>{e.stop()}))}}return e&&(n=n.filter((t=>t.kind===e))),n}))}normalizeDeviceId(e,t,i){return Ct(this,void 0,void 0,(function*(){if(t!==mr)return t;const n=(yield this.getDevices(e)).find((e=>e.groupId===i&&e.deviceId!==mr));return null==n?void 0:n.deviceId}))}hasDeviceInUse(e){return e?gr.userMediaPromiseMap.has(e):gr.userMediaPromiseMap.size>0}}gr.mediaDeviceKinds=["audioinput","audiooutput","videoinput"],gr.userMediaPromiseMap=new Map;class fr extends Cs{get constraints(){return this._constraints}constructor(t,i,n){let s=arguments.length>3&&void 0!==arguments[3]&&arguments[3];super(t,i,arguments.length>4?arguments[4]:void 0),this._isUpstreamPaused=!1,this.handleTrackMuteEvent=()=>this.debouncedTrackMuteHandler().catch((()=>this.log.debug("track mute bounce got cancelled by an unmute event",this.logContext))),this.debouncedTrackMuteHandler=kn((()=>Ct(this,void 0,void 0,(function*(){yield this.pauseUpstream()}))),5e3),this.handleTrackUnmuteEvent=()=>Ct(this,void 0,void 0,(function*(){this.debouncedTrackMuteHandler.cancel("unmute"),yield this.resumeUpstream()})),this.handleEnded=()=>{this.isInBackground&&(this.reacquireTrack=!0),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),this.emit(e.TrackEvent.Ended,this)},this.reacquireTrack=!1,this.providedByUser=s,this.muteLock=new ur,this.pauseUpstreamLock=new ur,this.processorLock=new ur,this.setMediaStreamTrack(t,!0),this._constraints=t.getConstraints(),n&&(this._constraints=n)}get id(){return this._mediaStreamTrack.id}get dimensions(){if(this.kind!==Cs.Kind.Video)return;const{width:e,height:t}=this._mediaStreamTrack.getSettings();return e&&t?{width:e,height:t}:void 0}get isUpstreamPaused(){return this._isUpstreamPaused}get isUserProvided(){return this.providedByUser}get mediaStreamTrack(){var e,t;return null!==(t=null===(e=this.processor)||void 0===e?void 0:e.processedTrack)&&void 0!==t?t:this._mediaStreamTrack}setMediaStreamTrack(e,t){return Ct(this,void 0,void 0,(function*(){if(e===this._mediaStreamTrack&&!t)return;let i;if(this._mediaStreamTrack&&(this.attachedElements.forEach((e=>{ws(this._mediaStreamTrack,e)})),this.debouncedTrackMuteHandler.cancel("new-track"),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent)),this.mediaStream=new MediaStream([e]),e&&(e.addEventListener("ended",this.handleEnded),e.addEventListener("mute",this.handleTrackMuteEvent),e.addEventListener("unmute",this.handleTrackUnmuteEvent),this._constraints=e.getConstraints()),this.processor&&e&&this.processorElement){if(this.log.debug("restarting processor",this.logContext),"unknown"===this.kind)throw TypeError("cannot set processor on track of unknown kind");Es(e,this.processorElement),this.processorElement.muted=!0,yield this.processor.restart({track:e,kind:this.kind,element:this.processorElement}),i=this.processor.processedTrack}this.sender&&(yield this.sender.replaceTrack(null!=i?i:e)),this.providedByUser||this._mediaStreamTrack===e||this._mediaStreamTrack.stop(),this._mediaStreamTrack=e,e&&(this._mediaStreamTrack.enabled=!this.isMuted,yield this.resumeUpstream(),this.attachedElements.forEach((t=>{Es(null!=i?i:e,t)})))}))}waitForDimensions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;var t;return Ct(this,void 0,void 0,(function*(){if(this.kind===Cs.Kind.Audio)throw new Error("cannot get dimensions for audio tracks");"iOS"===(null===(t=Sn())||void 0===t?void 0:t.os)&&(yield Ls(10));const i=Date.now();for(;Date.now()-i<e;){const e=this.dimensions;if(e)return e;yield Ls(50)}throw new ln("unable to get track dimensions after timeout")}))}getDeviceId(){return Ct(this,void 0,void 0,(function*(){if(this.source===Cs.Source.ScreenShare)return;const{deviceId:e,groupId:t}=this._mediaStreamTrack.getSettings(),i=this.kind===Cs.Kind.Audio?"audioinput":"videoinput";return gr.getInstance().normalizeDeviceId(i,e,t)}))}mute(){return Ct(this,void 0,void 0,(function*(){return this.setTrackMuted(!0),this}))}unmute(){return Ct(this,void 0,void 0,(function*(){return this.setTrackMuted(!1),this}))}replaceTrack(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Ct(this,void 0,void 0,(function*(){if(!this.sender)throw new ln("unable to replace an unpublished track");return this.log.debug("replace MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(e),this.providedByUser=t,this.processor&&(yield this.stopProcessor()),this}))}restart(t){return Ct(this,void 0,void 0,(function*(){t||(t=this._constraints),this.log.debug("restarting track with constraints",Object.assign(Object.assign({},this.logContext),{constraints:t}));const i={audio:!1,video:!1};this.kind===Cs.Kind.Video?i.video=t:i.audio=t,this.attachedElements.forEach((e=>{ws(this.mediaStreamTrack,e)})),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.stop();const n=(yield navigator.mediaDevices.getUserMedia(i)).getTracks()[0];return n.addEventListener("ended",this.handleEnded),this.log.debug("re-acquired MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(n),this._constraints=t,this.emit(e.TrackEvent.Restarted,this),this}))}setTrackMuted(t){this.log.debug("setting ".concat(this.kind," track ").concat(t?"muted":"unmuted"),this.logContext),this.isMuted===t&&this._mediaStreamTrack.enabled!==t||(this.isMuted=t,this._mediaStreamTrack.enabled=!t,this.emit(t?e.TrackEvent.Muted:e.TrackEvent.Unmuted,this))}get needsReAcquisition(){return"live"!==this._mediaStreamTrack.readyState||this._mediaStreamTrack.muted||!this._mediaStreamTrack.enabled||this.reacquireTrack}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Ct(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),Gs()&&(this.log.debug("visibility changed, is in Background: ".concat(this.isInBackground),this.logContext),this.isInBackground||!this.needsReAcquisition||this.isUserProvided||this.isMuted||(this.log.debug("track needs to be reacquired, restarting ".concat(this.source),this.logContext),yield this.restart(),this.reacquireTrack=!1))}))}stop(){var e;super.stop(),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),null===(e=this.processor)||void 0===e||e.destroy(),this.processor=void 0}pauseUpstream(){return Ct(this,void 0,void 0,(function*(){const t=yield this.pauseUpstreamLock.lock();try{if(!0===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to pause upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!0,this.emit(e.TrackEvent.UpstreamPaused,this);const t=Sn();if("Safari"===(null==t?void 0:t.name)&&$s(t.version,"12.0")<0)throw new dn("pauseUpstream is not supported on Safari < 12.");yield this.sender.replaceTrack(null)}finally{t()}}))}resumeUpstream(){return Ct(this,void 0,void 0,(function*(){const t=yield this.pauseUpstreamLock.lock();try{if(!1===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to resume upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!1,this.emit(e.TrackEvent.UpstreamResumed,this),yield this.sender.replaceTrack(this._mediaStreamTrack)}finally{t()}}))}getRTCStatsReport(){var e;return Ct(this,void 0,void 0,(function*(){if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return;return yield this.sender.getStats()}))}setProcessor(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var i,n;return Ct(this,void 0,void 0,(function*(){const s=yield this.processorLock.lock();try{if(this.log.debug("setting up processor",this.logContext),this.processor&&(yield this.stopProcessor()),"unknown"===this.kind)throw TypeError("cannot set processor on track of unknown kind");this.processorElement=null!==(i=this.processorElement)&&void 0!==i?i:document.createElement(this.kind),Es(this._mediaStreamTrack,this.processorElement),this.processorElement.muted=!0,this.processorElement.play().catch((e=>this.log.error("failed to play processor element",Object.assign(Object.assign({},this.logContext),{error:e}))));const s={kind:this.kind,track:this._mediaStreamTrack,element:this.processorElement};if(yield e.init(s),this.processor=e,this.processor.processedTrack){for(const e of this.attachedElements)e!==this.processorElement&&t&&(ws(this._mediaStreamTrack,e),Es(this.processor.processedTrack,e));yield null===(n=this.sender)||void 0===n?void 0:n.replaceTrack(this.processor.processedTrack)}}finally{s()}}))}getProcessor(){return this.processor}stopProcessor(){var e,t;return Ct(this,void 0,void 0,(function*(){this.processor&&(this.log.debug("stopping processor",this.logContext),null===(e=this.processor.processedTrack)||void 0===e||e.stop(),yield this.processor.destroy(),this.processor=void 0,null===(t=this.processorElement)||void 0===t||t.remove(),this.processorElement=void 0,yield this.restart())}))}}class vr extends Vt.EventEmitter{constructor(t){super(),this.onWorkerMessage=t=>{var i,n;const{kind:s,data:r}=t.data;switch(s){case"error":c.error(r.error.message),this.emit(e.EncryptionEvent.EncryptionError,r.error);break;case"initAck":r.enabled&&this.keyProvider.getKeys().forEach((e=>{this.postKey(e)}));break;case"enable":if(this.encryptionEnabled!==r.enabled&&r.participantIdentity===(null===(i=this.room)||void 0===i?void 0:i.localParticipant.identity))this.emit(e.EncryptionEvent.ParticipantEncryptionStatusChanged,r.enabled,this.room.localParticipant),this.encryptionEnabled=r.enabled;else if(r.participantIdentity){const t=null===(n=this.room)||void 0===n?void 0:n.getParticipantByIdentity(r.participantIdentity);if(!t)throw TypeError("couldn't set encryption status, participant not found".concat(r.participantIdentity));this.emit(e.EncryptionEvent.ParticipantEncryptionStatusChanged,r.enabled,t)}this.encryptionEnabled&&this.keyProvider.getKeys().forEach((e=>{this.postKey(e)}));break;case"ratchetKey":this.keyProvider.emit(e.KeyProviderEvent.KeyRatcheted,r.material,r.keyIndex)}},this.onWorkerError=t=>{c.error("e2ee worker encountered an error:",{error:t.error}),this.emit(e.EncryptionEvent.EncryptionError,t.error)},this.keyProvider=t.keyProvider,this.worker=t.worker,this.encryptionEnabled=!1}setup(e){if(!$i())throw new dn("tried to setup end-to-end encryption on an unsupported browser");if(c.info("setting up e2ee"),e!==this.room){this.room=e,this.setupEventListeners(e,this.keyProvider);const t={kind:"init",data:{keyProviderOptions:this.keyProvider.getOptions()}};this.worker&&(c.info("initializing worker",{worker:this.worker}),this.worker.onmessage=this.onWorkerMessage,this.worker.onerror=this.onWorkerError,this.worker.postMessage(t))}}setParticipantCryptorEnabled(e,t){c.debug("set e2ee to ".concat(e," for participant ").concat(t)),this.postEnable(e,t)}setSifTrailer(e){e&&0!==e.length?this.postSifTrailer(e):c.warn("ignoring server sent trailer as it's empty")}setupEngine(t){t.on(e.EngineEvent.RTPVideoMapUpdate,(e=>{this.postRTPMap(e)}))}setupEventListeners(t,i){t.on(e.RoomEvent.TrackPublished,((e,t)=>this.setParticipantCryptorEnabled(e.trackInfo.encryption!==$e.NONE,t.identity))),t.on(e.RoomEvent.ConnectionStateChanged,(i=>{i===e.ConnectionState.Connected&&t.participants.forEach((e=>{e.tracks.forEach((t=>{this.setParticipantCryptorEnabled(t.trackInfo.encryption!==$e.NONE,e.identity)}))}))})).on(e.RoomEvent.TrackUnsubscribed,((e,t,i)=>{var n;const s={kind:"removeTransform",data:{participantIdentity:i.identity,trackId:e.mediaStreamID}};null===(n=this.worker)||void 0===n||n.postMessage(s)})).on(e.RoomEvent.TrackSubscribed,((e,t,i)=>{this.setupE2EEReceiver(e,i.identity,t.trackInfo)})).on(e.RoomEvent.SignalConnected,(()=>{if(!this.room)throw new TypeError("expected room to be present on signal connect");this.setParticipantCryptorEnabled(this.room.localParticipant.isE2EEEnabled,this.room.localParticipant.identity),i.getKeys().forEach((e=>{this.postKey(e)}))})),t.localParticipant.on(e.ParticipantEvent.LocalTrackPublished,(e=>Ct(this,void 0,void 0,(function*(){this.setupE2EESender(e.track,e.track.sender)})))),i.on(e.KeyProviderEvent.SetKey,(e=>this.postKey(e))).on(e.KeyProviderEvent.RatchetRequest,((e,t)=>this.postRatchetRequest(e,t)))}postRatchetRequest(e,t){if(!this.worker)throw Error("could not ratchet key, worker is missing");const i={kind:"ratchetRequest",data:{participantIdentity:e,keyIndex:t}};this.worker.postMessage(i)}postKey(e){let{key:t,participantIdentity:i,keyIndex:n}=e;var s;if(!this.worker)throw Error("could not set key, worker is missing");const r={kind:"setKey",data:{participantIdentity:i,isPublisher:i===(null===(s=this.room)||void 0===s?void 0:s.localParticipant.identity),key:t,keyIndex:n}};this.worker.postMessage(r)}postEnable(e,t){if(!this.worker)throw new ReferenceError("failed to enable e2ee, worker is not ready");{const i={kind:"enable",data:{enabled:e,participantIdentity:t}};this.worker.postMessage(i)}}postRTPMap(e){var t;if(!this.worker)throw TypeError("could not post rtp map, worker is missing");if(!(null===(t=this.room)||void 0===t?void 0:t.localParticipant.identity))throw TypeError("could not post rtp map, local participant identity is missing");const i={kind:"setRTPMap",data:{map:e,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(i)}postSifTrailer(e){if(!this.worker)throw Error("could not post SIF trailer, worker is missing");const t={kind:"setSifTrailer",data:{trailer:e}};this.worker.postMessage(t)}setupE2EEReceiver(e,t,i){if(e.receiver){if(!(null==i?void 0:i.mimeType)||""===i.mimeType)throw new TypeError("MimeType missing from trackInfo, cannot set up E2EE cryptor");this.handleReceiver(e.receiver,e.mediaStreamID,t,"video"===e.kind?xs(i.mimeType):void 0)}}setupE2EESender(e,t){e instanceof fr&&t?this.handleSender(t,e.mediaStreamID,void 0):t||c.warn("early return because sender is not ready")}handleReceiver(e,t,i,n){return Ct(this,void 0,void 0,(function*(){if(this.worker){if(en()){const s={kind:"decode",participantIdentity:i,trackId:t,codec:n};e.transform=new RTCRtpScriptTransform(this.worker,s)}else{if(Qi in e&&n){const e={kind:"updateCodec",data:{trackId:t,codec:n,participantIdentity:i}};return void this.worker.postMessage(e)}let s=e.writableStream,r=e.readableStream;if(!s||!r){const t=e.createEncodedStreams();e.writableStream=t.writable,s=t.writable,e.readableStream=t.readable,r=t.readable}const o={kind:"decode",data:{readableStream:r,writableStream:s,trackId:t,codec:n,participantIdentity:i}};this.worker.postMessage(o,[r,s])}e[Qi]=!0}}))}handleSender(e,t,i){var n;if(!(Qi in e)&&this.worker){if(!(null===(n=this.room)||void 0===n?void 0:n.localParticipant.identity)||""===this.room.localParticipant.identity)throw TypeError("local identity needs to be known in order to set up encrypted sender");if(en()){c.info("initialize script transform");const n={kind:"encode",participantIdentity:this.room.localParticipant.identity,trackId:t,codec:i};e.transform=new RTCRtpScriptTransform(this.worker,n)}else{c.info("initialize encoded streams");const n=e.createEncodedStreams(),s={kind:"encode",data:{readableStream:n.readable,writableStream:n.writable,codec:i,trackId:t,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(s,[n.readable,n.writable])}e[Qi]=!0}}}var yr;!function(e){e[e.WAITING=0]="WAITING",e[e.RUNNING=1]="RUNNING",e[e.COMPLETED=2]="COMPLETED"}(yr||(yr={}));class kr{constructor(){this.pendingTasks=new Map,this.taskMutex=new ur,this.nextTaskIndex=0}run(e){return Ct(this,void 0,void 0,(function*(){const t={id:this.nextTaskIndex++,enqueuedAt:Date.now(),status:yr.WAITING};this.pendingTasks.set(t.id,t);const i=yield this.taskMutex.lock();try{return t.executedAt=Date.now(),t.status=yr.RUNNING,yield e()}finally{t.status=yr.COMPLETED,this.pendingTasks.delete(t.id),i()}}))}flush(){return Ct(this,void 0,void 0,(function*(){return this.run((()=>Ct(this,void 0,void 0,(function*(){}))))}))}snapshot(){return Array.from(this.pendingTasks.values())}}const br=["syncState","trickle","offer","answer","simulate","leave"];var Tr;!function(e){e[e.CONNECTING=0]="CONNECTING",e[e.CONNECTED=1]="CONNECTED",e[e.RECONNECTING=2]="RECONNECTING",e[e.DISCONNECTING=3]="DISCONNECTING",e[e.DISCONNECTED=4]="DISCONNECTED"}(Tr||(Tr={}));class Sr{get currentState(){return this.state}get isDisconnected(){return this.state===Tr.DISCONNECTING||this.state===Tr.DISCONNECTED}get isEstablishingConnection(){return this.state===Tr.CONNECTING||this.state===Tr.RECONNECTING}constructor(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var i;this.rtt=0,this.state=Tr.DISCONNECTED,this.log=c,this.resetCallbacks=()=>{this.onAnswer=void 0,this.onLeave=void 0,this.onLocalTrackPublished=void 0,this.onLocalTrackUnpublished=void 0,this.onNegotiateRequested=void 0,this.onOffer=void 0,this.onRemoteMuteChanged=void 0,this.onSubscribedQualityUpdate=void 0,this.onTokenRefresh=void 0,this.onTrickle=void 0,this.onClose=void 0},this.log=d(null!==(i=t.loggerName)&&void 0!==i?i:o.Signal),this.loggerContextCb=t.loggerContextCb,this.useJSON=e,this.requestQueue=new kr,this.queuedRequests=[],this.closingLock=new ur,this.connectionLock=new ur,this.state=Tr.DISCONNECTED}get logContext(){var e,t;return null!==(t=null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this))&&void 0!==t?t:{}}join(e,t,i,n){return Ct(this,void 0,void 0,(function*(){this.state=Tr.CONNECTING,this.options=i;return yield this.connect(e,t,i,n)}))}reconnect(e,t,i,n){return Ct(this,void 0,void 0,(function*(){if(!this.options)return void this.log.warn("attempted to reconnect without signal options being set, ignoring",this.logContext);this.state=Tr.RECONNECTING,this.clearPingInterval();return yield this.connect(e,t,Object.assign(Object.assign({},this.options),{reconnect:!0,sid:i,reconnectReason:n}))}))}connect(e,t,i,n){this.connectOptions=i,e=(e=function(e){return e.startsWith("http")?e.replace(/^(http)/,"ws"):e}(e)).replace(/\/$/,""),e+="/rtc";const s=function(e,t,i){var n;const s=new URLSearchParams;s.set("access_token",e),i.reconnect&&(s.set("reconnect","1"),i.sid&&s.set("sid",i.sid));s.set("auto_subscribe",i.autoSubscribe?"1":"0"),s.set("sdk",zs()?"reactnative":"js"),s.set("version",t.version),s.set("protocol",t.protocol.toString()),t.deviceModel&&s.set("device_model",t.deviceModel);t.os&&s.set("os",t.os);t.osVersion&&s.set("os_version",t.osVersion);t.browser&&s.set("browser",t.browser);t.browserVersion&&s.set("browser_version",t.browserVersion);void 0!==i.publishOnly&&s.set("publish",i.publishOnly);i.adaptiveStream&&s.set("adaptive_stream","1");i.reconnectReason&&s.set("reconnect_reason",i.reconnectReason.toString());(null===(n=navigator.connection)||void 0===n?void 0:n.type)&&s.set("network",navigator.connection.type);return"?".concat(s.toString())}(t,function(){var e;const t=new pt({sdk:it.JS,protocol:11,version:wn});return zs()&&(t.os=null!==(e=Xs())&&void 0!==e?e:""),t}(),i);return new Promise(((t,r)=>Ct(this,void 0,void 0,(function*(){const o=yield this.connectionLock.lock();try{const o=()=>Ct(this,void 0,void 0,(function*(){this.close(),clearTimeout(a),r(new cn("room connection has been cancelled (signal)"))})),a=setTimeout((()=>{this.close(),r(new cn("room connection has timed out (signal)"))}),i.websocketTimeout);(null==n?void 0:n.aborted)&&o(),null==n||n.addEventListener("abort",o),this.log.debug("connecting to ".concat(e+s),this.logContext),this.ws&&(yield this.close(!1)),this.ws=new WebSocket(e+s),this.ws.binaryType="arraybuffer",this.ws.onopen=()=>{clearTimeout(a)},this.ws.onerror=t=>Ct(this,void 0,void 0,(function*(){if(this.state===Tr.CONNECTED)this.handleWSError(t);else{clearTimeout(a);try{const t=yield fetch("http".concat(e.substring(2),"/validate").concat(s));if(t.status.toFixed(0).startsWith("4")){const e=yield t.text();r(new cn(e,0,t.status))}else r(new cn("Internal error",2,t.status))}catch(e){r(new cn("server was not reachable",1))}}})),this.ws.onmessage=e=>Ct(this,void 0,void 0,(function*(){var s,a,c,d;let l;if("string"==typeof e.data){const t=JSON.parse(e.data);l=Bn.fromJson(t)}else{if(!(e.data instanceof ArrayBuffer))return void this.log.error("could not decode websocket message: ".concat(typeof e.data),this.logContext);l=Bn.fromBinary(new Uint8Array(e.data))}if(this.state!==Tr.CONNECTED){let e=!1;if("join"===(null===(s=l.message)||void 0===s?void 0:s.case)?(this.state=Tr.CONNECTED,null==n||n.removeEventListener("abort",o),this.pingTimeoutDuration=l.message.value.pingTimeout,this.pingIntervalDuration=l.message.value.pingInterval,this.pingTimeoutDuration&&this.pingTimeoutDuration>0&&(this.log.debug("ping config",Object.assign(Object.assign({},this.logContext),{timeout:this.pingTimeoutDuration,interval:this.pingIntervalDuration})),this.startPingInterval()),t(l.message.value)):this.state===Tr.RECONNECTING&&"leave"!==l.message.case?(this.state=Tr.CONNECTED,null==n||n.removeEventListener("abort",o),this.startPingInterval(),"reconnect"===(null===(a=l.message)||void 0===a?void 0:a.case)?t(null===(c=l.message)||void 0===c?void 0:c.value):(t(),e=!0)):this.isEstablishingConnection&&"leave"===l.message.case?r(new cn("Received leave request while trying to (re)connect",4)):i.reconnect||r(new cn("did not receive join response, got ".concat(null===(d=l.message)||void 0===d?void 0:d.case," instead"))),!e)return}this.signalLatency&&(yield Ls(this.signalLatency)),this.handleSignalResponse(l)})),this.ws.onclose=e=>{this.isEstablishingConnection&&r(new cn("Websocket got closed during a (re)connection attempt")),this.log.warn("websocket closed",Object.assign(Object.assign({},this.logContext),{reason:e.reason,state:this.state})),this.handleOnClose(e.reason)}}finally{o()}}))))}close(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return Ct(this,void 0,void 0,(function*(){const t=yield this.closingLock.lock();try{if(e&&(this.state=Tr.DISCONNECTING),this.ws){this.ws.onmessage=null,this.ws.onopen=null,this.ws.onclose=null;const e=new Promise((e=>{this.ws?this.ws.onclose=()=>{e()}:e()}));this.ws.readyState<this.ws.CLOSING&&(this.ws.close(),yield Promise.race([e,Ls(250)])),this.ws=void 0}}finally{e&&(this.state=Tr.DISCONNECTED),this.clearPingInterval(),t()}}))}sendOffer(e){this.log.debug("sending offer",Object.assign(Object.assign({},this.logContext),{offerSdp:e.sdp})),this.sendRequest({case:"offer",value:Er(e)})}sendAnswer(e){return this.log.debug("sending answer",Object.assign(Object.assign({},this.logContext),{answerSdp:e.sdp})),this.sendRequest({case:"answer",value:Er(e)})}sendIceCandidate(e,t){return this.log.trace("sending ice candidate",Object.assign(Object.assign({},this.logContext),{candidate:e})),this.sendRequest({case:"trickle",value:new Vn({candidateInit:JSON.stringify(e),target:t})})}sendMuteTrack(e,t){return this.sendRequest({case:"mute",value:new qn({sid:e,muted:t})})}sendAddTrack(e){return this.sendRequest({case:"addTrack",value:e})}sendUpdateLocalMetadata(e,t){return this.sendRequest({case:"updateMetadata",value:new es({metadata:e,name:t})})}sendUpdateTrackSettings(e){this.sendRequest({case:"trackSetting",value:e})}sendUpdateSubscription(e){return this.sendRequest({case:"subscription",value:e})}sendSyncState(e){return this.sendRequest({case:"syncState",value:e})}sendUpdateVideoLayers(e,t){return this.sendRequest({case:"updateLayers",value:new $n({trackSid:e,layers:t})})}sendUpdateSubscriptionPermissions(e,t){return this.sendRequest({case:"subscriptionPermission",value:new hs({allParticipants:e,trackPermissions:t})})}sendSimulateScenario(e){return this.sendRequest({case:"simulate",value:e})}sendPing(){return Promise.all([this.sendRequest({case:"ping",value:j.parse(Date.now())}),this.sendRequest({case:"pingReq",value:new vs({timestamp:j.parse(Date.now()),rtt:j.parse(this.rtt)})})])}sendLeave(){return this.sendRequest({case:"leave",value:new Zn({canReconnect:!1,reason:e.DisconnectReason.CLIENT_INITIATED})})}sendRequest(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Ct(this,void 0,void 0,(function*(){const i=!t&&!function(e){const t=br.indexOf(e.case)>=0;return c.trace("request allowed to bypass queue:",{canPass:t,req:e}),t}(e);if(i&&this.state===Tr.RECONNECTING)return void this.queuedRequests.push((()=>Ct(this,void 0,void 0,(function*(){yield this.sendRequest(e,!0)}))));if(t||(yield this.requestQueue.flush()),this.signalLatency&&(yield Ls(this.signalLatency)),!this.ws||this.ws.readyState!==this.ws.OPEN)return void this.log.error("cannot send signal request before connected, type: ".concat(null==e?void 0:e.case),this.logContext);const n=new jn({message:e});try{this.useJSON?this.ws.send(n.toJsonString()):this.ws.send(n.toBinary())}catch(e){this.log.error("error sending signal message",Object.assign(Object.assign({},this.logContext),{error:e}))}}))}handleSignalResponse(e){var t,i;const n=e.message;if(null==n)return void this.log.debug("received unsupported message",this.logContext);let s=!1;if("answer"===n.case){const e=Cr(n.value);this.onAnswer&&this.onAnswer(e)}else if("offer"===n.case){const e=Cr(n.value);this.onOffer&&this.onOffer(e)}else if("trickle"===n.case){const e=JSON.parse(n.value.candidateInit);this.onTrickle&&this.onTrickle(e,n.value.target)}else"update"===n.case?this.onParticipantUpdate&&this.onParticipantUpdate(null!==(t=n.value.participants)&&void 0!==t?t:[]):"trackPublished"===n.case?this.onLocalTrackPublished&&this.onLocalTrackPublished(n.value):"speakersChanged"===n.case?this.onSpeakersChanged&&this.onSpeakersChanged(null!==(i=n.value.speakers)&&void 0!==i?i:[]):"leave"===n.case?this.onLeave&&this.onLeave(n.value):"mute"===n.case?this.onRemoteMuteChanged&&this.onRemoteMuteChanged(n.value.sid,n.value.muted):"roomUpdate"===n.case?this.onRoomUpdate&&n.value.room&&this.onRoomUpdate(n.value.room):"connectionQuality"===n.case?this.onConnectionQuality&&this.onConnectionQuality(n.value):"streamStateUpdate"===n.case?this.onStreamStateUpdate&&this.onStreamStateUpdate(n.value):"subscribedQualityUpdate"===n.case?this.onSubscribedQualityUpdate&&this.onSubscribedQualityUpdate(n.value):"subscriptionPermissionUpdate"===n.case?this.onSubscriptionPermissionUpdate&&this.onSubscriptionPermissionUpdate(n.value):"refreshToken"===n.case?this.onTokenRefresh&&this.onTokenRefresh(n.value):"trackUnpublished"===n.case?this.onLocalTrackUnpublished&&this.onLocalTrackUnpublished(n.value):"subscriptionResponse"===n.case?this.onSubscriptionError&&this.onSubscriptionError(n.value):"pong"===n.case||("pongResp"===n.case?(this.rtt=Date.now()-Number.parseInt(n.value.lastPingTimestamp.toString()),this.resetPingTimeout(),s=!0):this.log.debug("unsupported message",Object.assign(Object.assign({},this.logContext),{msgCase:n.case})));s||this.resetPingTimeout()}setReconnected(){for(;this.queuedRequests.length>0;){const e=this.queuedRequests.shift();e&&this.requestQueue.run(e)}}handleOnClose(e){return Ct(this,void 0,void 0,(function*(){if(this.state===Tr.DISCONNECTED)return;const t=this.onClose;yield this.close(),this.log.debug("websocket connection closed: ".concat(e),Object.assign(Object.assign({},this.logContext),{reason:e})),t&&t(e)}))}handleWSError(e){this.log.error("websocket error",Object.assign(Object.assign({},this.logContext),{error:e}))}resetPingTimeout(){this.clearPingTimeout(),this.pingTimeoutDuration?this.pingTimeout=Pn.setTimeout((()=>{this.log.warn("ping timeout triggered. last pong received at: ".concat(new Date(Date.now()-1e3*this.pingTimeoutDuration).toUTCString()),this.logContext),this.handleOnClose("ping timeout")}),1e3*this.pingTimeoutDuration):this.log.warn("ping timeout duration not set",this.logContext)}clearPingTimeout(){this.pingTimeout&&Pn.clearTimeout(this.pingTimeout)}startPingInterval(){this.clearPingInterval(),this.resetPingTimeout(),this.pingIntervalDuration?(this.log.debug("start ping interval",this.logContext),this.pingInterval=Pn.setInterval((()=>{this.sendPing()}),1e3*this.pingIntervalDuration)):this.log.warn("ping interval duration not set",this.logContext)}clearPingInterval(){this.log.debug("clearing ping interval",this.logContext),this.clearPingTimeout(),this.pingInterval&&Pn.clearInterval(this.pingInterval)}}function Cr(e){const t={type:"offer",sdp:e.sdp};switch(e.type){case"answer":case"offer":case"pranswer":case"rollback":t.type=e.type}return t}function Er(e){return new zn({sdp:e.sdp,type:e.type})}var wr={},Pr={exports:{}},Rr=Pr.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":""}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(Rr).forEach((function(e){Rr[e].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}));var Ir=Pr.exports;!function(e){var t=function(e){return String(Number(e))===e?Number(e):e},i=function(e,i,n){var s=e.name&&e.names;e.push&&!i[e.push]?i[e.push]=[]:s&&!i[e.name]&&(i[e.name]={});var r=e.push?{}:s?i[e.name]:i;!function(e,i,n,s){if(s&&!n)i[s]=t(e[1]);else for(var r=0;r<n.length;r+=1)null!=e[r+1]&&(i[n[r]]=t(e[r+1]))}(n.match(e.reg),r,e.names,e.name),e.push&&i[e.push].push(r)},n=Ir,s=RegExp.prototype.test.bind(/^([a-z])=(.*)/);e.parse=function(e){var t={},r=[],o=t;return e.split(/(\r\n|\r|\n)/).filter(s).forEach((function(e){var t=e[0],s=e.slice(2);"m"===t&&(r.push({rtp:[],fmtp:[]}),o=r[r.length-1]);for(var a=0;a<(n[t]||[]).length;a+=1){var c=n[t][a];if(c.reg.test(s))return i(c,o,s)}})),t.media=r,t};var r=function(e,i){var n=i.split(/=(.+)/,2);return 2===n.length?e[n[0]]=t(n[1]):1===n.length&&i.length>1&&(e[n[0]]=void 0),e};e.parseParams=function(e){return e.split(/;\s?/).reduce(r,{})},e.parseFmtpConfig=e.parseParams,e.parsePayloads=function(e){return e.toString().split(" ").map(Number)},e.parseRemoteCandidates=function(e){for(var i=[],n=e.split(" ").map(t),s=0;s<n.length;s+=3)i.push({component:n[s],ip:n[s+1],port:n[s+2]});return i},e.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(r,{})}))},e.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var i,n=!1;return"~"!==e[0]?i=t(e):(i=t(e.substring(1,e.length)),n=!0),{scid:i,paused:n}}))}))}}(wr);var Or=Ir,Dr=/%[sdv%]/g,xr=function(e){var t=1,i=arguments,n=i.length;return e.replace(Dr,(function(e){if(t>=n)return e;var s=i[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(s);case"%d":return Number(s);case"%v":return""}}))},Nr=function(e,t,i){var n=[e+"="+(t.format instanceof Function?t.format(t.push?i:i[t.name]):t.format)];if(t.names)for(var s=0;s<t.names.length;s+=1){var r=t.names[s];t.name?n.push(i[t.name][r]):n.push(i[t.names[s]])}else n.push(i[t.name]);return xr.apply(null,n)},_r=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Mr=["i","c","b","a"],Lr=wr,Ar=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var i=t.outerOrder||_r,n=t.innerOrder||Mr,s=[];return i.forEach((function(t){Or[t].forEach((function(i){i.name in e&&null!=e[i.name]?s.push(Nr(t,i,e)):i.push in e&&null!=e[i.push]&&e[i.push].forEach((function(e){s.push(Nr(t,i,e))}))}))})),e.media.forEach((function(e){s.push(Nr("m",Or.m[0],e)),n.forEach((function(t){Or[t].forEach((function(i){i.name in e&&null!=e[i.name]?s.push(Nr(t,i,e)):i.push in e&&null!=e[i.push]&&e[i.push].forEach((function(e){s.push(Nr(t,i,e))}))}))}))})),s.join("\r\n")+"\r\n"},Ur=Ar,jr=Lr.parse;const Br="negotiationStarted",Fr="negotiationComplete",Jr="rtpVideoPayloadTypes";class Vr extends Vt.EventEmitter{get pc(){return this._pc||(this._pc=this.createPC()),this._pc}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var n;super(),this.log=c,this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate=!1,this.trackBitrates=[],this.remoteStereoMids=[],this.remoteNackMids=[],this.negotiate=kn((e=>Ct(this,void 0,void 0,(function*(){this.emit(Br);try{yield this.createAndSendOffer()}catch(t){if(!e)throw t;e(t)}}))),100),this.close=()=>{this._pc&&(this._pc.close(),this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc.onicegatheringstatechange=null,this._pc.ondatachannel=null,this._pc.onnegotiationneeded=null,this._pc.onsignalingstatechange=null,this._pc.onicecandidate=null,this._pc.ondatachannel=null,this._pc.ontrack=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null)},this.log=d(null!==(n=i.loggerName)&&void 0!==n?n:o.PCTransport),this.loggerOptions=i,this.config=e,this.mediaConstraints=t,this._pc=this.createPC()}createPC(){const e="Chrome"===(null===(t=Sn())||void 0===t?void 0:t.name)?new RTCPeerConnection(this.config,this.mediaConstraints):new RTCPeerConnection(this.config);var t;return e.onicecandidate=e=>{var t;e.candidate&&(null===(t=this.onIceCandidate)||void 0===t||t.call(this,e.candidate))},e.onicecandidateerror=e=>{var t;null===(t=this.onIceCandidateError)||void 0===t||t.call(this,e)},e.oniceconnectionstatechange=()=>{var t;null===(t=this.onIceConnectionStateChange)||void 0===t||t.call(this,e.iceConnectionState)},e.onsignalingstatechange=()=>{var t;null===(t=this.onSignalingStatechange)||void 0===t||t.call(this,e.signalingState)},e.onconnectionstatechange=()=>{var t;null===(t=this.onConnectionStateChange)||void 0===t||t.call(this,e.connectionState)},e.ondatachannel=e=>{var t;null===(t=this.onDataChannel)||void 0===t||t.call(this,e)},e.ontrack=e=>{var t;null===(t=this.onTrack)||void 0===t||t.call(this,e)},e}get logContext(){var e,t;return Object.assign({},null===(t=(e=this.loggerOptions).loggerContextCb)||void 0===t?void 0:t.call(e))}get isICEConnected(){return null!==this._pc&&("connected"===this.pc.iceConnectionState||"completed"===this.pc.iceConnectionState)}addIceCandidate(e){return Ct(this,void 0,void 0,(function*(){if(this.pc.remoteDescription&&!this.restartingIce)return this.pc.addIceCandidate(e);this.pendingCandidates.push(e)}))}setRemoteDescription(e){var t;return Ct(this,void 0,void 0,(function*(){let i;if("offer"===e.type){let{stereoMids:t,nackMids:i}=function(e){var t;const i=[],n=[],s=jr(null!==(t=e.sdp)&&void 0!==t?t:"");let r=0;return s.media.forEach((e=>{var t;"audio"===e.type&&(e.rtp.some((e=>"opus"===e.codec&&(r=e.payload,!0))),(null===(t=e.rtcpFb)||void 0===t?void 0:t.some((e=>e.payload===r&&"nack"===e.type)))&&n.push(e.mid),e.fmtp.some((t=>t.payload===r&&(t.config.includes("sprop-stereo=1")&&i.push(e.mid),!0))))})),{stereoMids:i,nackMids:n}}(e);this.remoteStereoMids=t,this.remoteNackMids=i}else if("answer"===e.type){const n=jr(null!==(t=e.sdp)&&void 0!==t?t:"");n.media.forEach((e=>{"audio"===e.type&&this.trackBitrates.some((t=>{if(!t.transceiver||e.mid!=t.transceiver.mid)return!1;let i=0;if(e.rtp.some((e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(i=e.payload,!0))),0===i)return!0;let n=!1;for(const s of e.fmtp)if(s.payload===i){s.config=s.config.split(";").filter((e=>!e.includes("maxaveragebitrate"))).join(";"),t.maxbr>0&&(s.config+=";maxaveragebitrate=".concat(1e3*t.maxbr)),n=!0;break}return n||t.maxbr>0&&e.fmtp.push({payload:i,config:"maxaveragebitrate=".concat(1e3*t.maxbr)}),!0}))})),i=Ur(n)}if(yield this.setMungedSDP(e,i,!0),this.pendingCandidates.forEach((e=>{this.pc.addIceCandidate(e)})),this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate)this.renegotiate=!1,yield this.createAndSendOffer();else if("answer"===e.type&&(this.emit(Fr),e.sdp)){jr(e.sdp).media.forEach((e=>{"video"===e.type&&this.emit(Jr,e.rtp)}))}}))}createAndSendOffer(e){var t;return Ct(this,void 0,void 0,(function*(){if(void 0===this.onOffer)return;if((null==e?void 0:e.iceRestart)&&(this.log.debug("restarting ICE",this.logContext),this.restartingIce=!0),this._pc&&"have-local-offer"===this._pc.signalingState){const t=this._pc.remoteDescription;if(!(null==e?void 0:e.iceRestart)||!t)return void(this.renegotiate=!0);yield this._pc.setRemoteDescription(t)}else if(!this._pc||"closed"===this._pc.signalingState)return void this.log.warn("could not createOffer with closed peer connection",this.logContext);this.log.debug("starting to negotiate",this.logContext);const i=yield this.pc.createOffer(e),n=jr(null!==(t=i.sdp)&&void 0!==t?t:"");n.media.forEach((e=>{"audio"===e.type?qr(e,[],[]):"video"===e.type&&(!function(e){var t,i,n,s;const r=null===(i=null===(t=e.rtp[0])||void 0===t?void 0:t.codec)||void 0===i?void 0:i.toLowerCase();if(!Fs(r))return;let o=0;const a=null===(n=e.ext)||void 0===n?void 0:n.some((e=>e.uri===Ms||(e.value>o&&(o=e.value),!1)));a||null===(s=e.ext)||void 0===s||s.push({value:o+1,uri:Ms})}(e),this.trackBitrates.some((t=>{if(!e.msid||!t.cid||!e.msid.includes(t.cid))return!1;let i=0;if(e.rtp.some((e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(i=e.payload,!0))),0===i)return!0;let n=!1;for(const s of e.fmtp)if(s.payload===i){s.config.includes("x-google-start-bitrate")||(s.config+=";x-google-start-bitrate=".concat(Math.round(.7*t.maxbr))),s.config.includes("x-google-max-bitrate")||(s.config+=";x-google-max-bitrate=".concat(t.maxbr)),n=!0;break}return n||e.fmtp.push({payload:i,config:"x-google-start-bitrate=".concat(Math.round(.7*t.maxbr),";x-google-max-bitrate=").concat(t.maxbr)}),!0})))})),yield this.setMungedSDP(i,Ur(n)),this.onOffer(i)}))}createAndSetAnswer(){var e;return Ct(this,void 0,void 0,(function*(){const t=yield this.pc.createAnswer(),i=jr(null!==(e=t.sdp)&&void 0!==e?e:"");return i.media.forEach((e=>{"audio"===e.type&&qr(e,this.remoteStereoMids,this.remoteNackMids)})),yield this.setMungedSDP(t,Ur(i)),t}))}createDataChannel(e,t){return this.pc.createDataChannel(e,t)}addTransceiver(e,t){return this.pc.addTransceiver(e,t)}addTrack(e){if(!this._pc)throw new hn("PC closed, cannot add track");return this._pc.addTrack(e)}setTrackCodecBitrate(e){this.trackBitrates.push(e)}setConfiguration(e){var t;if(!this._pc)throw new hn("PC closed, cannot configure");return null===(t=this._pc)||void 0===t?void 0:t.setConfiguration(e)}canRemoveTrack(){var e;return!!(null===(e=this._pc)||void 0===e?void 0:e.removeTrack)}removeTrack(e){var t;return null===(t=this._pc)||void 0===t?void 0:t.removeTrack(e)}getConnectionState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.connectionState)&&void 0!==t?t:"closed"}getICEConnectionState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.iceConnectionState)&&void 0!==t?t:"closed"}getSignallingState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.signalingState)&&void 0!==t?t:"closed"}getTransceivers(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.getTransceivers())&&void 0!==t?t:[]}getSenders(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.getSenders())&&void 0!==t?t:[]}getLocalDescription(){var e;return null===(e=this._pc)||void 0===e?void 0:e.localDescription}getRemoteDescription(){var e;return null===(e=this.pc)||void 0===e?void 0:e.remoteDescription}getStats(){return this.pc.getStats()}getConnectedAddress(){var e;return Ct(this,void 0,void 0,(function*(){if(!this._pc)return;let t="";const i=new Map,n=new Map;if((yield this._pc.getStats()).forEach((e=>{switch(e.type){case"transport":t=e.selectedCandidatePairId;break;case"candidate-pair":""===t&&e.selected&&(t=e.id),i.set(e.id,e);break;case"remote-candidate":n.set(e.id,"".concat(e.address,":").concat(e.port))}})),""===t)return;const s=null===(e=i.get(t))||void 0===e?void 0:e.remoteCandidateId;return void 0!==s?n.get(s):void 0}))}setMungedSDP(e,t,i){return Ct(this,void 0,void 0,(function*(){if(t){const n=e.sdp;e.sdp=t;try{return this.log.debug("setting munged ".concat(i?"remote":"local"," description"),this.logContext),void(i?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e))}catch(i){this.log.warn("not able to set ".concat(e.type,", falling back to unmodified sdp"),Object.assign(Object.assign({},this.logContext),{error:i,sdp:t})),e.sdp=n}}try{i?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e)}catch(t){let n="unknown error";t instanceof Error?n=t.message:"string"==typeof t&&(n=t);const s={error:n,sdp:e.sdp};throw!i&&this.pc.remoteDescription&&(s.remoteSdp=this.pc.remoteDescription),this.log.error("unable to set ".concat(e.type),Object.assign(Object.assign({},this.logContext),{fields:s})),new pn(n)}}))}}function qr(e,t,i){let n=0;e.rtp.some((e=>"opus"===e.codec&&(n=e.payload,!0))),n>0&&(e.rtcpFb||(e.rtcpFb=[]),i.includes(e.mid)&&!e.rtcpFb.some((e=>e.payload===n&&"nack"===e.type))&&e.rtcpFb.push({payload:n,type:"nack"}),t.includes(e.mid)&&e.fmtp.some((e=>e.payload===n&&(e.config.includes("stereo=1")||(e.config+=";stereo=1"),!0))))}const Kr="vp8",Wr={audioBitrate:e.AudioPresets.music.maxBitrate,audioPreset:e.AudioPresets.music,dtx:!0,red:!0,forceStereo:!1,simulcast:!0,screenShareEncoding:Mn.h1080fps15.encoding,stopMicTrackOnMute:!1,videoCodec:Kr,backupCodec:!0},Gr={autoGainControl:!0,echoCancellation:!0,noiseSuppression:!0},Hr={resolution:Nn.h720.resolution},zr={adaptiveStream:!1,dynacast:!1,stopLocalTrackOnUnpublish:!0,reconnectPolicy:new St,disconnectOnPageLeave:!0,expWebAudioMix:!1},Qr={autoSubscribe:!0,maxRetries:1,peerConnectionTimeout:15e3,websocketTimeout:15e3};var Yr;!function(e){e[e.NEW=0]="NEW",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED",e[e.FAILED=3]="FAILED",e[e.CLOSING=4]="CLOSING",e[e.CLOSED=5]="CLOSED"}(Yr||(Yr={}));class Xr{get needsPublisher(){return this.isPublisherConnectionRequired}get needsSubscriber(){return this.isSubscriberConnectionRequired}get currentState(){return this.state}constructor(e,t,i){var n;this.peerConnectionTimeout=Qr.peerConnectionTimeout,this.log=c,this.updateState=()=>{var e;const t=this.state,i=this.requiredTransports.map((e=>e.getConnectionState()));i.every((e=>"connected"===e))?this.state=Yr.CONNECTED:i.some((e=>"failed"===e))?this.state=Yr.FAILED:i.some((e=>"connecting"===e))?this.state=Yr.CONNECTING:i.every((e=>"closed"===e))?this.state=Yr.CLOSED:i.some((e=>"closed"===e))?this.state=Yr.CLOSING:i.every((e=>"new"===e))&&(this.state=Yr.NEW),t!==this.state&&(this.log.debug("pc state change: from ".concat(Yr[t]," to ").concat(Yr[this.state]),this.logContext),null===(e=this.onStateChange)||void 0===e||e.call(this,this.state,this.publisher.getConnectionState(),this.subscriber.getConnectionState()))},this.log=d(null!==(n=i.loggerName)&&void 0!==n?n:o.PCManager),this.loggerOptions=i,this.isPublisherConnectionRequired=!t,this.isSubscriberConnectionRequired=t;this.publisher=new Vr(e,{optional:[{googDscp:!0}]},i),this.subscriber=new Vr(e,void 0,i),this.publisher.onConnectionStateChange=this.updateState,this.subscriber.onConnectionStateChange=this.updateState,this.publisher.onIceConnectionStateChange=this.updateState,this.subscriber.onIceConnectionStateChange=this.updateState,this.publisher.onSignalingStatechange=this.updateState,this.subscriber.onSignalingStatechange=this.updateState,this.publisher.onIceCandidate=e=>{var t;null===(t=this.onIceCandidate)||void 0===t||t.call(this,e,Ln.PUBLISHER)},this.subscriber.onIceCandidate=e=>{var t;null===(t=this.onIceCandidate)||void 0===t||t.call(this,e,Ln.SUBSCRIBER)},this.subscriber.onDataChannel=e=>{var t;null===(t=this.onDataChannel)||void 0===t||t.call(this,e)},this.subscriber.onTrack=e=>{var t;null===(t=this.onTrack)||void 0===t||t.call(this,e)},this.publisher.onOffer=e=>{var t;null===(t=this.onPublisherOffer)||void 0===t||t.call(this,e)},this.state=Yr.NEW,this.connectionLock=new ur}get logContext(){var e,t;return Object.assign({},null===(t=(e=this.loggerOptions).loggerContextCb)||void 0===t?void 0:t.call(e))}requirePublisher(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.isPublisherConnectionRequired=e,this.updateState()}requireSubscriber(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.isSubscriberConnectionRequired=e,this.updateState()}createAndSendPublisherOffer(e){return this.publisher.createAndSendOffer(e)}setPublisherAnswer(e){return this.publisher.setRemoteDescription(e)}removeTrack(e){return this.publisher.removeTrack(e)}close(){return Ct(this,void 0,void 0,(function*(){if(this.publisher&&"closed"!==this.publisher.getSignallingState()){const e=this.publisher;for(const t of e.getSenders())try{e.canRemoveTrack()&&e.removeTrack(t)}catch(e){this.log.warn("could not removeTrack",Object.assign(Object.assign({},this.logContext),{error:e}))}}yield Promise.all([this.publisher.close(),this.subscriber.close()]),this.updateState()}))}triggerIceRestart(){return Ct(this,void 0,void 0,(function*(){this.subscriber.restartingIce=!0,this.needsPublisher&&(yield this.createAndSendPublisherOffer({iceRestart:!0}))}))}addIceCandidate(e,t){return Ct(this,void 0,void 0,(function*(){t===Ln.PUBLISHER?yield this.publisher.addIceCandidate(e):yield this.subscriber.addIceCandidate(e)}))}createSubscriberAnswerFromOffer(e){return Ct(this,void 0,void 0,(function*(){this.log.debug("received server offer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type,sdp:e.sdp,signalingState:this.subscriber.getSignallingState().toString()})),yield this.subscriber.setRemoteDescription(e);return yield this.subscriber.createAndSetAnswer()}))}updateConfiguration(e,t){this.publisher.setConfiguration(e),this.subscriber.setConfiguration(e),t&&this.triggerIceRestart()}ensurePCTransportConnection(e,t){var i;return Ct(this,void 0,void 0,(function*(){const n=yield this.connectionLock.lock();try{this.isPublisherConnectionRequired&&"connected"!==this.publisher.getConnectionState()&&"connecting"!==this.publisher.getConnectionState()&&(this.log.debug("negotiation required, start negotiating",this.logContext),this.publisher.negotiate()),yield Promise.all(null===(i=this.requiredTransports)||void 0===i?void 0:i.map((i=>this.ensureTransportConnected(i,e,t))))}finally{n()}}))}negotiate(e){return Ct(this,void 0,void 0,(function*(){return new Promise(((t,i)=>Ct(this,void 0,void 0,(function*(){const n=setTimeout((()=>{i("negotiation timed out")}),this.peerConnectionTimeout);e.signal.addEventListener("abort",(()=>{clearTimeout(n),i("negotiation aborted")})),this.publisher.once(Br,(()=>{e.signal.aborted||this.publisher.once(Fr,(()=>{clearTimeout(n),t()}))})),yield this.publisher.negotiate((e=>{clearTimeout(n),i(e)}))}))))}))}addPublisherTransceiver(e,t){return this.publisher.addTransceiver(e,t)}addPublisherTrack(e){return this.publisher.addTrack(e)}createPublisherDataChannel(e,t){return this.publisher.createDataChannel(e,t)}getConnectedAddress(e){return e===Ln.PUBLISHER||e===Ln.SUBSCRIBER?this.publisher.getConnectedAddress():this.requiredTransports[0].getConnectedAddress()}get requiredTransports(){const e=[];return this.isPublisherConnectionRequired&&e.push(this.publisher),this.isSubscriberConnectionRequired&&e.push(this.subscriber),e}ensureTransportConnected(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.peerConnectionTimeout;return Ct(this,void 0,void 0,(function*(){if("connected"!==e.getConnectionState())return new Promise(((e,n)=>Ct(this,void 0,void 0,(function*(){const s=()=>{this.log.warn("abort transport connection",this.logContext),Pn.clearTimeout(r),n(new cn("room connection has been cancelled",3))};(null==t?void 0:t.signal.aborted)&&s(),null==t||t.signal.addEventListener("abort",s);const r=Pn.setTimeout((()=>{null==t||t.signal.removeEventListener("abort",s),n(new cn("could not establish pc connection"))}),i);for(;this.state!==Yr.CONNECTED;)if(yield Ls(50),null==t?void 0:t.signal.aborted)return void n(new cn("room connection has been cancelled",3));Pn.clearTimeout(r),null==t||t.signal.removeEventListener("abort",s),e()}))))}))}}const Zr="_lossy",$r="_reliable",eo="leave-reconnect";var to;!function(e){e[e.New=0]="New",e[e.Connected=1]="Connected",e[e.Disconnected=2]="Disconnected",e[e.Reconnecting=3]="Reconnecting",e[e.Closed=4]="Closed"}(to||(to={}));class io extends Vt.EventEmitter{get isClosed(){return this._isClosed}constructor(t){var i;super(),this.options=t,this.rtcConfig={},this.peerConnectionTimeout=Qr.peerConnectionTimeout,this.fullReconnectOnNext=!1,this.subscriberPrimary=!1,this.pcState=to.New,this._isClosed=!0,this.pendingTrackResolvers={},this.reconnectAttempts=0,this.reconnectStart=0,this.attemptingReconnect=!1,this.joinAttempts=0,this.maxJoinAttempts=1,this.shouldFailNext=!1,this.log=c,this.handleDataChannel=e=>{let{channel:t}=e;return Ct(this,void 0,void 0,(function*(){if(t){if(t.label===$r)this.reliableDCSub=t;else{if(t.label!==Zr)return;this.lossyDCSub=t}this.log.debug("on data channel ".concat(t.id,", ").concat(t.label),this.logContext),t.onmessage=this.handleDataMessage}}))},this.handleDataMessage=t=>Ct(this,void 0,void 0,(function*(){var i,n;const s=yield this.dataProcessLock.lock();try{let s;if(t.data instanceof ArrayBuffer)s=t.data;else{if(!(t.data instanceof Blob))return void this.log.error("unsupported data type",Object.assign(Object.assign({},this.logContext),{data:t.data}));s=yield t.data.arrayBuffer()}const r=at.fromBinary(new Uint8Array(s));"speaker"===(null===(i=r.value)||void 0===i?void 0:i.case)?this.emit(e.EngineEvent.ActiveSpeakersUpdate,r.value.value.speakers):"user"===(null===(n=r.value)||void 0===n?void 0:n.case)&&this.emit(e.EngineEvent.DataPacketReceived,r.value.value,r.kind)}finally{s()}})),this.handleDataError=e=>{const t=0===e.currentTarget.maxRetransmits?"lossy":"reliable";if(e instanceof ErrorEvent&&e.error){const{error:i}=e.error;this.log.error("DataChannel error on ".concat(t,": ").concat(e.message),Object.assign(Object.assign({},this.logContext),{error:i}))}else this.log.error("Unknown DataChannel error on ".concat(t),Object.assign(Object.assign({},this.logContext),{event:e}))},this.handleBufferedAmountLow=t=>{const i=0===t.currentTarget.maxRetransmits?e.DataPacket_Kind.LOSSY:e.DataPacket_Kind.RELIABLE;this.updateAndEmitDCBufferStatus(i)},this.handleDisconnect=(t,i)=>{if(this._isClosed)return;this.log.warn("".concat(t," disconnected"),this.logContext),0===this.reconnectAttempts&&(this.reconnectStart=Date.now());const n=t=>{this.log.warn("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(t,"ms. giving up"),this.logContext),this.emit(e.EngineEvent.Disconnected),this.close()},s=Date.now()-this.reconnectStart;let r=this.getNextRetryDelay({elapsedMs:s,retryCount:this.reconnectAttempts});null!==r?(t===eo&&(r=0),this.log.debug("reconnecting in ".concat(r,"ms"),this.logContext),this.clearReconnectTimeout(),this.token&&this.regionUrlProvider&&this.regionUrlProvider.updateToken(this.token),this.reconnectTimeout=Pn.setTimeout((()=>this.attemptReconnect(i)),r)):n(s)},this.waitForRestarted=()=>new Promise(((t,i)=>{this.pcState===to.Connected&&t();const n=()=>{this.off(e.EngineEvent.Disconnected,s),t()},s=()=>{this.off(e.EngineEvent.Restarted,n),i()};this.once(e.EngineEvent.Restarted,n),this.once(e.EngineEvent.Disconnected,s)})),this.updateAndEmitDCBufferStatus=t=>{const i=this.isBufferStatusLow(t);void 0!==i&&i!==this.dcBufferStatus.get(t)&&(this.dcBufferStatus.set(t,i),this.emit(e.EngineEvent.DCBufferStatusChanged,i,t))},this.isBufferStatusLow=e=>{const t=this.dataChannelForKind(e);if(t)return t.bufferedAmount<=t.bufferedAmountLowThreshold},this.handleBrowserOnLine=()=>{this.client.currentState===Tr.RECONNECTING&&(this.clearReconnectTimeout(),this.attemptReconnect(qe.RR_SIGNAL_DISCONNECTED))},this.log=d(null!==(i=t.loggerName)&&void 0!==i?i:o.Engine),this.loggerOptions={loggerName:t.loggerName,loggerContextCb:()=>this.logContext},this.client=new Sr(void 0,this.loggerOptions),this.client.signalLatency=this.options.expSignalLatency,this.reconnectPolicy=this.options.reconnectPolicy,this.registerOnLineListener(),this.closingLock=new ur,this.dataProcessLock=new ur,this.dcBufferStatus=new Map([[e.DataPacket_Kind.LOSSY,!0],[e.DataPacket_Kind.RELIABLE,!0]]),this.client.onParticipantUpdate=t=>this.emit(e.EngineEvent.ParticipantUpdate,t),this.client.onConnectionQuality=t=>this.emit(e.EngineEvent.ConnectionQualityUpdate,t),this.client.onRoomUpdate=t=>this.emit(e.EngineEvent.RoomUpdate,t),this.client.onSubscriptionError=t=>this.emit(e.EngineEvent.SubscriptionError,t),this.client.onSubscriptionPermissionUpdate=t=>this.emit(e.EngineEvent.SubscriptionPermissionUpdate,t),this.client.onSpeakersChanged=t=>this.emit(e.EngineEvent.SpeakersChanged,t),this.client.onStreamStateUpdate=t=>this.emit(e.EngineEvent.StreamStateChanged,t)}get logContext(){var e,t,i,n,s,r;return{room:null===(t=null===(e=this.latestJoinResponse)||void 0===e?void 0:e.room)||void 0===t?void 0:t.name,roomSid:null===(n=null===(i=this.latestJoinResponse)||void 0===i?void 0:i.room)||void 0===n?void 0:n.sid,identity:null===(r=null===(s=this.latestJoinResponse)||void 0===s?void 0:s.participant)||void 0===r?void 0:r.identity}}join(e,t,i,n){return Ct(this,void 0,void 0,(function*(){this.url=e,this.token=t,this.signalOpts=i,this.maxJoinAttempts=i.maxRetries;try{this.joinAttempts+=1,this.setupSignalClientCallbacks();const s=yield this.client.join(e,t,i,n);return this._isClosed=!1,this.latestJoinResponse=s,this.subscriberPrimary=s.subscriberPrimary,this.pcManager||(yield this.configure(s)),this.subscriberPrimary||this.negotiate(),this.clientConfiguration=s.clientConfiguration,s}catch(s){if(s instanceof cn&&1===s.reason&&(this.log.warn("Couldn't connect to server, attempt ".concat(this.joinAttempts," of ").concat(this.maxJoinAttempts),this.logContext),this.joinAttempts<this.maxJoinAttempts))return this.join(e,t,i,n);throw s}}))}close(){return Ct(this,void 0,void 0,(function*(){const t=yield this.closingLock.lock();if(this.isClosed)t();else try{this._isClosed=!0,this.emit(e.EngineEvent.Closing),this.removeAllListeners(),this.deregisterOnLineListener(),this.clearPendingReconnect(),yield this.cleanupPeerConnections(),yield this.cleanupClient()}finally{t()}}))}cleanupPeerConnections(){var e;return Ct(this,void 0,void 0,(function*(){yield null===(e=this.pcManager)||void 0===e?void 0:e.close(),this.pcManager=void 0;const t=e=>{e&&(e.close(),e.onbufferedamountlow=null,e.onclose=null,e.onclosing=null,e.onerror=null,e.onmessage=null,e.onopen=null)};t(this.lossyDC),t(this.lossyDCSub),t(this.reliableDC),t(this.reliableDCSub),this.lossyDC=void 0,this.lossyDCSub=void 0,this.reliableDC=void 0,this.reliableDCSub=void 0}))}cleanupClient(){return Ct(this,void 0,void 0,(function*(){yield this.client.close(),this.client.resetCallbacks()}))}addTrack(e){if(this.pendingTrackResolvers[e.cid])throw new ln("a track with the same ID has already been published");return new Promise(((t,i)=>{const n=setTimeout((()=>{delete this.pendingTrackResolvers[e.cid],i(new cn("publication of local track timed out, no response from server"))}),1e4);this.pendingTrackResolvers[e.cid]={resolve:e=>{clearTimeout(n),t(e)},reject:()=>{clearTimeout(n),i(new Error("Cancelled publication by calling unpublish"))}},this.client.sendAddTrack(e)}))}removeTrack(e){if(e.track&&this.pendingTrackResolvers[e.track.id]){const{reject:t}=this.pendingTrackResolvers[e.track.id];t&&t(),delete this.pendingTrackResolvers[e.track.id]}try{return this.pcManager.removeTrack(e),!0}catch(e){this.log.warn("failed to remove track",Object.assign(Object.assign({},this.logContext),{error:e}))}return!1}updateMuteStatus(e,t){this.client.sendMuteTrack(e,t)}get dataSubscriberReadyState(){var e;return null===(e=this.reliableDCSub)||void 0===e?void 0:e.readyState}getConnectedServerAddress(){var e;return Ct(this,void 0,void 0,(function*(){return null===(e=this.pcManager)||void 0===e?void 0:e.getConnectedAddress()}))}setRegionUrlProvider(e){this.regionUrlProvider=e}configure(t){var i;return Ct(this,void 0,void 0,(function*(){if(this.pcManager&&this.pcManager.currentState!==Yr.NEW)return;this.participantSid=null===(i=t.participant)||void 0===i?void 0:i.sid;const n=this.makeRTCConfiguration(t);this.pcManager=new Xr(n,t.subscriberPrimary,this.loggerOptions),this.emit(e.EngineEvent.TransportsCreated,this.pcManager.publisher,this.pcManager.subscriber),this.pcManager.onIceCandidate=(e,t)=>{this.client.sendIceCandidate(e,t)},this.pcManager.onPublisherOffer=e=>{this.client.sendOffer(e)},this.pcManager.onDataChannel=this.handleDataChannel,this.pcManager.onStateChange=(i,n,s)=>Ct(this,void 0,void 0,(function*(){if(this.log.debug("primary PC state changed ".concat(i),this.logContext),i===Yr.CONNECTED){const i=this.pcState===to.New;this.pcState=to.Connected,i&&this.emit(e.EngineEvent.Connected,t)}else i===Yr.FAILED&&this.pcState===to.Connected&&(this.pcState=to.Disconnected,this.handleDisconnect("peerconnection failed","failed"===s?qe.RR_SUBSCRIBER_FAILED:qe.RR_PUBLISHER_FAILED))})),this.pcManager.onTrack=t=>{this.emit(e.EngineEvent.MediaTrackAdded,t.track,t.streams[0],t.receiver)},this.createDataChannels()}))}setupSignalClientCallbacks(){this.client.onAnswer=e=>Ct(this,void 0,void 0,(function*(){this.pcManager&&(this.log.debug("received server answer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type})),yield this.pcManager.setPublisherAnswer(e))})),this.client.onTrickle=(e,t)=>{this.pcManager&&(this.log.trace("got ICE candidate from peer",Object.assign(Object.assign({},this.logContext),{candidate:e,target:t})),this.pcManager.addIceCandidate(e,t))},this.client.onOffer=e=>Ct(this,void 0,void 0,(function*(){if(!this.pcManager)return;const t=yield this.pcManager.createSubscriberAnswerFromOffer(e);this.client.sendAnswer(t)})),this.client.onLocalTrackPublished=e=>{var t;if(this.log.debug("received trackPublishedResponse",Object.assign(Object.assign({},this.logContext),{cid:e.cid,track:null===(t=e.track)||void 0===t?void 0:t.sid})),!this.pendingTrackResolvers[e.cid])return void this.log.error("missing track resolver for ".concat(e.cid),Object.assign(Object.assign({},this.logContext),{cid:e.cid}));const{resolve:i}=this.pendingTrackResolvers[e.cid];delete this.pendingTrackResolvers[e.cid],i(e.track)},this.client.onLocalTrackUnpublished=t=>{this.emit(e.EngineEvent.LocalTrackUnpublished,t)},this.client.onTokenRefresh=e=>{this.token=e},this.client.onRemoteMuteChanged=(t,i)=>{this.emit(e.EngineEvent.RemoteMute,t,i)},this.client.onSubscribedQualityUpdate=t=>{this.emit(e.EngineEvent.SubscribedQualityUpdate,t)},this.client.onClose=()=>{this.handleDisconnect("signal",qe.RR_SIGNAL_DISCONNECTED)},this.client.onLeave=t=>{(null==t?void 0:t.canReconnect)?(this.fullReconnectOnNext=!0,this.handleDisconnect(eo)):(this.emit(e.EngineEvent.Disconnected,null==t?void 0:t.reason),this.close()),this.log.debug("client leave request",Object.assign(Object.assign({},this.logContext),{reason:null==t?void 0:t.reason}))}}makeRTCConfiguration(e){var t;const i=Object.assign({},this.rtcConfig);if((null===(t=this.signalOpts)||void 0===t?void 0:t.e2eeEnabled)&&(this.log.debug("E2EE - setting up transports with insertable streams",this.logContext),i.encodedInsertableStreams=!0),e.iceServers&&!i.iceServers){const t=[];e.iceServers.forEach((e=>{const i={urls:e.urls};e.username&&(i.username=e.username),e.credential&&(i.credential=e.credential),t.push(i)})),i.iceServers=t}return e.clientConfiguration&&e.clientConfiguration.forceRelay===Je.ENABLED&&(i.iceTransportPolicy="relay"),i.sdpSemantics="unified-plan",i.continualGatheringPolicy="gather_continually",i}createDataChannels(){this.pcManager&&(this.lossyDC&&(this.lossyDC.onmessage=null,this.lossyDC.onerror=null),this.reliableDC&&(this.reliableDC.onmessage=null,this.reliableDC.onerror=null),this.lossyDC=this.pcManager.createPublisherDataChannel(Zr,{ordered:!0,maxRetransmits:0}),this.reliableDC=this.pcManager.createPublisherDataChannel($r,{ordered:!0}),this.lossyDC.onmessage=this.handleDataMessage,this.reliableDC.onmessage=this.handleDataMessage,this.lossyDC.onerror=this.handleDataError,this.reliableDC.onerror=this.handleDataError,this.lossyDC.bufferedAmountLowThreshold=65535,this.reliableDC.bufferedAmountLowThreshold=65535,this.lossyDC.onbufferedamountlow=this.handleBufferedAmountLow,this.reliableDC.onbufferedamountlow=this.handleBufferedAmountLow)}setPreferredCodec(e,t,i){if(!("getCapabilities"in RTCRtpReceiver))return;const n=RTCRtpReceiver.getCapabilities(t);if(!n)return;this.log.debug("get receiver capabilities",Object.assign(Object.assign({},this.logContext),{cap:n}));const s=[],r=[],o=[];n.codecs.forEach((e=>{const t=e.mimeType.toLowerCase();if("audio/opus"===t)return void s.push(e);t==="video/".concat(i)?"h264"!==i||e.sdpFmtpLine&&e.sdpFmtpLine.includes("profile-level-id=42e01f")?s.push(e):r.push(e):o.push(e)})),function(e){if(!Hs())return!1;if(!("setCodecPreferences"in e))return!1;const t=Sn();if(!(null==t?void 0:t.name)||!t.version)return!1;const i=Vs[t.name];return!!i&&$s(t.version,i)>=0}(e)&&e.setCodecPreferences(s.concat(r,o))}createSender(e,t,i){return Ct(this,void 0,void 0,(function*(){if(As()){return yield this.createTransceiverRTCRtpSender(e,t,i)}if(Us()){this.log.warn("using add-track fallback",this.logContext);return yield this.createRTCRtpSender(e.mediaStreamTrack)}throw new hn("Required webRTC APIs not supported on this device")}))}createSimulcastSender(e,t,i,n){return Ct(this,void 0,void 0,(function*(){if(As())return this.createSimulcastTransceiverSender(e,t,i,n);if(Us())return this.log.debug("using add-track fallback",this.logContext),this.createRTCRtpSender(e.mediaStreamTrack);throw new hn("Cannot stream on this device")}))}createTransceiverRTCRtpSender(e,t,i){return Ct(this,void 0,void 0,(function*(){if(!this.pcManager)throw new hn("publisher is closed");const n=[];e.mediaStream&&n.push(e.mediaStream);const s={direction:"sendonly",streams:n};i&&(s.sendEncodings=i);const r=yield this.pcManager.addPublisherTransceiver(e.mediaStreamTrack,s);return e.kind===Cs.Kind.Video&&t.videoCodec&&(this.setPreferredCodec(r,e.kind,t.videoCodec),e.codec=t.videoCodec),r.sender}))}createSimulcastTransceiverSender(e,t,i,n){return Ct(this,void 0,void 0,(function*(){if(!this.pcManager)throw new hn("publisher is closed");const s={direction:"sendonly"};n&&(s.sendEncodings=n);const r=yield this.pcManager.addPublisherTransceiver(t.mediaStreamTrack,s);if(i.videoCodec)return this.setPreferredCodec(r,e.kind,i.videoCodec),e.setSimulcastTrackSender(i.videoCodec,r.sender),r.sender}))}createRTCRtpSender(e){return Ct(this,void 0,void 0,(function*(){if(!this.pcManager)throw new hn("publisher is closed");return this.pcManager.addPublisherTrack(e)}))}attemptReconnect(t){var i,n,s;return Ct(this,void 0,void 0,(function*(){if(!this._isClosed)if(this.attemptingReconnect)c.warn("already attempting reconnect, returning early",this.logContext);else{(null===(i=this.clientConfiguration)||void 0===i?void 0:i.resumeConnection)!==Je.DISABLED&&(null!==(s=null===(n=this.pcManager)||void 0===n?void 0:n.currentState)&&void 0!==s?s:Yr.NEW)!==Yr.NEW||(this.fullReconnectOnNext=!0);try{this.attemptingReconnect=!0,this.fullReconnectOnNext?yield this.restartConnection():yield this.resumeConnection(t),this.clearPendingReconnect(),this.fullReconnectOnNext=!1}catch(t){this.reconnectAttempts+=1;let i=!0;t instanceof hn?(this.log.debug("received unrecoverable error",Object.assign(Object.assign({},this.logContext),{error:t})),i=!1):t instanceof no||(this.fullReconnectOnNext=!0),i?this.handleDisconnect("reconnect",qe.RR_UNKNOWN):(this.log.info("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(Date.now()-this.reconnectStart,"ms. giving up"),this.logContext),this.emit(e.EngineEvent.Disconnected),yield this.close())}finally{this.attemptingReconnect=!1}}}))}getNextRetryDelay(e){try{return this.reconnectPolicy.nextRetryDelayInMs(e)}catch(e){this.log.warn("encountered error in reconnect policy",Object.assign(Object.assign({},this.logContext),{error:e}))}return null}restartConnection(t){var i,n,s;return Ct(this,void 0,void 0,(function*(){try{if(!this.url||!this.token)throw new hn("could not reconnect, url or token not saved");let n;this.log.info("reconnecting, attempt: ".concat(this.reconnectAttempts),this.logContext),this.emit(e.EngineEvent.Restarting),this.client.isDisconnected||(yield this.client.sendLeave()),yield this.cleanupPeerConnections(),yield this.cleanupClient();try{if(!this.signalOpts)throw this.log.warn("attempted connection restart, without signal options present",this.logContext),new no;n=yield this.join(null!=t?t:this.url,this.token,this.signalOpts)}catch(e){if(e instanceof cn&&0===e.reason)throw new hn("could not reconnect, token might be expired");throw new no}if(this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(this.client.setReconnected(),this.emit(e.EngineEvent.SignalRestarted,n),yield this.waitForPCReconnected(),this.client.currentState!==Tr.CONNECTED)throw new no("Signal connection got severed during reconnect");null===(i=this.regionUrlProvider)||void 0===i||i.resetAttempts(),this.emit(e.EngineEvent.Restarted)}catch(e){const t=yield null===(n=this.regionUrlProvider)||void 0===n?void 0:n.getNextBestRegionUrl();if(t)return void(yield this.restartConnection(t));throw null===(s=this.regionUrlProvider)||void 0===s||s.resetAttempts(),e}}))}resumeConnection(t){var i;return Ct(this,void 0,void 0,(function*(){if(!this.url||!this.token)throw new hn("could not reconnect, url or token not saved");if(!this.pcManager)throw new hn("publisher and subscriber connections unset");this.log.info("resuming signal connection, attempt ".concat(this.reconnectAttempts),this.logContext),this.emit(e.EngineEvent.Resuming);try{this.setupSignalClientCallbacks();const e=yield this.client.reconnect(this.url,this.token,this.participantSid,t);if(e){const t=this.makeRTCConfiguration(e);this.pcManager.updateConfiguration(t)}}catch(e){let t="";if(e instanceof Error&&(t=e.message,this.log.error(e.message,Object.assign(Object.assign({},this.logContext),{error:e}))),e instanceof cn&&0===e.reason)throw new hn("could not reconnect, token might be expired");if(e instanceof cn&&4===e.reason)throw e;throw new no(t)}if(this.emit(e.EngineEvent.SignalResumed),this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(yield this.pcManager.triggerIceRestart(),yield this.waitForPCReconnected(),this.client.currentState!==Tr.CONNECTED)throw new no("Signal connection got severed during reconnect");this.client.setReconnected(),"open"===(null===(i=this.reliableDC)||void 0===i?void 0:i.readyState)&&null===this.reliableDC.id&&this.createDataChannels(),this.emit(e.EngineEvent.Resumed)}))}waitForPCInitialConnection(e,t){return Ct(this,void 0,void 0,(function*(){if(!this.pcManager)throw new hn("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(t,e)}))}waitForPCReconnected(){return Ct(this,void 0,void 0,(function*(){this.pcState=to.Reconnecting,this.log.debug("waiting for peer connection to reconnect",this.logContext);try{if(yield Ls(2e3),!this.pcManager)throw new hn("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(void 0,this.peerConnectionTimeout),this.pcState=to.Connected}catch(e){throw this.pcState=to.Disconnected,new cn("could not establish PC connection, ".concat(e.message))}}))}sendDataPacket(e,t){return Ct(this,void 0,void 0,(function*(){const i=e.toBinary();yield this.ensurePublisherConnected(t);const n=this.dataChannelForKind(t);n&&n.send(i),this.updateAndEmitDCBufferStatus(t)}))}ensureDataTransportConnected(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.subscriberPrimary;var i;return Ct(this,void 0,void 0,(function*(){if(!this.pcManager)throw new hn("PC manager is closed");const n=t?this.pcManager.subscriber:this.pcManager.publisher,s=t?"Subscriber":"Publisher";if(!n)throw new cn("".concat(s," connection not set"));t||this.pcManager.publisher.isICEConnected||"checking"===this.pcManager.publisher.getICEConnectionState()||this.negotiate();const r=this.dataChannelForKind(e,t);if("open"===(null==r?void 0:r.readyState))return;const o=(new Date).getTime()+this.peerConnectionTimeout;for(;(new Date).getTime()<o;){if(n.isICEConnected&&"open"===(null===(i=this.dataChannelForKind(e,t))||void 0===i?void 0:i.readyState))return;yield Ls(50)}throw new cn("could not establish ".concat(s," connection, state: ").concat(n.getICEConnectionState()))}))}ensurePublisherConnected(e){return Ct(this,void 0,void 0,(function*(){yield this.ensureDataTransportConnected(e,!1)}))}verifyTransport(){return!!this.pcManager&&(this.pcManager.currentState===Yr.CONNECTED&&!(!this.client.ws||this.client.ws.readyState===WebSocket.CLOSED))}negotiate(){return Ct(this,void 0,void 0,(function*(){return new Promise(((t,i)=>Ct(this,void 0,void 0,(function*(){if(!this.pcManager)return void i(new pn("PC manager is closed"));this.pcManager.requirePublisher();const n=new AbortController,s=()=>{n.abort(),this.log.debug("engine disconnected while negotiation was ongoing",this.logContext),t()};this.isClosed&&i("cannot negotiate on closed engine"),this.on(e.EngineEvent.Closing,s),this.pcManager.publisher.once(Jr,(t=>{const i=new Map;t.forEach((e=>{const t=e.codec.toLowerCase();var n;n=t,On.includes(n)&&i.set(e.payload,t)})),this.emit(e.EngineEvent.RTPVideoMapUpdate,i)}));try{yield this.pcManager.negotiate(n),t()}catch(e){e instanceof pn&&(this.fullReconnectOnNext=!0),this.handleDisconnect("negotiation",qe.RR_UNKNOWN),i(e)}finally{this.off(e.EngineEvent.Closing,s)}}))))}))}dataChannelForKind(t,i){if(i){if(t===e.DataPacket_Kind.LOSSY)return this.lossyDCSub;if(t===e.DataPacket_Kind.RELIABLE)return this.reliableDCSub}else{if(t===e.DataPacket_Kind.LOSSY)return this.lossyDC;if(t===e.DataPacket_Kind.RELIABLE)return this.reliableDC}}sendSyncState(e,t){var i,n;if(!this.pcManager)return void this.log.warn("sync state cannot be sent without peer connection setup",this.logContext);const s=this.pcManager.subscriber.getLocalDescription(),r=this.pcManager.subscriber.getRemoteDescription(),o=null===(n=null===(i=this.signalOpts)||void 0===i?void 0:i.autoSubscribe)||void 0===n||n,a=new Array;e.forEach((e=>{e.isDesired!==o&&a.push(e.trackSid)})),this.client.sendSyncState(new ms({answer:s?Er({sdp:s.sdp,type:s.type}):void 0,offer:r?Er({sdp:r.sdp,type:r.type}):void 0,subscription:new Yn({trackSids:a,subscribe:!o,participantTracks:[]}),publishTracks:Ns(t),dataChannels:this.dataChannelsInfo()}))}failNext(){this.shouldFailNext=!0}dataChannelsInfo(){const t=[],i=(e,i)=>{void 0!==(null==e?void 0:e.id)&&null!==e.id&&t.push(new gs({label:e.label,id:e.id,target:i}))};return i(this.dataChannelForKind(e.DataPacket_Kind.LOSSY),Ln.PUBLISHER),i(this.dataChannelForKind(e.DataPacket_Kind.RELIABLE),Ln.PUBLISHER),i(this.dataChannelForKind(e.DataPacket_Kind.LOSSY,!0),Ln.SUBSCRIBER),i(this.dataChannelForKind(e.DataPacket_Kind.RELIABLE,!0),Ln.SUBSCRIBER),t}clearReconnectTimeout(){this.reconnectTimeout&&Pn.clearTimeout(this.reconnectTimeout)}clearPendingReconnect(){this.clearReconnectTimeout(),this.reconnectAttempts=0}registerOnLineListener(){Hs()&&window.addEventListener("online",this.handleBrowserOnLine)}deregisterOnLineListener(){Hs()&&window.removeEventListener("online",this.handleBrowserOnLine)}}class no extends Error{}class so{constructor(e,t){this.lastUpdateAt=0,this.settingsCacheTime=3e3,this.attemptedRegions=[],this.serverUrl=new URL(e),this.token=t}updateToken(e){this.token=e}isCloud(){return Qs(this.serverUrl)}getServerUrl(){return this.serverUrl}getNextBestRegionUrl(e){return Ct(this,void 0,void 0,(function*(){if(!this.isCloud())throw Error("region availability is only supported for LiveKit Cloud domains");(!this.regionSettings||Date.now()-this.lastUpdateAt>this.settingsCacheTime)&&(this.regionSettings=yield this.fetchRegionSettings(e));const t=this.regionSettings.regions.filter((e=>!this.attemptedRegions.find((t=>t.url===e.url))));if(t.length>0){const e=t[0];return this.attemptedRegions.push(e),c.debug("next region: ".concat(e.region)),e.url}return null}))}resetAttempts(){this.attemptedRegions=[]}fetchRegionSettings(e){return Ct(this,void 0,void 0,(function*(){const t=yield fetch("".concat((i=this.serverUrl,"".concat(i.protocol.replace("ws","http"),"//").concat(i.host,"/settings")),"/regions"),{headers:{authorization:"Bearer ".concat(this.token)},signal:e});var i;if(t.ok){const e=yield t.json();return this.lastUpdateAt=Date.now(),e}throw new cn("Could not fetch region settings: ".concat(t.statusText),401===t.status?0:void 0,t.status)}))}}const ro=2e3;function oo(e,t){if(!t)return 0;let i,n;return"bytesReceived"in e?(i=e.bytesReceived,n=t.bytesReceived):"bytesSent"in e&&(i=e.bytesSent,n=t.bytesSent),void 0===i||void 0===n||void 0===e.timestamp||void 0===t.timestamp?0:8*(i-n)*1e3/(e.timestamp-t.timestamp)}class ao extends fr{constructor(e,t){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;super(e,Cs.Kind.Audio,t,i,s),this.stopOnMute=!1,this.monitorSender=()=>Ct(this,void 0,void 0,(function*(){if(!this.sender)return void(this._currentBitrate=0);let e;try{e=yield this.getSenderStats()}catch(e){return void this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:e}))}e&&this.prevStats&&(this._currentBitrate=oo(e,this.prevStats)),this.prevStats=e})),this.audioContext=n,this.checkForSilence()}setDeviceId(e){return Ct(this,void 0,void 0,(function*(){return this._constraints.deviceId===e||(this._constraints.deviceId=e,this.isMuted||(yield this.restartTrack()),this.isMuted||hr(e)===this.mediaStreamTrack.getSettings().deviceId)}))}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return Ct(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.source===Cs.Source.Microphone&&this.stopOnMute&&!this.isUserProvided&&(this.log.debug("stopping mic track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this}finally{t()}}))}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return Ct(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{const t=this._constraints.deviceId&&this._mediaStreamTrack.getSettings().deviceId!==hr(this._constraints.deviceId);return this.source!==Cs.Source.Microphone||!this.stopOnMute&&"ended"!==this._mediaStreamTrack.readyState&&!t||this.isUserProvided||(this.log.debug("reacquiring mic track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{t()}}))}restartTrack(e){return Ct(this,void 0,void 0,(function*(){let t;if(e){const i=Is({audio:e});"boolean"!=typeof i.audio&&(t=i.audio)}yield this.restart(t)}))}restart(e){const t=Object.create(null,{restart:{get:()=>super.restart}});return Ct(this,void 0,void 0,(function*(){const i=yield t.restart.call(this,e);return this.checkForSilence(),i}))}startMonitor(){Hs()&&(this.monitorInterval||(this.monitorInterval=setInterval((()=>{this.monitorSender()}),ro)))}setProcessor(e){var t;return Ct(this,void 0,void 0,(function*(){const i=yield this.processorLock.lock();try{if(!this.audioContext)throw Error("Audio context needs to be set on LocalAudioTrack in order to enable processors");if(this.processor&&(yield this.stopProcessor()),"unknown"===this.kind)throw TypeError("cannot set processor on track of unknown kind");const i={kind:this.kind,track:this._mediaStreamTrack,audioContext:this.audioContext};this.log.debug("setting up audio processor ".concat(e.name),this.logContext),yield e.init(i),this.processor=e,this.processor.processedTrack&&(yield null===(t=this.sender)||void 0===t?void 0:t.replaceTrack(this.processor.processedTrack))}finally{i()}}))}setAudioContext(e){this.audioContext=e}getSenderStats(){var e;return Ct(this,void 0,void 0,(function*(){if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return;let t;return(yield this.sender.getStats()).forEach((e=>{"outbound-rtp"===e.type&&(t={type:"audio",streamId:e.id,packetsSent:e.packetsSent,packetsLost:e.packetsLost,bytesSent:e.bytesSent,timestamp:e.timestamp,roundTripTime:e.roundTripTime,jitter:e.jitter})})),t}))}checkForSilence(){return Ct(this,void 0,void 0,(function*(){const t=yield function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return Ct(this,void 0,void 0,(function*(){const i=Os();if(i){const n=i.createAnalyser();n.fftSize=2048;const s=n.frequencyBinCount,r=new Uint8Array(s);i.createMediaStreamSource(new MediaStream([e.mediaStreamTrack])).connect(n),yield Ls(t),n.getByteTimeDomainData(r);const o=r.some((e=>128!==e&&0!==e));return i.close(),!o}return!1}))}(this);return t&&(this.isMuted||this.log.warn("silence detected on local audio track",this.logContext),this.emit(e.TrackEvent.AudioSilenceDetected)),t}))}}function co(e,t,i){switch(e.kind){case"audio":return new ao(e,t,!1,void 0,i);case"video":return new So(e,t,!1,i);default:throw new ln("unsupported track type: ".concat(e.kind))}}const lo=Object.values(Nn),uo=Object.values(_n),ho=Object.values(Mn),po=[Nn.h180,Nn.h360],mo=[_n.h180,_n.h360],go=e=>[{scaleResolutionDownBy:2,fps:e.encoding.maxFramerate}].map((t=>{var i,n;return new Rn(Math.floor(e.width/t.scaleResolutionDownBy),Math.floor(e.height/t.scaleResolutionDownBy),Math.max(15e4,Math.floor(e.encoding.maxBitrate/(Math.pow(t.scaleResolutionDownBy,2)*((null!==(i=e.encoding.maxFramerate)&&void 0!==i?i:30)/(null!==(n=t.fps)&&void 0!==n?n:30))))),t.fps,e.encoding.priority)})),fo=["q","h","f"];function vo(e,t,i,n){var s,r;let o=null==n?void 0:n.videoEncoding;e&&(o=null==n?void 0:n.screenShareEncoding);const a=null==n?void 0:n.simulcast,d=null==n?void 0:n.scalabilityMode,l=null==n?void 0:n.videoCodec;if(!o&&!a&&!d||!t||!i)return[{}];o||(o=function(e,t,i,n){const s=function(e,t,i){if(e)return ho;const n=t>i?t/i:i/t;if(Math.abs(n-16/9)<Math.abs(n-4/3))return lo;return uo}(e,t,i);let{encoding:r}=s[0];const o=Math.max(t,i);for(let e=0;e<s.length;e+=1){const t=s[e];if(r=t.encoding,t.width>=o)break}if(n)switch(n){case"av1":r=Object.assign({},r),r.maxBitrate=.7*r.maxBitrate;break;case"vp9":r=Object.assign({},r),r.maxBitrate=.85*r.maxBitrate}return r}(e,t,i,l),c.debug("using video encoding",o));const u=new Rn(t,i,o.maxBitrate,o.maxFramerate,o.priority);if(d&&Fs(l)){c.debug("using svc with scalabilityMode ".concat(d));const e=new To(d),t=[];if(e.spatial>3)throw new Error("unsupported scalabilityMode: ".concat(d));for(let i=0;i<e.spatial;i+=1)t.push({rid:fo[2-i],maxBitrate:o.maxBitrate/Math.pow(3,i),maxFramerate:u.encoding.maxFramerate});return t[0].scalabilityMode=d,c.debug("encodings",t),t}if(!a)return[o];let h,p=[];if(p=e?null!==(s=bo(null==n?void 0:n.screenShareSimulcastLayers))&&void 0!==s?s:yo(e,u):null!==(r=bo(null==n?void 0:n.videoSimulcastLayers))&&void 0!==r?r:yo(e,u),p.length>0){const e=p[0];p.length>1&&([,h]=p);const n=Math.max(t,i);if(n>=960&&h)return ko(t,i,[e,h,u]);if(n>=480)return ko(t,i,[e,u])}return ko(t,i,[u])}function yo(e,t){if(e)return go(t);const{width:i,height:n}=t,s=i>n?i/n:n/i;return Math.abs(s-16/9)<Math.abs(s-4/3)?po:mo}function ko(e,t,i){const n=[];if(i.forEach(((i,s)=>{if(s>=fo.length)return;const r=Math.min(e,t),o={rid:fo[s],scaleResolutionDownBy:Math.max(1,r/Math.min(i.width,i.height)),maxBitrate:i.encoding.maxBitrate};i.encoding.maxFramerate&&(o.maxFramerate=i.encoding.maxFramerate);const a=qs()||0===s;i.encoding.priority&&a&&(o.priority=i.encoding.priority,o.networkPriority=i.encoding.priority),n.push(o)})),zs()&&"ios"===Xs()){let e;n.forEach((t=>{e?t.maxFramerate&&t.maxFramerate>e&&(e=t.maxFramerate):e=t.maxFramerate}));let t=!0;n.forEach((i=>{var n;i.maxFramerate!=e&&(t&&(t=!1,c.info("Simulcast on iOS React-Native requires all encodings to share the same framerate.")),c.info('Setting framerate of encoding "'.concat(null!==(n=i.rid)&&void 0!==n?n:"",'" to ').concat(e)),i.maxFramerate=e)}))}return n}function bo(e){if(e)return e.sort(((e,t)=>{const{encoding:i}=e,{encoding:n}=t;return i.maxBitrate>n.maxBitrate?1:i.maxBitrate<n.maxBitrate?-1:i.maxBitrate===n.maxBitrate&&i.maxFramerate&&n.maxFramerate?i.maxFramerate>n.maxFramerate?1:-1:0}))}class To{constructor(e){const t=e.match(/^L(\d)T(\d)(h|_KEY|_KEY_SHIFT){0,1}$/);if(!t)throw new Error("invalid scalability mode");if(this.spatial=parseInt(t[1]),this.temporal=parseInt(t[2]),t.length>3)switch(t[3]){case"h":case"_KEY":case"_KEY_SHIFT":this.suffix=t[3]}}toString(){var e;return"L".concat(this.spatial,"T").concat(this.temporal).concat(null!==(e=this.suffix)&&void 0!==e?e:"")}}class So extends fr{constructor(e,t){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=arguments.length>3?arguments[3]:void 0;super(e,Cs.Kind.Video,t,i,n),this.simulcastCodecs=new Map,this.monitorSender=()=>Ct(this,void 0,void 0,(function*(){if(!this.sender)return void(this._currentBitrate=0);let e;try{e=yield this.getSenderStats()}catch(e){return void this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:e}))}const t=new Map(e.map((e=>[e.rid,e])));if(this.prevStats){let e=0;t.forEach(((t,i)=>{var n;const s=null===(n=this.prevStats)||void 0===n?void 0:n.get(i);e+=oo(t,s)})),this._currentBitrate=e}this.prevStats=t})),this.senderLock=new ur}get isSimulcast(){return!!(this.sender&&this.sender.getParameters().encodings.length>1)}startMonitor(e){var t;if(this.signalClient=e,!Hs())return;const i=null===(t=this.sender)||void 0===t?void 0:t.getParameters();i&&(this.encodings=i.encodings),this.monitorInterval||(this.monitorInterval=setInterval((()=>{this.monitorSender()}),ro))}stop(){this._mediaStreamTrack.getConstraints(),this.simulcastCodecs.forEach((e=>{e.mediaStreamTrack.stop()})),super.stop()}pauseUpstream(){const e=Object.create(null,{pauseUpstream:{get:()=>super.pauseUpstream}});var t,i,n,s,r;return Ct(this,void 0,void 0,(function*(){yield e.pauseUpstream.call(this);try{for(var o,a=!0,c=wt(this.simulcastCodecs.values());o=yield c.next(),!(t=o.done);a=!0){s=o.value,a=!1;const e=s;yield null===(r=e.sender)||void 0===r?void 0:r.replaceTrack(null)}}catch(e){i={error:e}}finally{try{a||t||!(n=c.return)||(yield n.call(c))}finally{if(i)throw i.error}}}))}resumeUpstream(){const e=Object.create(null,{resumeUpstream:{get:()=>super.resumeUpstream}});var t,i,n,s,r;return Ct(this,void 0,void 0,(function*(){yield e.resumeUpstream.call(this);try{for(var o,a=!0,c=wt(this.simulcastCodecs.values());o=yield c.next(),!(t=o.done);a=!0){s=o.value,a=!1;const e=s;yield null===(r=e.sender)||void 0===r?void 0:r.replaceTrack(e.mediaStreamTrack)}}catch(e){i={error:e}}finally{try{a||t||!(n=c.return)||(yield n.call(c))}finally{if(i)throw i.error}}}))}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return Ct(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.source!==Cs.Source.Camera||this.isUserProvided||(this.log.debug("stopping camera track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this}finally{t()}}))}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return Ct(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.source!==Cs.Source.Camera||this.isUserProvided||(this.log.debug("reacquiring camera track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{t()}}))}setTrackMuted(e){super.setTrackMuted(e);for(const t of this.simulcastCodecs.values())t.mediaStreamTrack.enabled=!e}getSenderStats(){var e;return Ct(this,void 0,void 0,(function*(){if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return[];const t=[],i=yield this.sender.getStats();return i.forEach((e=>{var n;if("outbound-rtp"===e.type){const s={type:"video",streamId:e.id,frameHeight:e.frameHeight,frameWidth:e.frameWidth,firCount:e.firCount,pliCount:e.pliCount,nackCount:e.nackCount,packetsSent:e.packetsSent,bytesSent:e.bytesSent,framesSent:e.framesSent,timestamp:e.timestamp,rid:null!==(n=e.rid)&&void 0!==n?n:e.id,retransmittedPacketsSent:e.retransmittedPacketsSent,qualityLimitationReason:e.qualityLimitationReason,qualityLimitationResolutionChanges:e.qualityLimitationResolutionChanges},r=i.get(e.remoteId);r&&(s.jitter=r.jitter,s.packetsLost=r.packetsLost,s.roundTripTime=r.roundTripTime),t.push(s)}})),t}))}setPublishingQuality(t){const i=[];for(let n=e.VideoQuality.LOW;n<=e.VideoQuality.HIGH;n+=1)i.push(new cs({quality:n,enabled:n<=t}));this.log.debug("setting publishing quality. max quality ".concat(t),this.logContext),this.setPublishingLayers(i)}setDeviceId(e){return Ct(this,void 0,void 0,(function*(){return this._constraints.deviceId===e&&this._mediaStreamTrack.getSettings().deviceId===hr(e)||(this._constraints.deviceId=e,this.isMuted||(yield this.restartTrack()),this.isMuted||hr(e)===this._mediaStreamTrack.getSettings().deviceId)}))}restartTrack(e){var t,i,n,s;return Ct(this,void 0,void 0,(function*(){let r;if(e){const t=Is({video:e});"boolean"!=typeof t.video&&(r=t.video)}yield this.restart(r);try{for(var o,a=!0,c=wt(this.simulcastCodecs.values());o=yield c.next(),!(t=o.done);a=!0){s=o.value,a=!1;const e=s;e.sender&&(e.mediaStreamTrack=this.mediaStreamTrack.clone(),yield e.sender.replaceTrack(e.mediaStreamTrack))}}catch(e){i={error:e}}finally{try{a||t||!(n=c.return)||(yield n.call(c))}finally{if(i)throw i.error}}}))}setProcessor(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=Object.create(null,{setProcessor:{get:()=>super.setProcessor}});var n,s,r,o,a,c;return Ct(this,void 0,void 0,(function*(){if(yield i.setProcessor.call(this,e,t),null===(a=this.processor)||void 0===a?void 0:a.processedTrack)try{for(var d,l=!0,u=wt(this.simulcastCodecs.values());d=yield u.next(),!(n=d.done);l=!0){o=d.value,l=!1;const e=o;yield null===(c=e.sender)||void 0===c?void 0:c.replaceTrack(this.processor.processedTrack)}}catch(e){s={error:e}}finally{try{l||n||!(r=u.return)||(yield r.call(u))}finally{if(s)throw s.error}}}))}addSimulcastTrack(e,t){if(this.simulcastCodecs.has(e))throw new Error("".concat(e," already added"));const i={codec:e,mediaStreamTrack:this.mediaStreamTrack.clone(),sender:void 0,encodings:t};return this.simulcastCodecs.set(e,i),i}setSimulcastTrackSender(e,t){const i=this.simulcastCodecs.get(e);i&&(i.sender=t,setTimeout((()=>{this.subscribedCodecs&&this.setPublishingCodecs(this.subscribedCodecs)}),5e3))}setPublishingCodecs(e){var t,i,n,s,r,o,a;return Ct(this,void 0,void 0,(function*(){if(this.log.debug("setting publishing codecs",Object.assign(Object.assign({},this.logContext),{codecs:e,currentCodec:this.codec})),!this.codec&&e.length>0)return yield this.setPublishingLayers(e[0].qualities),[];this.subscribedCodecs=e;const c=[];try{for(t=!0,i=wt(e);n=yield i.next(),!(s=n.done);t=!0){a=n.value,t=!1;const e=a;if(this.codec&&this.codec!==e.codec){const t=this.simulcastCodecs.get(e.codec);if(this.log.debug("try setPublishingCodec for ".concat(e.codec),Object.assign(Object.assign({},this.logContext),{simulcastCodecInfo:t})),t&&t.sender)t.encodings&&(this.log.debug("try setPublishingLayersForSender ".concat(e.codec),this.logContext),yield Co(t.sender,t.encodings,e.qualities,this.senderLock,this.log,this.logContext));else for(const t of e.qualities)if(t.enabled){c.push(e.codec);break}}else yield this.setPublishingLayers(e.qualities)}}catch(e){r={error:e}}finally{try{t||s||!(o=i.return)||(yield o.call(i))}finally{if(r)throw r.error}}return c}))}setPublishingLayers(e){return Ct(this,void 0,void 0,(function*(){this.log.debug("setting publishing layers",Object.assign(Object.assign({},this.logContext),{qualities:e})),this.sender&&this.encodings&&(yield Co(this.sender,this.encodings,e,this.senderLock,this.log,this.logContext))}))}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Ct(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),Gs()&&this.isInBackground&&this.source===Cs.Source.Camera&&(this._mediaStreamTrack.enabled=!1)}))}}function Co(e,t,i,n,s,r){return Ct(this,void 0,void 0,(function*(){const o=yield n.lock();s.debug("setPublishingLayersForSender",Object.assign(Object.assign({},r),{sender:e,qualities:i,senderEncodings:t}));try{const n=e.getParameters(),{encodings:o}=n;if(!o)return;if(o.length!==t.length)return void s.warn("cannot set publishing layers, encodings mismatch");let a=!1;!1&&o[0].scalabilityMode||o.forEach(((e,n)=>{var o;let c=null!==(o=e.rid)&&void 0!==o?o:"";""===c&&(c="q");const d=Eo(c),l=i.find((e=>e.quality===d));l&&e.active!==l.enabled&&(a=!0,e.active=l.enabled,s.debug("setting layer ".concat(l.quality," to ").concat(e.active?"enabled":"disabled"),r),qs()&&(l.enabled?(e.scaleResolutionDownBy=t[n].scaleResolutionDownBy,e.maxBitrate=t[n].maxBitrate,e.maxFrameRate=t[n].maxFrameRate):(e.scaleResolutionDownBy=4,e.maxBitrate=10,e.maxFrameRate=2)))})),a&&(n.encodings=o,s.debug("setting encodings",Object.assign(Object.assign({},r),{encodings:n.encodings})),yield e.setParameters(n))}finally{o()}}))}function Eo(t){switch(t){case"f":default:return e.VideoQuality.HIGH;case"h":return e.VideoQuality.MEDIUM;case"q":return e.VideoQuality.LOW}}function wo(t,i,n,s){if(!n)return[new ot({quality:e.VideoQuality.HIGH,width:t,height:i,bitrate:0,ssrc:0})];if(s){const s=n[0].scalabilityMode,r=new To(s),o=[];for(let s=0;s<r.spatial;s+=1)o.push(new ot({quality:e.VideoQuality.HIGH-s,width:Math.ceil(t/Math.pow(2,s)),height:Math.ceil(i/Math.pow(2,s)),bitrate:n[0].maxBitrate?Math.ceil(n[0].maxBitrate/Math.pow(3,s)):0,ssrc:0}));return o}return n.map((e=>{var n,s,r;const o=null!==(n=e.scaleResolutionDownBy)&&void 0!==n?n:1;let a=Eo(null!==(s=e.rid)&&void 0!==s?s:"");return new ot({quality:a,width:Math.ceil(t/o),height:Math.ceil(i/o),bitrate:null!==(r=e.maxBitrate)&&void 0!==r?r:0,ssrc:0})}))}class Po extends Cs{constructor(e,t,i,n,s){super(e,i,s),this.sid=t,this.receiver=n}setMuted(t){this.isMuted!==t&&(this.isMuted=t,this._mediaStreamTrack.enabled=!t,this.emit(t?e.TrackEvent.Muted:e.TrackEvent.Unmuted,this))}setMediaStream(t){this.mediaStream=t;const i=n=>{n.track===this._mediaStreamTrack&&(t.removeEventListener("removetrack",i),this.receiver=void 0,this._currentBitrate=0,this.emit(e.TrackEvent.Ended,this))};t.addEventListener("removetrack",i)}start(){this.startMonitor(),super.enable()}stop(){this.stopMonitor(),super.disable()}getRTCStatsReport(){var e;return Ct(this,void 0,void 0,(function*(){if(!(null===(e=this.receiver)||void 0===e?void 0:e.getStats))return;return yield this.receiver.getStats()}))}startMonitor(){this.monitorInterval||(this.monitorInterval=setInterval((()=>this.monitorReceiver()),ro))}}class Ro extends Po{constructor(e,t,i,n,s,r){super(e,t,Cs.Kind.Audio,i,r),this.monitorReceiver=()=>Ct(this,void 0,void 0,(function*(){if(!this.receiver)return void(this._currentBitrate=0);const e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=oo(e,this.prevStats)),this.prevStats=e})),this.audioContext=n,this.webAudioPluginNodes=[],s&&(this.sinkId=s.deviceId)}setVolume(e){var t;for(const i of this.attachedElements)this.audioContext?null===(t=this.gainNode)||void 0===t||t.gain.setTargetAtTime(e,0,.1):i.volume=e;zs()&&this._mediaStreamTrack._setVolume(e),this.elementVolume=e}getVolume(){if(this.elementVolume)return this.elementVolume;if(zs())return 1;let e=0;return this.attachedElements.forEach((t=>{t.volume>e&&(e=t.volume)})),e}setSinkId(e){return Ct(this,void 0,void 0,(function*(){this.sinkId=e,yield Promise.all(this.attachedElements.map((t=>{if(Js(t))return t.setSinkId(e)})))}))}attach(e){const t=0===this.attachedElements.length;return e?super.attach(e):e=super.attach(),this.sinkId&&Js(e)&&e.setSinkId(this.sinkId),this.audioContext&&t&&(this.log.debug("using audio context mapping",this.logContext),this.connectWebAudio(this.audioContext,e),e.volume=0,e.muted=!0),this.elementVolume&&this.setVolume(this.elementVolume),e}detach(e){let t;return e?(t=super.detach(e),this.audioContext&&(this.attachedElements.length>0?this.connectWebAudio(this.audioContext,this.attachedElements[0]):this.disconnectWebAudio())):(t=super.detach(),this.disconnectWebAudio()),t}setAudioContext(e){this.audioContext=e,e&&this.attachedElements.length>0?this.connectWebAudio(e,this.attachedElements[0]):e||this.disconnectWebAudio()}setWebAudioPlugins(e){this.webAudioPluginNodes=e,this.attachedElements.length>0&&this.audioContext&&this.connectWebAudio(this.audioContext,this.attachedElements[0])}connectWebAudio(t,i){this.disconnectWebAudio(),this.sourceNode=t.createMediaStreamSource(i.srcObject);let n=this.sourceNode;this.webAudioPluginNodes.forEach((e=>{n.connect(e),n=e})),this.gainNode=t.createGain(),n.connect(this.gainNode),this.gainNode.connect(t.destination),this.elementVolume&&this.gainNode.gain.setTargetAtTime(this.elementVolume,0,.1),"running"!==t.state&&t.resume().then((()=>{"running"!==t.state&&this.emit(e.TrackEvent.AudioPlaybackFailed,new Error("Audio Context couldn't be started automatically"))})).catch((t=>{this.emit(e.TrackEvent.AudioPlaybackFailed,t)}))}disconnectWebAudio(){var e,t;null===(e=this.gainNode)||void 0===e||e.disconnect(),null===(t=this.sourceNode)||void 0===t||t.disconnect(),this.gainNode=void 0,this.sourceNode=void 0}getReceiverStats(){return Ct(this,void 0,void 0,(function*(){if(!this.receiver||!this.receiver.getStats)return;let e;return(yield this.receiver.getStats()).forEach((t=>{"inbound-rtp"===t.type&&(e={type:"audio",timestamp:t.timestamp,jitter:t.jitter,bytesReceived:t.bytesReceived,concealedSamples:t.concealedSamples,concealmentEvents:t.concealmentEvents,silentConcealedSamples:t.silentConcealedSamples,silentConcealmentEvents:t.silentConcealmentEvents,totalAudioEnergy:t.totalAudioEnergy,totalSamplesDuration:t.totalSamplesDuration})})),e}))}}class Io extends Po{constructor(e,t,i,n,s){super(e,t,Cs.Kind.Video,i,s),this.elementInfos=[],this.monitorReceiver=()=>Ct(this,void 0,void 0,(function*(){if(!this.receiver)return void(this._currentBitrate=0);const e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=oo(e,this.prevStats)),this.prevStats=e})),this.debouncedHandleResize=kn((()=>{this.updateDimensions()}),100),this.adaptiveStreamSettings=n}get isAdaptiveStream(){return void 0!==this.adaptiveStreamSettings}get mediaStreamTrack(){return this._mediaStreamTrack}setMuted(e){super.setMuted(e),this.attachedElements.forEach((t=>{e?ws(this._mediaStreamTrack,t):Es(this._mediaStreamTrack,t)}))}attach(e){if(e?super.attach(e):e=super.attach(),this.adaptiveStreamSettings&&void 0===this.elementInfos.find((t=>t.element===e))){const t=new Oo(e);this.observeElementInfo(t)}return e}observeElementInfo(e){this.adaptiveStreamSettings&&void 0===this.elementInfos.find((t=>t===e))?(e.handleResize=()=>{this.debouncedHandleResize()},e.handleVisibilityChanged=()=>{this.updateVisibility()},this.elementInfos.push(e),e.observe(),this.debouncedHandleResize(),this.updateVisibility()):this.log.warn("visibility resize observer not triggered",this.logContext)}stopObservingElementInfo(e){if(!this.isAdaptiveStream)return void this.log.warn("stopObservingElementInfo ignored",this.logContext);const t=this.elementInfos.filter((t=>t===e));for(const e of t)e.stopObserving();this.elementInfos=this.elementInfos.filter((t=>t!==e)),this.updateVisibility(),this.debouncedHandleResize()}detach(e){let t=[];if(e)return this.stopObservingElement(e),super.detach(e);t=super.detach();for(const e of t)this.stopObservingElement(e);return t}getDecoderImplementation(){var e;return null===(e=this.prevStats)||void 0===e?void 0:e.decoderImplementation}getReceiverStats(){return Ct(this,void 0,void 0,(function*(){if(!this.receiver||!this.receiver.getStats)return;const e=yield this.receiver.getStats();let t,i="",n=new Map;return e.forEach((e=>{"inbound-rtp"===e.type?(i=e.codecId,t={type:"video",framesDecoded:e.framesDecoded,framesDropped:e.framesDropped,framesReceived:e.framesReceived,packetsReceived:e.packetsReceived,packetsLost:e.packetsLost,frameWidth:e.frameWidth,frameHeight:e.frameHeight,pliCount:e.pliCount,firCount:e.firCount,nackCount:e.nackCount,jitter:e.jitter,timestamp:e.timestamp,bytesReceived:e.bytesReceived,decoderImplementation:e.decoderImplementation}):"codec"===e.type&&n.set(e.id,e)})),t&&""!==i&&n.get(i)&&(t.mimeType=n.get(i).mimeType),t}))}stopObservingElement(e){const t=this.elementInfos.filter((t=>t.element===e));for(const e of t)this.stopObservingElementInfo(e)}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Ct(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),this.isAdaptiveStream&&this.updateVisibility()}))}updateVisibility(){var t,i;const n=this.elementInfos.reduce(((e,t)=>Math.max(e,t.visibilityChangedAt||0)),0),s=!(null!==(i=null===(t=this.adaptiveStreamSettings)||void 0===t?void 0:t.pauseVideoInBackground)&&void 0!==i&&!i)&&this.isInBackground,r=this.elementInfos.some((e=>e.pictureInPicture)),o=this.elementInfos.some((e=>e.visible))&&!s||r;this.lastVisible!==o&&(!o&&Date.now()-n<100?Pn.setTimeout((()=>{this.updateVisibility()}),100):(this.lastVisible=o,this.emit(e.TrackEvent.VisibilityChanged,o,this)))}updateDimensions(){var t,i;let n=0,s=0;const r=this.getPixelDensity();for(const e of this.elementInfos){const t=e.width()*r,i=e.height()*r;t+i>n+s&&(n=t,s=i)}(null===(t=this.lastDimensions)||void 0===t?void 0:t.width)===n&&(null===(i=this.lastDimensions)||void 0===i?void 0:i.height)===s||(this.lastDimensions={width:n,height:s},this.emit(e.TrackEvent.VideoDimensionsChanged,this.lastDimensions,this))}getPixelDensity(){var e;const t=null===(e=this.adaptiveStreamSettings)||void 0===e?void 0:e.pixelDensity;if("screen"===t)return Zs();if(!t){return Zs()>2?2:1}return t}}class Oo{get visible(){return this.isPiP||this.isIntersecting}get pictureInPicture(){return this.isPiP}constructor(e,t){this.onVisibilityChanged=e=>{var t;const{target:i,isIntersecting:n}=e;i===this.element&&(this.isIntersecting=n,this.visibilityChangedAt=Date.now(),null===(t=this.handleVisibilityChanged)||void 0===t||t.call(this))},this.onEnterPiP=()=>{var e;this.isPiP=!0,null===(e=this.handleVisibilityChanged)||void 0===e||e.call(this)},this.onLeavePiP=()=>{var e;this.isPiP=!1,null===(e=this.handleVisibilityChanged)||void 0===e||e.call(this)},this.element=e,this.isIntersecting=null!=t?t:Do(e),this.isPiP=Hs()&&document.pictureInPictureElement===e,this.visibilityChangedAt=0}width(){return this.element.clientWidth}height(){return this.element.clientHeight}observe(){this.isIntersecting=Do(this.element),this.isPiP=document.pictureInPictureElement===this.element,this.element.handleResize=()=>{var e;null===(e=this.handleResize)||void 0===e||e.call(this)},this.element.handleVisibilityChanged=this.onVisibilityChanged,rr().observe(this.element),nr().observe(this.element),this.element.addEventListener("enterpictureinpicture",this.onEnterPiP),this.element.addEventListener("leavepictureinpicture",this.onLeavePiP)}stopObserving(){var e,t;null===(e=rr())||void 0===e||e.unobserve(this.element),null===(t=nr())||void 0===t||t.unobserve(this.element),this.element.removeEventListener("enterpictureinpicture",this.onEnterPiP),this.element.removeEventListener("leavepictureinpicture",this.onLeavePiP)}}function Do(e){let t=e.offsetTop,i=e.offsetLeft;const n=e.offsetWidth,s=e.offsetHeight,{hidden:r}=e,{opacity:o,display:a}=getComputedStyle(e);for(;e.offsetParent;)t+=(e=e.offsetParent).offsetTop,i+=e.offsetLeft;return t<window.pageYOffset+window.innerHeight&&i<window.pageXOffset+window.innerWidth&&t+s>window.pageYOffset&&i+n>window.pageXOffset&&!r&&(""===o||parseFloat(o)>0)&&"none"!==a}class xo extends Vt.EventEmitter{constructor(t,i,n,s){var r;super(),this.metadataMuted=!1,this.encryption=$e.NONE,this.log=c,this.handleMuted=()=>{this.emit(e.TrackEvent.Muted)},this.handleUnmuted=()=>{this.emit(e.TrackEvent.Unmuted)},this.log=d(null!==(r=null==s?void 0:s.loggerName)&&void 0!==r?r:o.Publication),this.loggerContextCb=this.loggerContextCb,this.setMaxListeners(100),this.kind=t,this.trackSid=i,this.trackName=n,this.source=Cs.Source.Unknown}setTrack(t){this.track&&(this.track.off(e.TrackEvent.Muted,this.handleMuted),this.track.off(e.TrackEvent.Unmuted,this.handleUnmuted)),this.track=t,t&&(t.on(e.TrackEvent.Muted,this.handleMuted),t.on(e.TrackEvent.Unmuted,this.handleUnmuted))}get logContext(){var e;return Object.assign(Object.assign({},null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this)),_s(this))}get isMuted(){return this.metadataMuted}get isEnabled(){return!0}get isSubscribed(){return void 0!==this.track}get isEncrypted(){return this.encryption!==$e.NONE}get audioTrack(){if(this.track instanceof ao||this.track instanceof Ro)return this.track}get videoTrack(){if(this.track instanceof So||this.track instanceof Io)return this.track}updateInfo(e){this.trackSid=e.sid,this.trackName=e.name,this.source=Cs.sourceFromProto(e.source),this.mimeType=e.mimeType,this.kind===Cs.Kind.Video&&e.width>0&&(this.dimensions={width:e.width,height:e.height},this.simulcasted=e.simulcast),this.encryption=e.encryption,this.trackInfo=e,this.log.debug("update publication info",Object.assign(Object.assign({},this.logContext),{info:e}))}}!function(e){var t,i;(t=e.SubscriptionStatus||(e.SubscriptionStatus={})).Desired="desired",t.Subscribed="subscribed",t.Unsubscribed="unsubscribed",(i=e.PermissionStatus||(e.PermissionStatus={})).Allowed="allowed",i.NotAllowed="not_allowed"}(xo||(xo={}));class No extends xo{get isUpstreamPaused(){var e;return null===(e=this.track)||void 0===e?void 0:e.isUpstreamPaused}constructor(t,i,n,s){super(t,i.sid,i.name,s),this.track=void 0,this.handleTrackEnded=()=>{this.emit(e.TrackEvent.Ended)},this.updateInfo(i),this.setTrack(n)}setTrack(t){this.track&&this.track.off(e.TrackEvent.Ended,this.handleTrackEnded),super.setTrack(t),t&&t.on(e.TrackEvent.Ended,this.handleTrackEnded)}get isMuted(){return this.track?this.track.isMuted:super.isMuted}get audioTrack(){return super.audioTrack}get videoTrack(){return super.videoTrack}mute(){var e;return Ct(this,void 0,void 0,(function*(){return null===(e=this.track)||void 0===e?void 0:e.mute()}))}unmute(){var e;return Ct(this,void 0,void 0,(function*(){return null===(e=this.track)||void 0===e?void 0:e.unmute()}))}pauseUpstream(){var e;return Ct(this,void 0,void 0,(function*(){yield null===(e=this.track)||void 0===e?void 0:e.pauseUpstream()}))}resumeUpstream(){var e;return Ct(this,void 0,void 0,(function*(){yield null===(e=this.track)||void 0===e?void 0:e.resumeUpstream()}))}}e.ConnectionQuality=void 0,function(e){e.Excellent="excellent",e.Good="good",e.Poor="poor",e.Lost="lost",e.Unknown="unknown"}(e.ConnectionQuality||(e.ConnectionQuality={}));class _o extends Vt.EventEmitter{get logContext(){var e,t;return Object.assign(Object.assign({},null===(t=null===(e=this.loggerOptions)||void 0===e?void 0:e.loggerContextCb)||void 0===t?void 0:t.call(e)),{participantSid:this.sid,participantId:this.identity})}get isEncrypted(){return this.tracks.size>0&&Array.from(this.tracks.values()).every((e=>e.isEncrypted))}get isAgent(){var e,t;return null!==(t=null===(e=this.permissions)||void 0===e?void 0:e.agent)&&void 0!==t&&t}constructor(t,i,n,s,r){var a;super(),this.audioLevel=0,this.isSpeaking=!1,this._connectionQuality=e.ConnectionQuality.Unknown,this.log=c,this.log=d(null!==(a=null==r?void 0:r.loggerName)&&void 0!==a?a:o.Participant),this.loggerOptions=r,this.setMaxListeners(100),this.sid=t,this.identity=i,this.name=n,this.metadata=s,this.audioTracks=new Map,this.videoTracks=new Map,this.tracks=new Map}getTracks(){return Array.from(this.tracks.values())}getTrack(e){for(const[,t]of this.tracks)if(t.source===e)return t}getTrackByName(e){for(const[,t]of this.tracks)if(t.trackName===e)return t}get connectionQuality(){return this._connectionQuality}get isCameraEnabled(){var e;const t=this.getTrack(Cs.Source.Camera);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}get isMicrophoneEnabled(){var e;const t=this.getTrack(Cs.Source.Microphone);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}get isScreenShareEnabled(){return!!this.getTrack(Cs.Source.ScreenShare)}get isLocal(){return!1}get joinedAt(){return this.participantInfo?new Date(1e3*Number.parseInt(this.participantInfo.joinedAt.toString())):new Date}updateInfo(e){return!(this.participantInfo&&this.participantInfo.sid===e.sid&&this.participantInfo.version>e.version)&&(this.identity=e.identity,this.sid=e.sid,this._setName(e.name),this._setMetadata(e.metadata),e.permission&&this.setPermissions(e.permission),this.participantInfo=e,this.log.trace("update participant info",Object.assign(Object.assign({},this.logContext),{info:e})),!0)}_setMetadata(t){const i=this.metadata!==t,n=this.metadata;this.metadata=t,i&&this.emit(e.ParticipantEvent.ParticipantMetadataChanged,n)}_setName(t){const i=this.name!==t;this.name=t,i&&this.emit(e.ParticipantEvent.ParticipantNameChanged,t)}setPermissions(t){var i,n,s,r,o;const a=this.permissions,c=t.canPublish!==(null===(i=this.permissions)||void 0===i?void 0:i.canPublish)||t.canSubscribe!==(null===(n=this.permissions)||void 0===n?void 0:n.canSubscribe)||t.canPublishData!==(null===(s=this.permissions)||void 0===s?void 0:s.canPublishData)||t.hidden!==(null===(r=this.permissions)||void 0===r?void 0:r.hidden)||t.recorder!==(null===(o=this.permissions)||void 0===o?void 0:o.recorder)||t.canPublishSources.length!==this.permissions.canPublishSources.length||t.canPublishSources.some(((e,t)=>{var i;return e!==(null===(i=this.permissions)||void 0===i?void 0:i.canPublishSources[t])}));return this.permissions=t,c&&this.emit(e.ParticipantEvent.ParticipantPermissionsChanged,a),c}setIsSpeaking(t){t!==this.isSpeaking&&(this.isSpeaking=t,t&&(this.lastSpokeAt=new Date),this.emit(e.ParticipantEvent.IsSpeakingChanged,t))}setConnectionQuality(t){const i=this._connectionQuality;this._connectionQuality=function(t){switch(t){case Fe.EXCELLENT:return e.ConnectionQuality.Excellent;case Fe.GOOD:return e.ConnectionQuality.Good;case Fe.POOR:return e.ConnectionQuality.Poor;case Fe.LOST:return e.ConnectionQuality.Lost;default:return e.ConnectionQuality.Unknown}}(t),i!==this._connectionQuality&&this.emit(e.ParticipantEvent.ConnectionQualityChanged,this._connectionQuality)}setAudioContext(e){this.audioContext=e,this.audioTracks.forEach((t=>(t.track instanceof Ro||t.track instanceof ao)&&t.track.setAudioContext(e)))}addTrackPublication(t){t.on(e.TrackEvent.Muted,(()=>{this.emit(e.ParticipantEvent.TrackMuted,t)})),t.on(e.TrackEvent.Unmuted,(()=>{this.emit(e.ParticipantEvent.TrackUnmuted,t)}));const i=t;switch(i.track&&(i.track.sid=t.trackSid),this.tracks.set(t.trackSid,t),t.kind){case Cs.Kind.Audio:this.audioTracks.set(t.trackSid,t);break;case Cs.Kind.Video:this.videoTracks.set(t.trackSid,t)}}}class Mo extends xo{constructor(t,i,n,s){super(t,i.sid,i.name,s),this.track=void 0,this.allowed=!0,this.disabled=!1,this.currentVideoQuality=e.VideoQuality.HIGH,this.handleEnded=t=>{this.setTrack(void 0),this.emit(e.TrackEvent.Ended,t)},this.handleVisibilityChange=e=>{this.log.debug("adaptivestream video visibility ".concat(this.trackSid,", visible=").concat(e),this.logContext),this.disabled=!e,this.emitTrackUpdate()},this.handleVideoDimensionsChange=e=>{this.log.debug("adaptivestream video dimensions ".concat(e.width,"x").concat(e.height),this.logContext),this.videoDimensions=e,this.emitTrackUpdate()},this.subscribed=n,this.updateInfo(i)}setSubscribed(t){const i=this.subscriptionStatus,n=this.permissionStatus;this.subscribed=t,t&&(this.allowed=!0);const s=new Yn({trackSids:[this.trackSid],subscribe:this.subscribed,participantTracks:[new ut({participantSid:"",trackSids:[this.trackSid]})]});this.emit(e.TrackEvent.UpdateSubscription,s),this.emitSubscriptionUpdateIfChanged(i),this.emitPermissionUpdateIfChanged(n)}get subscriptionStatus(){return!1===this.subscribed?xo.SubscriptionStatus.Unsubscribed:super.isSubscribed?xo.SubscriptionStatus.Subscribed:xo.SubscriptionStatus.Desired}get permissionStatus(){return this.allowed?xo.PermissionStatus.Allowed:xo.PermissionStatus.NotAllowed}get isSubscribed(){return!1!==this.subscribed&&super.isSubscribed}get isDesired(){return!1!==this.subscribed}get isEnabled(){return!this.disabled}setEnabled(e){this.isManualOperationAllowed()&&this.disabled!==!e&&(this.disabled=!e,this.emitTrackUpdate())}setVideoQuality(e){this.isManualOperationAllowed()&&this.currentVideoQuality!==e&&(this.currentVideoQuality=e,this.videoDimensions=void 0,this.emitTrackUpdate())}setVideoDimensions(e){var t,i;this.isManualOperationAllowed()&&((null===(t=this.videoDimensions)||void 0===t?void 0:t.width)===e.width&&(null===(i=this.videoDimensions)||void 0===i?void 0:i.height)===e.height||(this.track instanceof Io&&(this.videoDimensions=e),this.currentVideoQuality=void 0,this.emitTrackUpdate()))}setVideoFPS(e){this.isManualOperationAllowed()&&this.track instanceof Io&&this.fps!==e&&(this.fps=e,this.emitTrackUpdate())}get videoQuality(){return this.currentVideoQuality}setTrack(t){const i=this.subscriptionStatus,n=this.permissionStatus,s=this.track;s!==t&&(s&&(s.off(e.TrackEvent.VideoDimensionsChanged,this.handleVideoDimensionsChange),s.off(e.TrackEvent.VisibilityChanged,this.handleVisibilityChange),s.off(e.TrackEvent.Ended,this.handleEnded),s.detach(),s.stopMonitor(),this.emit(e.TrackEvent.Unsubscribed,s)),super.setTrack(t),t&&(t.sid=this.trackSid,t.on(e.TrackEvent.VideoDimensionsChanged,this.handleVideoDimensionsChange),t.on(e.TrackEvent.VisibilityChanged,this.handleVisibilityChange),t.on(e.TrackEvent.Ended,this.handleEnded),this.emit(e.TrackEvent.Subscribed,t)),this.emitPermissionUpdateIfChanged(n),this.emitSubscriptionUpdateIfChanged(i))}setAllowed(e){const t=this.subscriptionStatus,i=this.permissionStatus;this.allowed=e,this.emitPermissionUpdateIfChanged(i),this.emitSubscriptionUpdateIfChanged(t)}setSubscriptionError(t){this.emit(e.TrackEvent.SubscriptionFailed,t)}updateInfo(t){super.updateInfo(t);const i=this.metadataMuted;this.metadataMuted=t.muted,this.track?this.track.setMuted(t.muted):i!==t.muted&&this.emit(t.muted?e.TrackEvent.Muted:e.TrackEvent.Unmuted)}emitSubscriptionUpdateIfChanged(t){const i=this.subscriptionStatus;t!==i&&this.emit(e.TrackEvent.SubscriptionStatusChanged,i,t)}emitPermissionUpdateIfChanged(t){this.permissionStatus!==t&&this.emit(e.TrackEvent.SubscriptionPermissionChanged,this.permissionStatus,t)}isManualOperationAllowed(){return this.kind===Cs.Kind.Video&&this.isAdaptiveStream?(this.log.warn("adaptive stream is enabled, cannot change video track settings",this.logContext),!1):!!this.isDesired||(this.log.warn("cannot update track settings when not subscribed",this.logContext),!1)}get isAdaptiveStream(){return this.track instanceof Io&&this.track.isAdaptiveStream}emitTrackUpdate(){const t=new Xn({trackSids:[this.trackSid],disabled:this.disabled,fps:this.fps});this.videoDimensions?(t.width=Math.ceil(this.videoDimensions.width),t.height=Math.ceil(this.videoDimensions.height)):void 0!==this.currentVideoQuality?t.quality=this.currentVideoQuality:t.quality=e.VideoQuality.HIGH,this.emit(e.TrackEvent.UpdateSettings,t)}}class Lo extends _o{static fromParticipantInfo(e,t){return new Lo(e,t.sid,t.identity,t.name,t.metadata)}constructor(e,t,i,n,s,r){super(t,i||"",n,s,r),this.signalClient=e,this.tracks=new Map,this.audioTracks=new Map,this.videoTracks=new Map,this.volumeMap=new Map}addTrackPublication(t){super.addTrackPublication(t),t.on(e.TrackEvent.UpdateSettings,(e=>{this.log.debug("send update settings",Object.assign(Object.assign({},this.logContext),_s(t))),this.signalClient.sendUpdateTrackSettings(e)})),t.on(e.TrackEvent.UpdateSubscription,(e=>{e.participantTracks.forEach((e=>{e.participantSid=this.sid})),this.signalClient.sendUpdateSubscription(e)})),t.on(e.TrackEvent.SubscriptionPermissionChanged,(i=>{this.emit(e.ParticipantEvent.TrackSubscriptionPermissionChanged,t,i)})),t.on(e.TrackEvent.SubscriptionStatusChanged,(i=>{this.emit(e.ParticipantEvent.TrackSubscriptionStatusChanged,t,i)})),t.on(e.TrackEvent.Subscribed,(i=>{this.emit(e.ParticipantEvent.TrackSubscribed,i,t)})),t.on(e.TrackEvent.Unsubscribed,(i=>{this.emit(e.ParticipantEvent.TrackUnsubscribed,i,t)})),t.on(e.TrackEvent.SubscriptionFailed,(i=>{this.emit(e.ParticipantEvent.TrackSubscriptionFailed,t.trackSid,i)}))}getTrack(e){const t=super.getTrack(e);if(t)return t}getTrackByName(e){const t=super.getTrackByName(e);if(t)return t}setVolume(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Cs.Source.Microphone;this.volumeMap.set(t,e);const i=this.getTrack(t);i&&i.track&&i.track.setVolume(e)}getVolume(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Cs.Source.Microphone;const t=this.getTrack(e);return t&&t.track?t.track.getVolume():this.volumeMap.get(e)}addSubscribedMediaTrack(t,i,n,s,r,o){let a=this.getTrackPublication(i);if(a||i.startsWith("TR")||this.tracks.forEach((e=>{a||t.kind!==e.kind.toString()||(a=e)})),!a)return 0===o?(this.log.error("could not find published track",Object.assign(Object.assign({},this.logContext),{trackSid:i})),void this.emit(e.ParticipantEvent.TrackSubscriptionFailed,i)):(void 0===o&&(o=20),void setTimeout((()=>{this.addSubscribedMediaTrack(t,i,n,s,r,o-1)}),150));if("ended"===t.readyState)return this.log.error("unable to subscribe because MediaStreamTrack is ended. Do not call MediaStreamTrack.stop()",Object.assign(Object.assign({},this.logContext),_s(a))),void this.emit(e.ParticipantEvent.TrackSubscriptionFailed,i);let c;return c="video"===t.kind?new Io(t,i,s,r):new Ro(t,i,s,this.audioContext,this.audioOutput),c.source=a.source,c.isMuted=a.isMuted,c.setMediaStream(n),c.start(),a.setTrack(c),this.volumeMap.has(a.source)&&c instanceof Ro&&c.setVolume(this.volumeMap.get(a.source)),a}get hasMetadata(){return!!this.participantInfo}getTrackPublication(e){return this.tracks.get(e)}updateInfo(t){if(!super.updateInfo(t))return!1;const i=new Map,n=new Map;return t.tracks.forEach((e=>{var t,s;let r=this.getTrackPublication(e.sid);if(r)r.updateInfo(e);else{const i=Cs.kindFromProto(e.type);if(!i)return;r=new Mo(i,e,null===(t=this.signalClient.connectOptions)||void 0===t?void 0:t.autoSubscribe,{loggerContextCb:()=>this.logContext,loggerName:null===(s=this.loggerOptions)||void 0===s?void 0:s.loggerName}),r.updateInfo(e),n.set(e.sid,r);const o=Array.from(this.tracks.values()).find((e=>e.source===(null==r?void 0:r.source)));o&&r.source!==Cs.Source.Unknown&&this.log.debug("received a second track publication for ".concat(this.identity," with the same source: ").concat(r.source),Object.assign(Object.assign({},this.logContext),{oldTrack:_s(o),newTrack:_s(r)})),this.addTrackPublication(r)}i.set(e.sid,r)})),this.tracks.forEach((e=>{i.has(e.trackSid)||(this.log.trace("detected removed track on remote participant, unpublishing",Object.assign(Object.assign({},this.logContext),_s(e))),this.unpublishTrack(e.trackSid,!0))})),n.forEach((t=>{this.emit(e.ParticipantEvent.TrackPublished,t)})),!0}unpublishTrack(t,i){const n=this.tracks.get(t);if(!n)return;const{track:s}=n;switch(s&&(s.stop(),n.setTrack(void 0)),this.tracks.delete(t),n.kind){case Cs.Kind.Audio:this.audioTracks.delete(t);break;case Cs.Kind.Video:this.videoTracks.delete(t)}i&&this.emit(e.ParticipantEvent.TrackUnpublished,n)}setAudioOutput(e){return Ct(this,void 0,void 0,(function*(){this.audioOutput=e;const t=[];this.audioTracks.forEach((i=>{var n;i.track instanceof Ro&&t.push(i.track.setSinkId(null!==(n=e.deviceId)&&void 0!==n?n:"default"))})),yield Promise.all(t)}))}emit(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return this.log.trace("participant event",Object.assign(Object.assign({},this.logContext),{event:e,args:i})),super.emit(e,...i)}}class Ao extends _o{constructor(e,t,i,n){super(e,t,void 0,void 0,{loggerName:n.loggerName,loggerContextCb:()=>this.engine.logContext}),this.pendingPublishing=new Set,this.pendingPublishPromises=new Map,this.participantTrackPermissions=[],this.allParticipantsAllowedToSubscribe=!0,this.encryptionType=$e.NONE,this.handleReconnecting=()=>{this.reconnectFuture||(this.reconnectFuture=new lr)},this.handleReconnected=()=>{var e,t;null===(t=null===(e=this.reconnectFuture)||void 0===e?void 0:e.resolve)||void 0===t||t.call(e),this.reconnectFuture=void 0,this.updateTrackSubscriptionPermissions()},this.handleDisconnected=()=>{var e,t;this.reconnectFuture&&(this.reconnectFuture.promise.catch((e=>this.log.warn(e.message,this.logContext))),null===(t=null===(e=this.reconnectFuture)||void 0===e?void 0:e.reject)||void 0===t||t.call(e,"Got disconnected during reconnection attempt"),this.reconnectFuture=void 0)},this.updateTrackSubscriptionPermissions=()=>{this.log.debug("updating track subscription permissions",Object.assign(Object.assign({},this.logContext),{allParticipantsAllowed:this.allParticipantsAllowedToSubscribe,participantTrackPermissions:this.participantTrackPermissions})),this.engine.client.sendUpdateSubscriptionPermissions(this.allParticipantsAllowedToSubscribe,this.participantTrackPermissions.map((e=>function(e){var t,i,n;if(!e.participantSid&&!e.participantIdentity)throw new Error("Invalid track permission, must provide at least one of participantIdentity and participantSid");return new us({participantIdentity:null!==(t=e.participantIdentity)&&void 0!==t?t:"",participantSid:null!==(i=e.participantSid)&&void 0!==i?i:"",allTracks:null!==(n=e.allowAll)&&void 0!==n&&n,trackSids:e.allowedTrackSids||[]})}(e))))},this.onTrackUnmuted=e=>{this.onTrackMuted(e,e.isUpstreamPaused)},this.onTrackMuted=(e,t)=>{void 0===t&&(t=!0),e.sid?this.engine.updateMuteStatus(e.sid,t):this.log.error("could not update mute status for unpublished track",Object.assign(Object.assign({},this.logContext),_s(e)))},this.onTrackUpstreamPaused=e=>{this.log.debug("upstream paused",Object.assign(Object.assign({},this.logContext),_s(e))),this.onTrackMuted(e,!0)},this.onTrackUpstreamResumed=e=>{this.log.debug("upstream resumed",Object.assign(Object.assign({},this.logContext),_s(e))),this.onTrackMuted(e,e.isMuted)},this.handleSubscribedQualityUpdate=e=>Ct(this,void 0,void 0,(function*(){var t,i,n,s,r,o;if(!(null===(r=this.roomOptions)||void 0===r?void 0:r.dynacast))return;const a=this.videoTracks.get(e.trackSid);if(a)if(e.subscribedCodecs.length>0){if(!a.videoTrack)return;const r=yield a.videoTrack.setPublishingCodecs(e.subscribedCodecs);try{for(var c,d=!0,l=wt(r);!(t=(c=yield l.next()).done);d=!0){s=c.value,d=!1;const e=s;Dn(e)&&(this.log.debug("publish ".concat(e," for ").concat(a.videoTrack.sid),Object.assign(Object.assign({},this.logContext),_s(a))),yield this.publishAdditionalCodecForTrack(a.videoTrack,e,a.options))}}catch(e){i={error:e}}finally{try{d||t||!(n=l.return)||(yield n.call(l))}finally{if(i)throw i.error}}}else e.subscribedQualities.length>0&&(yield null===(o=a.videoTrack)||void 0===o?void 0:o.setPublishingLayers(e.subscribedQualities));else this.log.warn("received subscribed quality update for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}))})),this.handleLocalTrackUnpublished=e=>{const t=this.tracks.get(e.trackSid);t?this.unpublishTrack(t.track):this.log.warn("received unpublished event for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}))},this.handleTrackEnded=e=>Ct(this,void 0,void 0,(function*(){if(e.source===Cs.Source.ScreenShare||e.source===Cs.Source.ScreenShareAudio)this.log.debug("unpublishing local track due to TrackEnded",Object.assign(Object.assign({},this.logContext),_s(e))),this.unpublishTrack(e);else if(e.isUserProvided)yield e.mute();else if(e instanceof ao||e instanceof So)try{if(Hs())try{const t=yield null===navigator||void 0===navigator?void 0:navigator.permissions.query({name:e.source===Cs.Source.Camera?"camera":"microphone"});if(t&&"denied"===t.state)throw this.log.warn("user has revoked access to ".concat(e.source),Object.assign(Object.assign({},this.logContext),_s(e))),t.onchange=()=>{"denied"!==t.state&&(e.isMuted||e.restartTrack(),t.onchange=null)},new Error("GetUserMedia Permission denied")}catch(e){}e.isMuted||(this.log.debug("track ended, attempting to use a different device",Object.assign(Object.assign({},this.logContext),_s(e))),yield e.restartTrack())}catch(t){this.log.warn("could not restart track, muting instead",Object.assign(Object.assign({},this.logContext),_s(e))),yield e.mute()}})),this.audioTracks=new Map,this.videoTracks=new Map,this.tracks=new Map,this.engine=i,this.roomOptions=n,this.setupEngine(i),this.activeDeviceMap=new Map}get lastCameraError(){return this.cameraError}get lastMicrophoneError(){return this.microphoneError}get isE2EEEnabled(){return this.encryptionType!==$e.NONE}getTrack(e){const t=super.getTrack(e);if(t)return t}getTrackByName(e){const t=super.getTrackByName(e);if(t)return t}setupEngine(t){this.engine=t,this.engine.on(e.EngineEvent.RemoteMute,((e,t)=>{const i=this.tracks.get(e);i&&i.track&&(t?i.mute():i.unmute())})),this.engine.on(e.EngineEvent.Connected,this.handleReconnected).on(e.EngineEvent.SignalRestarted,this.handleReconnected).on(e.EngineEvent.SignalResumed,this.handleReconnected).on(e.EngineEvent.Restarting,this.handleReconnecting).on(e.EngineEvent.Resuming,this.handleReconnecting).on(e.EngineEvent.LocalTrackUnpublished,this.handleLocalTrackUnpublished).on(e.EngineEvent.SubscribedQualityUpdate,this.handleSubscribedQualityUpdate).on(e.EngineEvent.Disconnected,this.handleDisconnected)}setMetadata(e){var t;this.engine.client.sendUpdateLocalMetadata(e,null!==(t=this.name)&&void 0!==t?t:"")}setName(e){var t;this.engine.client.sendUpdateLocalMetadata(null!==(t=this.metadata)&&void 0!==t?t:"",e)}setCameraEnabled(e,t,i){return this.setTrackEnabled(Cs.Source.Camera,e,t,i)}setMicrophoneEnabled(e,t,i){return this.setTrackEnabled(Cs.Source.Microphone,e,t,i)}setScreenShareEnabled(e,t,i){return this.setTrackEnabled(Cs.Source.ScreenShare,e,t,i)}setPermissions(t){const i=this.permissions,n=super.setPermissions(t);return n&&i&&this.emit(e.ParticipantEvent.ParticipantPermissionsChanged,i),n}setE2EEEnabled(e){return Ct(this,void 0,void 0,(function*(){this.encryptionType=e?$e.GCM:$e.NONE,yield this.republishAllTracks(void 0,!1)}))}setTrackEnabled(t,i,n,s){var r,o;return Ct(this,void 0,void 0,(function*(){this.log.debug("setTrackEnabled",Object.assign(Object.assign({},this.logContext),{source:t,enabled:i}));let a=this.getTrack(t);if(i)if(a)yield a.unmute();else{let i;if(this.pendingPublishing.has(t))return void this.log.info("skipping duplicate published source",Object.assign(Object.assign({},this.logContext),{source:t}));this.pendingPublishing.add(t);try{switch(t){case Cs.Source.Camera:i=yield this.createTracks({video:null===(r=n)||void 0===r||r});break;case Cs.Source.Microphone:i=yield this.createTracks({audio:null===(o=n)||void 0===o||o});break;case Cs.Source.ScreenShare:i=yield this.createScreenTracks(Object.assign({},n));break;default:throw new ln(t)}const e=[];for(const t of i)this.log.info("publishing track",Object.assign(Object.assign({},this.logContext),_s(t))),e.push(this.publishTrack(t,s));const c=yield Promise.all(e);[a]=c}catch(t){throw null==i||i.forEach((e=>{e.stop()})),t instanceof Error&&!(t instanceof ln)&&this.emit(e.ParticipantEvent.MediaDevicesError,t),t}finally{this.pendingPublishing.delete(t)}}else if(a&&a.track)if(t===Cs.Source.ScreenShare){a=yield this.unpublishTrack(a.track);const e=this.getTrack(Cs.Source.ScreenShareAudio);e&&e.track&&this.unpublishTrack(e.track)}else yield a.mute();return a}))}enableCameraAndMicrophone(){return Ct(this,void 0,void 0,(function*(){if(!this.pendingPublishing.has(Cs.Source.Camera)&&!this.pendingPublishing.has(Cs.Source.Microphone)){this.pendingPublishing.add(Cs.Source.Camera),this.pendingPublishing.add(Cs.Source.Microphone);try{const e=yield this.createTracks({audio:!0,video:!0});yield Promise.all(e.map((e=>this.publishTrack(e))))}finally{this.pendingPublishing.delete(Cs.Source.Camera),this.pendingPublishing.delete(Cs.Source.Microphone)}}}))}createTracks(t){var i,n;return Ct(this,void 0,void 0,(function*(){const s=Is(Ps(t,null===(i=this.roomOptions)||void 0===i?void 0:i.audioCaptureDefaults,null===(n=this.roomOptions)||void 0===n?void 0:n.videoCaptureDefaults));let r;try{r=yield navigator.mediaDevices.getUserMedia(s)}catch(e){throw e instanceof Error&&(s.audio&&(this.microphoneError=e),s.video&&(this.cameraError=e)),e}return s.audio&&(this.microphoneError=void 0,this.emit(e.ParticipantEvent.AudioStreamAcquired)),s.video&&(this.cameraError=void 0),r.getTracks().map((e=>{const i="audio"===e.kind;let n;i?t.audio:t.video;const o=i?s.audio:s.video;"boolean"!=typeof o&&(n=o);const a=co(e,n,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});return a.kind===Cs.Kind.Video?a.source=Cs.Source.Camera:a.kind===Cs.Kind.Audio&&(a.source=Cs.Source.Microphone),a.mediaStream=r,a}))}))}createScreenTracks(t){return Ct(this,void 0,void 0,(function*(){if(void 0===t&&(t={}),void 0===navigator.mediaDevices.getDisplayMedia)throw new dn("getDisplayMedia not supported");void 0!==t.resolution||Ws()||(t.resolution=Mn.h1080fps30.resolution);const i=Ds(t),n=yield navigator.mediaDevices.getDisplayMedia(i),s=n.getVideoTracks();if(0===s.length)throw new ln("no video track found");const r=new So(s[0],void 0,!1,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});r.source=Cs.Source.ScreenShare,t.contentHint&&(r.mediaStreamTrack.contentHint=t.contentHint);const o=[r];if(n.getAudioTracks().length>0){this.emit(e.ParticipantEvent.AudioStreamAcquired);const t=new ao(n.getAudioTracks()[0],void 0,!1,this.audioContext,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});t.source=Cs.Source.ScreenShareAudio,o.push(t)}return o}))}publishTrack(e,t){var i,n,s,r;return Ct(this,void 0,void 0,(function*(){let o,a;if(yield null===(i=this.reconnectFuture)||void 0===i?void 0:i.promise,e instanceof fr&&this.pendingPublishPromises.has(e)&&(yield this.pendingPublishPromises.get(e)),e instanceof MediaStreamTrack)o=e.getConstraints();else{let t;switch(o=e.constraints,e.source){case Cs.Source.Microphone:t="audioinput";break;case Cs.Source.Camera:t="videoinput"}t&&this.activeDeviceMap.has(t)&&(o=Object.assign(Object.assign({},o),{deviceId:this.activeDeviceMap.get(t)}))}if(e instanceof MediaStreamTrack)switch(e.kind){case"audio":e=new ao(e,o,!0,this.audioContext,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});break;case"video":e=new So(e,o,!0,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});break;default:throw new ln("unsupported MediaStreamTrack kind ".concat(e.kind))}else e.updateLoggerOptions({loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});if(e instanceof ao&&e.setAudioContext(this.audioContext),this.tracks.forEach((t=>{t.track&&t.track===e&&(a=t)})),a)return this.log.warn("track has already been published, skipping",Object.assign(Object.assign({},this.logContext),_s(a))),a;const c="channelCount"in e.mediaStreamTrack.getSettings()&&2===e.mediaStreamTrack.getSettings().channelCount||2===e.mediaStreamTrack.getConstraints().channelCount,d=null!==(n=null==t?void 0:t.forceStereo)&&void 0!==n?n:c;d&&(t||(t={}),void 0===t.dtx&&this.log.info("Opus DTX will be disabled for stereo tracks by default. Enable them explicitly to make it work.",Object.assign(Object.assign({},this.logContext),_s(e))),void 0===t.red&&this.log.info("Opus RED will be disabled for stereo tracks by default. Enable them explicitly to make it work."),null!==(s=t.dtx)&&void 0!==s||(t.dtx=!1),null!==(r=t.red)&&void 0!==r||(t.red=!1));const l=Object.assign(Object.assign({},this.roomOptions.publishDefaults),t);Ks()&&this.roomOptions.e2ee&&(this.log.info("End-to-end encryption is set up, simulcast publishing will be disabled on Safari",Object.assign({},this.logContext)),l.simulcast=!1),l.source&&(e.source=l.source);const u=this.publish(e,l,d);this.pendingPublishPromises.set(e,u);try{return yield u}catch(e){throw e}finally{this.pendingPublishPromises.delete(e)}}))}publish(t,i,n){var s,r,o,a,c,d,l,u,h,p,m,g,f;return Ct(this,void 0,void 0,(function*(){Array.from(this.tracks.values()).find((e=>t instanceof fr&&e.source===t.source))&&t.source!==Cs.Source.Unknown&&this.log.info("publishing a second track with the same source: ".concat(t.source),Object.assign(Object.assign({},this.logContext),_s(t))),i.stopMicTrackOnMute&&t instanceof ao&&(t.stopOnMute=!0),t.source===Cs.Source.ScreenShare&&qs()&&(i.simulcast=!1),"av1"!==i.videoCodec||js()||(i.videoCodec=void 0),"vp9"!==i.videoCodec||Bs()||(i.videoCodec=void 0),void 0===i.videoCodec&&(i.videoCodec=Kr);const v=i.videoCodec;t.on(e.TrackEvent.Muted,this.onTrackMuted),t.on(e.TrackEvent.Unmuted,this.onTrackUnmuted),t.on(e.TrackEvent.Ended,this.handleTrackEnded),t.on(e.TrackEvent.UpstreamPaused,this.onTrackUpstreamPaused),t.on(e.TrackEvent.UpstreamResumed,this.onTrackUpstreamResumed);const y=new Jn({cid:t.mediaStreamTrack.id,name:i.name,type:Cs.kindToProto(t.kind),muted:t.isMuted,source:Cs.sourceToProto(t.source),disableDtx:!(null===(s=i.dtx)||void 0===s||s),encryption:this.encryptionType,stereo:n,disableRed:this.isE2EEEnabled||!(null===(r=i.red)||void 0===r||r),stream:null==i?void 0:i.stream});let k;if(t.kind===Cs.Kind.Video){let e={width:0,height:0};try{e=yield t.waitForDimensions()}catch(i){const n=null!==(a=null===(o=this.roomOptions.videoCaptureDefaults)||void 0===o?void 0:o.resolution)&&void 0!==a?a:Nn.h720.resolution;e={width:n.width,height:n.height},this.log.error("could not determine track dimensions, using defaults",Object.assign(Object.assign(Object.assign({},this.logContext),_s(t)),{dims:e}))}y.width=e.width,y.height=e.height,t instanceof So&&(Fs(v)&&(t.source===Cs.Source.ScreenShare&&"vp9"===v&&(i.scalabilityMode="L1T3"),i.scalabilityMode=null!==(c=i.scalabilityMode)&&void 0!==c?c:"L3T3_KEY"),y.simulcastCodecs=[new Fn({codec:v,cid:t.mediaStreamTrack.id})],!0===i.backupCodec&&(i.backupCodec={codec:Kr}),i.backupCodec&&v!==i.backupCodec.codec&&y.encryption===$e.NONE&&(this.roomOptions.dynacast||(this.roomOptions.dynacast=!0),y.simulcastCodecs.push(new Fn({codec:i.backupCodec.codec,cid:""})))),k=vo(t.source===Cs.Source.ScreenShare,y.width,y.height,i),y.layers=wo(y.width,y.height,k,Fs(i.videoCodec))}else t.kind===Cs.Kind.Audio&&(k=[{maxBitrate:null!==(l=null===(d=i.audioPreset)||void 0===d?void 0:d.maxBitrate)&&void 0!==l?l:i.audioBitrate,priority:null!==(h=null===(u=i.audioPreset)||void 0===u?void 0:u.priority)&&void 0!==h?h:"high",networkPriority:null!==(m=null===(p=i.audioPreset)||void 0===p?void 0:p.priority)&&void 0!==m?m:"high"}]);if(!this.engine||this.engine.isClosed)throw new hn("cannot publish track when not connected");const b=yield this.engine.addTrack(y);let T;if(b.codecs.forEach((e=>{void 0===T&&(T=e.mimeType)})),T&&t.kind===Cs.Kind.Video){const e=xs(T);e!==v&&(this.log.debug("falling back to server selected codec",Object.assign(Object.assign(Object.assign({},this.logContext),_s(t)),{codec:e})),i.videoCodec=e,k=vo(t.source===Cs.Source.ScreenShare,y.width,y.height,i))}const S=new No(t.kind,b,t,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});if(S.options=i,t.sid=b.sid,!this.engine.pcManager)throw new hn("pcManager is not ready");if(this.log.debug("publishing ".concat(t.kind," with encodings"),Object.assign(Object.assign({},this.logContext),{encodings:k,trackInfo:b})),t.sender=yield this.engine.createSender(t,i,k),k)if(qs()&&t.kind===Cs.Kind.Audio){let e;for(const i of this.engine.pcManager.publisher.getTransceivers())if(i.sender===t.sender){e=i;break}e&&this.engine.pcManager.publisher.setTrackCodecBitrate({transceiver:e,codec:"opus",maxbr:(null===(g=k[0])||void 0===g?void 0:g.maxBitrate)?k[0].maxBitrate/1e3:0})}else t.codec&&Fs(t.codec)&&(null===(f=k[0])||void 0===f?void 0:f.maxBitrate)&&this.engine.pcManager.publisher.setTrackCodecBitrate({cid:y.cid,codec:t.codec,maxbr:k[0].maxBitrate/1e3});return yield this.engine.negotiate(),t instanceof So?t.startMonitor(this.engine.client):t instanceof ao&&t.startMonitor(),this.addTrackPublication(S),this.emit(e.ParticipantEvent.LocalTrackPublished,S),S}))}get isLocal(){return!0}publishAdditionalCodecForTrack(e,t,i){var n;return Ct(this,void 0,void 0,(function*(){if(this.encryptionType!==$e.NONE)return;let s;if(this.tracks.forEach((t=>{t.track&&t.track===e&&(s=t)})),!s)throw new ln("track is not published");if(!(e instanceof So))throw new ln("track is not a video track");const r=Object.assign(Object.assign({},null===(n=this.roomOptions)||void 0===n?void 0:n.publishDefaults),i),o=function(e,t,i){var n,s,r,o;if(!i.backupCodec||!0===i.backupCodec||i.backupCodec.codec===i.videoCodec)return;t!==i.backupCodec.codec&&c.warn("requested a different codec than specified as backup",{serverRequested:t,backup:i.backupCodec.codec}),i.videoCodec=t,i.videoEncoding=i.backupCodec.encoding;const a=e.mediaStreamTrack.getSettings(),d=null!==(n=a.width)&&void 0!==n?n:null===(s=e.dimensions)||void 0===s?void 0:s.width,l=null!==(r=a.height)&&void 0!==r?r:null===(o=e.dimensions)||void 0===o?void 0:o.height;return vo(e.source===Cs.Source.ScreenShare,d,l,i)}(e,t,r);if(!o)return void this.log.info("backup codec has been disabled, ignoring request to add additional codec for track",Object.assign(Object.assign({},this.logContext),_s(e)));const a=e.addSimulcastTrack(t,o),d=new Jn({cid:a.mediaStreamTrack.id,type:Cs.kindToProto(e.kind),muted:e.isMuted,source:Cs.sourceToProto(e.source),sid:e.sid,simulcastCodecs:[{codec:r.videoCodec,cid:a.mediaStreamTrack.id}]});if(d.layers=wo(d.width,d.height,o),!this.engine||this.engine.isClosed)throw new hn("cannot publish track when not connected");const l=yield this.engine.addTrack(d);yield this.engine.createSimulcastSender(e,a,r,o),yield this.engine.negotiate(),this.log.debug("published ".concat(t," for track ").concat(e.sid),Object.assign(Object.assign({},this.logContext),{encodings:o,trackInfo:l}))}))}unpublishTrack(t,i){var n,s;return Ct(this,void 0,void 0,(function*(){const r=this.getPublicationForTrack(t),o=r?_s(r):void 0;if(this.log.debug("unpublishing track",Object.assign(Object.assign({},this.logContext),o)),!r||!r.track)return void this.log.warn("track was not unpublished because no publication was found",Object.assign(Object.assign({},this.logContext),o));(t=r.track).off(e.TrackEvent.Muted,this.onTrackMuted),t.off(e.TrackEvent.Unmuted,this.onTrackUnmuted),t.off(e.TrackEvent.Ended,this.handleTrackEnded),t.off(e.TrackEvent.UpstreamPaused,this.onTrackUpstreamPaused),t.off(e.TrackEvent.UpstreamResumed,this.onTrackUpstreamResumed),void 0===i&&(i=null===(s=null===(n=this.roomOptions)||void 0===n?void 0:n.stopLocalTrackOnUnpublish)||void 0===s||s),i&&t.stop();let a=!1;const c=t.sender;if(t.sender=void 0,this.engine.pcManager&&this.engine.pcManager.currentState<Yr.FAILED&&c)try{for(const e of this.engine.pcManager.publisher.getTransceivers())e.sender===c&&(e.direction="inactive",a=!0);if(this.engine.removeTrack(c)&&(a=!0),t instanceof So){for(const[,e]of t.simulcastCodecs)e.sender&&(this.engine.removeTrack(e.sender)&&(a=!0),e.sender=void 0);t.simulcastCodecs.clear()}}catch(e){this.log.warn("failed to unpublish track",Object.assign(Object.assign(Object.assign({},this.logContext),o),{error:e}))}switch(this.tracks.delete(r.trackSid),r.kind){case Cs.Kind.Audio:this.audioTracks.delete(r.trackSid);break;case Cs.Kind.Video:this.videoTracks.delete(r.trackSid)}return this.emit(e.ParticipantEvent.LocalTrackUnpublished,r),r.setTrack(void 0),a&&(yield this.engine.negotiate()),r}))}unpublishTracks(e){return Ct(this,void 0,void 0,(function*(){return(yield Promise.all(e.map((e=>this.unpublishTrack(e))))).filter((e=>e instanceof No))}))}republishAllTracks(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Ct(this,void 0,void 0,(function*(){const i=[];this.tracks.forEach((t=>{t.track&&(e&&(t.options=Object.assign(Object.assign({},t.options),e)),i.push(t))})),yield Promise.all(i.map((e=>Ct(this,void 0,void 0,(function*(){const i=e.track;yield this.unpublishTrack(i,!1),t&&!i.isMuted&&i.source!==Cs.Source.ScreenShare&&i.source!==Cs.Source.ScreenShareAudio&&(i instanceof ao||i instanceof So)&&!i.isUserProvided&&(this.log.debug("restarting existing track",Object.assign(Object.assign({},this.logContext),{track:e.trackSid})),yield i.restartTrack()),yield this.publishTrack(i,e.options)})))))}))}publishData(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ct(this,void 0,void 0,(function*(){const n=Array.isArray(i)?i:null==i?void 0:i.destination,s=[],r=Array.isArray(i)?void 0:i.topic;void 0!==n&&n.forEach((e=>{e instanceof Lo?s.push(e.sid):s.push(e)}));const o=new at({kind:t,value:{case:"user",value:new lt({participantSid:this.sid,payload:e,destinationSids:s,topic:r})}});yield this.engine.sendDataPacket(o,t)}))}setTrackSubscriptionPermissions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.participantTrackPermissions=t,this.allParticipantsAllowedToSubscribe=e,this.engine.client.isDisconnected||this.updateTrackSubscriptionPermissions()}updateInfo(e){return e.sid===this.sid&&(!!super.updateInfo(e)&&(e.tracks.forEach((e=>{var t,i;const n=this.tracks.get(e.sid);if(n){const s=n.isMuted||null!==(i=null===(t=n.track)||void 0===t?void 0:t.isUpstreamPaused)&&void 0!==i&&i;s!==e.muted&&(this.log.debug("updating server mute state after reconcile",Object.assign(Object.assign(Object.assign({},this.logContext),_s(n)),{mutedOnServer:s})),this.engine.client.sendMuteTrack(e.sid,s))}})),!0))}getPublicationForTrack(e){let t;return this.tracks.forEach((i=>{const n=i.track;n&&(e instanceof MediaStreamTrack?(n instanceof ao||n instanceof So)&&n.mediaStreamTrack===e&&(t=i):e===n&&(t=i))})),t}}var Uo;e.ConnectionState=void 0,(Uo=e.ConnectionState||(e.ConnectionState={})).Disconnected="disconnected",Uo.Connecting="connecting",Uo.Connected="connected",Uo.Reconnecting="reconnecting";const jo=e.ConnectionState;class Bo extends Vt.EventEmitter{constructor(t){var i,n,s;super(),i=this,this.state=e.ConnectionState.Disconnected,this.activeSpeakers=[],this.isE2EEEnabled=!1,this.audioEnabled=!0,this.isVideoPlaybackBlocked=!1,this.log=c,this.bufferedEvents=[],this.connect=(t,i,n)=>Ct(this,void 0,void 0,(function*(){var s;const r=yield this.disconnectLock.lock();if(this.state===e.ConnectionState.Connected)return this.log.info("already connected to room ".concat(this.name),this.logContext),r(),Promise.resolve();if(this.connectFuture)return r(),this.connectFuture.promise;this.setAndEmitConnectionState(e.ConnectionState.Connecting),(null===(s=this.regionUrlProvider)||void 0===s?void 0:s.getServerUrl().toString())!==t&&(this.regionUrl=void 0,this.regionUrlProvider=void 0),Qs(new URL(t))&&(void 0===this.regionUrlProvider?this.regionUrlProvider=new so(t,i):this.regionUrlProvider.updateToken(i),this.regionUrlProvider.fetchRegionSettings().catch((e=>{this.log.warn("could not fetch region settings",Object.assign(Object.assign({},this.logContext),{error:e}))})));const o=(e,s,a)=>Ct(this,void 0,void 0,(function*(){var c;this.abortController&&this.abortController.abort();const d=new AbortController;this.abortController=d,null==r||r();try{yield this.attemptConnection(null!=a?a:t,i,n,d),this.abortController=void 0,e()}catch(t){if(this.regionUrlProvider&&t instanceof cn&&3!==t.reason&&0!==t.reason){let i=null;try{i=yield this.regionUrlProvider.getNextBestRegionUrl(null===(c=this.abortController)||void 0===c?void 0:c.signal)}catch(e){if(e instanceof cn&&(401===e.status||3===e.reason))return this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),void s(e)}i?(this.log.info("Initial connection failed with ConnectionError: ".concat(t.message,". Retrying with another region: ").concat(i),this.logContext),yield o(e,s,i)):(this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(t))}else this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),s(t)}})),a=this.regionUrl;return this.regionUrl=void 0,this.connectFuture=new lr(((e,t)=>{o(e,t,a)}),(()=>{this.clearConnectionFutures()})),this.connectFuture.promise})),this.connectSignal=(e,t,i,n,s,r)=>Ct(this,void 0,void 0,(function*(){var o,a,c;const d=yield i.join(e,t,{autoSubscribe:n.autoSubscribe,publishOnly:n.publishOnly,adaptiveStream:"object"==typeof s.adaptiveStream||s.adaptiveStream,maxRetries:n.maxRetries,e2eeEnabled:!!this.e2eeManager,websocketTimeout:n.websocketTimeout},r.signal);let l=d.serverInfo;if(l||(l={version:d.serverVersion,region:d.serverRegion}),this.log.debug("connected to Livekit Server ".concat(Object.entries(l).map((e=>{let[t,i]=e;return"".concat(t,": ").concat(i)})).join(", ")),{room:null===(o=d.room)||void 0===o?void 0:o.name,roomSid:null===(a=d.room)||void 0===a?void 0:a.sid,identity:null===(c=d.participant)||void 0===c?void 0:c.identity}),!d.serverVersion)throw new un("unknown server version");return"0.15.1"===d.serverVersion&&this.options.dynacast&&(this.log.debug("disabling dynacast due to server version",this.logContext),s.dynacast=!1),d})),this.applyJoinResponse=e=>{const t=e.participant;this.localParticipant.sid=t.sid,this.localParticipant.identity=t.identity,this.handleParticipantUpdates([t,...e.otherParticipants]),e.room&&this.handleRoomUpdate(e.room),this.options.e2ee&&this.e2eeManager&&this.e2eeManager.setSifTrailer(e.sifTrailer)},this.attemptConnection=(t,i,n,s)=>Ct(this,void 0,void 0,(function*(){var r,o;this.state===e.ConnectionState.Reconnecting?(this.log.info("Reconnection attempt replaced by new connection attempt",this.logContext),this.recreateEngine()):this.maybeCreateEngine(),(null===(r=this.regionUrlProvider)||void 0===r?void 0:r.isCloud())&&this.engine.setRegionUrlProvider(this.regionUrlProvider),this.acquireAudioContext(),this.connOptions=Object.assign(Object.assign({},Qr),n),this.connOptions.rtcConfig&&(this.engine.rtcConfig=this.connOptions.rtcConfig),this.connOptions.peerConnectionTimeout&&(this.engine.peerConnectionTimeout=this.connOptions.peerConnectionTimeout);try{const n=yield this.connectSignal(t,i,this.engine,this.connOptions,this.options,s);this.applyJoinResponse(n),this.setupLocalParticipantEvents(),this.emit(e.RoomEvent.SignalConnected)}catch(e){yield this.engine.close(),this.recreateEngine();const t=new cn("could not establish signal connection");throw e instanceof Error&&(t.message="".concat(t.message,": ").concat(e.message)),e instanceof cn&&(t.reason=e.reason,t.status=e.status),this.log.debug("error trying to establish signal connection",Object.assign(Object.assign({},this.logContext),{error:e})),t}if(s.signal.aborted)throw yield this.engine.close(),this.recreateEngine(),new cn("Connection attempt aborted");try{yield this.engine.waitForPCInitialConnection(this.connOptions.peerConnectionTimeout,s)}catch(e){throw yield this.engine.close(),this.recreateEngine(),e}Hs()&&this.options.disconnectOnPageLeave&&(window.addEventListener("pagehide",this.onPageLeave),window.addEventListener("beforeunload",this.onPageLeave)),Hs()&&(document.addEventListener("freeze",this.onPageLeave),null===(o=navigator.mediaDevices)||void 0===o||o.addEventListener("devicechange",this.handleDeviceChange)),this.setAndEmitConnectionState(e.ConnectionState.Connected),this.emit(e.RoomEvent.Connected),this.registerConnectionReconcile()})),this.disconnect=function(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return Ct(i,void 0,void 0,(function*(){var i,n,s,r;const o=yield this.disconnectLock.lock();try{if(this.state===e.ConnectionState.Disconnected)return void this.log.debug("already disconnected",this.logContext);this.log.info("disconnect from room",Object.assign({},this.logContext)),this.state!==e.ConnectionState.Connecting&&this.state!==e.ConnectionState.Reconnecting||(this.log.warn("abort connection attempt",this.logContext),null===(i=this.abortController)||void 0===i||i.abort(),null===(s=null===(n=this.connectFuture)||void 0===n?void 0:n.reject)||void 0===s||s.call(n,new cn("Client initiated disconnect")),this.connectFuture=void 0),(null===(r=this.engine)||void 0===r?void 0:r.client.isDisconnected)||(yield this.engine.client.sendLeave()),this.engine&&(yield this.engine.close()),this.handleDisconnect(t,e.DisconnectReason.CLIENT_INITIATED),this.engine=void 0}finally{o()}}))},this.onPageLeave=()=>Ct(this,void 0,void 0,(function*(){yield this.disconnect()})),this.startAudio=()=>Ct(this,void 0,void 0,(function*(){const t=[],i=Sn();if(i&&"iOS"===i.os){const i="livekit-dummy-audio-el";let n=document.getElementById(i);if(!n){n=document.createElement("audio"),n.id=i,n.autoplay=!0,n.hidden=!0;const t=dr();t.enabled=!0;const s=new MediaStream([t]);n.srcObject=s,document.addEventListener("visibilitychange",(()=>{n&&(n.srcObject=document.hidden?null:s,document.hidden||(this.log.debug("page visible again, triggering startAudio to resume playback and update playback status",this.logContext),this.startAudio()))})),document.body.append(n),this.once(e.RoomEvent.Disconnected,(()=>{null==n||n.remove(),n=null}))}t.push(n)}this.participants.forEach((e=>{e.audioTracks.forEach((e=>{e.track&&e.track.attachedElements.forEach((e=>{t.push(e)}))}))}));try{yield Promise.all([this.acquireAudioContext(),...t.map((e=>(e.muted=!1,e.play())))]),this.handleAudioPlaybackStarted()}catch(e){throw this.handleAudioPlaybackFailed(e),e}})),this.startVideo=()=>Ct(this,void 0,void 0,(function*(){const e=[];for(const t of this.participants.values())t.videoTracks.forEach((t=>{var i;null===(i=t.track)||void 0===i||i.attachedElements.forEach((t=>{e.includes(t)||e.push(t)}))}));yield Promise.all(e.map((e=>e.play()))).then((()=>{this.handleVideoPlaybackStarted()})).catch((e=>{"NotAllowedError"===e.name?this.handleVideoPlaybackFailed():this.log.warn("Resuming video playback failed, make sure you call `startVideo` directly in a user gesture handler",this.logContext)}))})),this.handleRestarting=()=>{this.clearConnectionReconcile();for(const e of this.participants.values())this.handleParticipantDisconnected(e.sid,e);this.setAndEmitConnectionState(e.ConnectionState.Reconnecting)&&this.emit(e.RoomEvent.Reconnecting)},this.handleSignalRestarted=t=>Ct(this,void 0,void 0,(function*(){this.log.debug("signal reconnected to server, region ".concat(t.serverRegion),Object.assign(Object.assign({},this.logContext),{region:t.serverRegion})),this.bufferedEvents=[],this.applyJoinResponse(t);try{yield this.localParticipant.republishAllTracks(void 0,!0)}catch(e){this.log.error("error trying to re-publish tracks after reconnection",Object.assign(Object.assign({},this.logContext),{error:e}))}try{yield this.engine.waitForRestarted(),this.log.debug("fully reconnected to server",Object.assign(Object.assign({},this.logContext),{region:t.serverRegion}))}catch(e){return}this.setAndEmitConnectionState(e.ConnectionState.Connected),this.emit(e.RoomEvent.Reconnected),this.registerConnectionReconcile(),this.emitBufferedEvents()})),this.handleParticipantUpdates=e=>{e.forEach((e=>{if(e.identity===this.localParticipant.identity)return void this.localParticipant.updateInfo(e);const t=this.identityToSid.get(e.identity);t&&t!==e.sid&&this.handleParticipantDisconnected(t,this.participants.get(t));let i=this.participants.get(e.sid);const n=!i;e.state===Ze.DISCONNECTED?this.handleParticipantDisconnected(e.sid,i):(i=this.getOrCreateParticipant(e.sid,e),n||i.updateInfo(e))}))},this.handleActiveSpeakersUpdate=t=>{const i=[],n={};t.forEach((e=>{if(n[e.sid]=!0,e.sid===this.localParticipant.sid)this.localParticipant.audioLevel=e.level,this.localParticipant.setIsSpeaking(!0),i.push(this.localParticipant);else{const t=this.participants.get(e.sid);t&&(t.audioLevel=e.level,t.setIsSpeaking(!0),i.push(t))}})),n[this.localParticipant.sid]||(this.localParticipant.audioLevel=0,this.localParticipant.setIsSpeaking(!1)),this.participants.forEach((e=>{n[e.sid]||(e.audioLevel=0,e.setIsSpeaking(!1))})),this.activeSpeakers=i,this.emitWhenConnected(e.RoomEvent.ActiveSpeakersChanged,i)},this.handleSpeakersChanged=t=>{const i=new Map;this.activeSpeakers.forEach((e=>{i.set(e.sid,e)})),t.forEach((e=>{let t=this.participants.get(e.sid);e.sid===this.localParticipant.sid&&(t=this.localParticipant),t&&(t.audioLevel=e.level,t.setIsSpeaking(e.active),e.active?i.set(e.sid,t):i.delete(e.sid))}));const n=Array.from(i.values());n.sort(((e,t)=>t.audioLevel-e.audioLevel)),this.activeSpeakers=n,this.emitWhenConnected(e.RoomEvent.ActiveSpeakersChanged,n)},this.handleStreamStateUpdate=t=>{t.streamStates.forEach((t=>{const i=this.participants.get(t.participantSid);if(!i)return;const n=i.getTrackPublication(t.trackSid);n&&n.track&&(n.track.streamState=Cs.streamStateFromProto(t.state),i.emit(e.ParticipantEvent.TrackStreamStateChanged,n,n.track.streamState),this.emitWhenConnected(e.RoomEvent.TrackStreamStateChanged,n,n.track.streamState,i))}))},this.handleSubscriptionPermissionUpdate=e=>{const t=this.participants.get(e.participantSid);if(!t)return;const i=t.getTrackPublication(e.trackSid);i&&i.setAllowed(e.allowed)},this.handleSubscriptionError=e=>{const t=Array.from(this.participants.values()).find((t=>t.tracks.has(e.trackSid)));if(!t)return;const i=t.getTrackPublication(e.trackSid);i&&i.setSubscriptionError(e.err)},this.handleDataPacket=(t,i)=>{const n=this.participants.get(t.participantSid);this.emit(e.RoomEvent.DataReceived,t.payload,n,i,t.topic),null==n||n.emit(e.ParticipantEvent.DataReceived,t.payload,i)},this.handleAudioPlaybackStarted=()=>{this.canPlaybackAudio||(this.audioEnabled=!0,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,!0))},this.handleAudioPlaybackFailed=t=>{this.log.warn("could not playback audio",Object.assign(Object.assign({},this.logContext),{error:t})),this.canPlaybackAudio&&(this.audioEnabled=!1,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,!1))},this.handleVideoPlaybackStarted=()=>{this.isVideoPlaybackBlocked&&(this.isVideoPlaybackBlocked=!1,this.emit(e.RoomEvent.VideoPlaybackStatusChanged,!0))},this.handleVideoPlaybackFailed=()=>{this.isVideoPlaybackBlocked||(this.isVideoPlaybackBlocked=!0,this.emit(e.RoomEvent.VideoPlaybackStatusChanged,!1))},this.handleDeviceChange=()=>Ct(this,void 0,void 0,(function*(){this.emit(e.RoomEvent.MediaDevicesChanged)})),this.handleRoomUpdate=t=>{const i=this.roomInfo;this.roomInfo=t,i&&i.metadata!==t.metadata&&this.emitWhenConnected(e.RoomEvent.RoomMetadataChanged,t.metadata),(null==i?void 0:i.activeRecording)!==t.activeRecording&&this.emitWhenConnected(e.RoomEvent.RecordingStatusChanged,t.activeRecording)},this.handleConnectionQualityUpdate=e=>{e.updates.forEach((e=>{if(e.participantSid===this.localParticipant.sid)return void this.localParticipant.setConnectionQuality(e.quality);const t=this.participants.get(e.participantSid);t&&t.setConnectionQuality(e.quality)}))},this.onLocalParticipantMetadataChanged=t=>{this.emit(e.RoomEvent.ParticipantMetadataChanged,t,this.localParticipant)},this.onLocalParticipantNameChanged=t=>{this.emit(e.RoomEvent.ParticipantNameChanged,t,this.localParticipant)},this.onLocalTrackMuted=t=>{this.emit(e.RoomEvent.TrackMuted,t,this.localParticipant)},this.onLocalTrackUnmuted=t=>{this.emit(e.RoomEvent.TrackUnmuted,t,this.localParticipant)},this.onLocalTrackPublished=t=>Ct(this,void 0,void 0,(function*(){var i;if(this.emit(e.RoomEvent.LocalTrackPublished,t,this.localParticipant),t.track instanceof ao){(yield t.track.checkForSilence())&&this.emit(e.RoomEvent.LocalAudioSilenceDetected,t)}const n=yield null===(i=t.track)||void 0===i?void 0:i.getDeviceId(),s=(r=t.source)===Cs.Source.Microphone?"audioinput":r===Cs.Source.Camera?"videoinput":void 0;var r;s&&n&&n!==this.localParticipant.activeDeviceMap.get(s)&&(this.localParticipant.activeDeviceMap.set(s,n),this.emit(e.RoomEvent.ActiveDeviceChanged,s,n))})),this.onLocalTrackUnpublished=t=>{this.emit(e.RoomEvent.LocalTrackUnpublished,t,this.localParticipant)},this.onLocalConnectionQualityChanged=t=>{this.emit(e.RoomEvent.ConnectionQualityChanged,t,this.localParticipant)},this.onMediaDevicesError=t=>{this.emit(e.RoomEvent.MediaDevicesError,t)},this.onLocalParticipantPermissionsChanged=t=>{this.emit(e.RoomEvent.ParticipantPermissionsChanged,t,this.localParticipant)},this.setMaxListeners(100),this.participants=new Map,this.identityToSid=new Map,this.options=Object.assign(Object.assign({},zr),t),this.log=d(null!==(n=this.options.loggerName)&&void 0!==n?n:o.Room),this.options.audioCaptureDefaults=Object.assign(Object.assign({},Gr),null==t?void 0:t.audioCaptureDefaults),this.options.videoCaptureDefaults=Object.assign(Object.assign({},Hr),null==t?void 0:t.videoCaptureDefaults),this.options.publishDefaults=Object.assign(Object.assign({},Wr),null==t?void 0:t.publishDefaults),this.maybeCreateEngine(),this.disconnectLock=new ur,this.localParticipant=new Ao("","",this.engine,this.options),this.options.videoCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("videoinput",hr(this.options.videoCaptureDefaults.deviceId)),this.options.audioCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("audioinput",hr(this.options.audioCaptureDefaults.deviceId)),(null===(s=this.options.audioOutput)||void 0===s?void 0:s.deviceId)&&this.switchActiveDevice("audiooutput",hr(this.options.audioOutput.deviceId)).catch((e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext))),this.options.e2ee&&this.setupE2EE()}setE2EEEnabled(e){return Ct(this,void 0,void 0,(function*(){if(!this.e2eeManager)throw Error("e2ee not configured, please set e2ee settings within the room options");yield Promise.all([this.localParticipant.setE2EEEnabled(e)]),""!==this.localParticipant.identity&&this.e2eeManager.setParticipantCryptorEnabled(e,this.localParticipant.identity)}))}setupE2EE(){var t;this.options.e2ee&&(this.e2eeManager=new vr(this.options.e2ee),this.e2eeManager.on(e.EncryptionEvent.ParticipantEncryptionStatusChanged,((t,i)=>{i instanceof Ao&&(this.isE2EEEnabled=t),this.emit(e.RoomEvent.ParticipantEncryptionStatusChanged,t,i)})),this.e2eeManager.on(e.EncryptionEvent.EncryptionError,(t=>this.emit(e.RoomEvent.EncryptionError,t))),null===(t=this.e2eeManager)||void 0===t||t.setup(this))}get logContext(){return{room:this.name,roomSid:this.sid,identity:this.localParticipant.identity}}get isRecording(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.activeRecording)&&void 0!==t&&t}get sid(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.sid)&&void 0!==t?t:""}get name(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.name)&&void 0!==t?t:""}get metadata(){var e;return null===(e=this.roomInfo)||void 0===e?void 0:e.metadata}get numParticipants(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numParticipants)&&void 0!==t?t:0}get numPublishers(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numPublishers)&&void 0!==t?t:0}maybeCreateEngine(){this.engine&&!this.engine.isClosed||(this.engine=new io(this.options),this.engine.on(e.EngineEvent.ParticipantUpdate,this.handleParticipantUpdates).on(e.EngineEvent.RoomUpdate,this.handleRoomUpdate).on(e.EngineEvent.SpeakersChanged,this.handleSpeakersChanged).on(e.EngineEvent.StreamStateChanged,this.handleStreamStateUpdate).on(e.EngineEvent.ConnectionQualityUpdate,this.handleConnectionQualityUpdate).on(e.EngineEvent.SubscriptionError,this.handleSubscriptionError).on(e.EngineEvent.SubscriptionPermissionUpdate,this.handleSubscriptionPermissionUpdate).on(e.EngineEvent.MediaTrackAdded,((e,t,i)=>{this.onTrackAdded(e,t,i)})).on(e.EngineEvent.Disconnected,(e=>{this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e)})).on(e.EngineEvent.ActiveSpeakersUpdate,this.handleActiveSpeakersUpdate).on(e.EngineEvent.DataPacketReceived,this.handleDataPacket).on(e.EngineEvent.Resuming,(()=>{this.clearConnectionReconcile(),this.setAndEmitConnectionState(e.ConnectionState.Reconnecting)&&this.emit(e.RoomEvent.Reconnecting)})).on(e.EngineEvent.Resumed,(()=>{this.setAndEmitConnectionState(e.ConnectionState.Connected),this.emit(e.RoomEvent.Reconnected),this.registerConnectionReconcile(),this.updateSubscriptions(),this.emitBufferedEvents()})).on(e.EngineEvent.SignalResumed,(()=>{this.bufferedEvents=[],this.state===e.ConnectionState.Reconnecting&&this.sendSyncState()})).on(e.EngineEvent.Restarting,this.handleRestarting).on(e.EngineEvent.SignalRestarted,this.handleSignalRestarted).on(e.EngineEvent.DCBufferStatusChanged,((t,i)=>{this.emit(e.RoomEvent.DCBufferStatusChanged,t,i)})),this.localParticipant&&this.localParticipant.setupEngine(this.engine),this.e2eeManager&&this.e2eeManager.setupEngine(this.engine))}static getLocalDevices(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return gr.getInstance().getDevices(e,t)}prepareConnection(t,i){return Ct(this,void 0,void 0,(function*(){if(this.state===e.ConnectionState.Disconnected){this.log.debug("prepareConnection to ".concat(t),this.logContext);try{if(Qs(new URL(t))&&i){this.regionUrlProvider=new so(t,i);const n=yield this.regionUrlProvider.getNextBestRegionUrl();n&&this.state===e.ConnectionState.Disconnected&&(this.regionUrl=n,yield fetch(pr(n),{method:"HEAD"}),this.log.debug("prepared connection to ".concat(n),this.logContext))}else yield fetch(pr(t),{method:"HEAD"})}catch(e){this.log.warn("could not prepare connection",Object.assign(Object.assign({},this.logContext),{error:e}))}}}))}getParticipantByIdentity(e){if(this.localParticipant.identity===e)return this.localParticipant;const t=this.identityToSid.get(e);return t?this.participants.get(t):void 0}clearConnectionFutures(){this.connectFuture=void 0}simulateScenario(t,i){return Ct(this,void 0,void 0,(function*(){let n,s=()=>{};switch(t){case"signal-reconnect":yield this.engine.client.handleOnClose("simulate disconnect");break;case"speaker":n=new fs({scenario:{case:"speakerUpdate",value:3}});break;case"node-failure":n=new fs({scenario:{case:"nodeFailure",value:!0}});break;case"server-leave":n=new fs({scenario:{case:"serverLeave",value:!0}});break;case"migration":n=new fs({scenario:{case:"migration",value:!0}});break;case"resume-reconnect":this.engine.failNext(),yield this.engine.client.handleOnClose("simulate resume-disconnect");break;case"disconnect-signal-on-resume":s=()=>Ct(this,void 0,void 0,(function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")})),n=new fs({scenario:{case:"disconnectSignalOnResume",value:!0}});break;case"disconnect-signal-on-resume-no-messages":s=()=>Ct(this,void 0,void 0,(function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")})),n=new fs({scenario:{case:"disconnectSignalOnResumeNoMessages",value:!0}});break;case"full-reconnect":this.engine.fullReconnectOnNext=!0,yield this.engine.client.handleOnClose("simulate full-reconnect");break;case"force-tcp":case"force-tls":n=new fs({scenario:{case:"switchCandidateProtocol",value:"force-tls"===t?2:1}}),s=()=>Ct(this,void 0,void 0,(function*(){const t=this.engine.client.onLeave;t&&t(new Zn({reason:e.DisconnectReason.CLIENT_INITIATED,canReconnect:!0}))}));break;case"subscriber-bandwidth":if(void 0===i||"number"!=typeof i)throw new Error("subscriber-bandwidth requires a number as argument");n=new fs({scenario:{case:"subscriberBandwidth",value:BigInt(i)}})}n&&(yield this.engine.client.sendSimulateScenario(n),yield s())}))}get canPlaybackAudio(){return this.audioEnabled}get canPlaybackVideo(){return!this.isVideoPlaybackBlocked}getActiveAudioOutputDevice(){var e,t;return null!==(t=null===(e=this.options.audioOutput)||void 0===e?void 0:e.deviceId)&&void 0!==t?t:""}getActiveDevice(e){return this.localParticipant.activeDeviceMap.get(e)}switchActiveDevice(t,i){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var s,r,o;return Ct(this,void 0,void 0,(function*(){let a=!1,c=!0;const d=n?{exact:i}:i;if("audioinput"===t){const e=this.options.audioCaptureDefaults.deviceId;this.options.audioCaptureDefaults.deviceId=d,a=e!==d;const t=Array.from(this.localParticipant.audioTracks.values()).filter((e=>e.source===Cs.Source.Microphone));try{c=(yield Promise.all(t.map((e=>{var t;return null===(t=e.audioTrack)||void 0===t?void 0:t.setDeviceId(d)})))).every((e=>!0===e))}catch(t){throw this.options.audioCaptureDefaults.deviceId=e,t}}else if("videoinput"===t){const e=this.options.videoCaptureDefaults.deviceId;this.options.videoCaptureDefaults.deviceId=d,a=e!==d;const t=Array.from(this.localParticipant.videoTracks.values()).filter((e=>e.source===Cs.Source.Camera));try{c=(yield Promise.all(t.map((e=>{var t;return null===(t=e.videoTrack)||void 0===t?void 0:t.setDeviceId(d)})))).every((e=>!0===e))}catch(t){throw this.options.videoCaptureDefaults.deviceId=e,t}}else if("audiooutput"===t){if(!Js()&&!this.options.expWebAudioMix||this.options.expWebAudioMix&&this.audioContext&&!("setSinkId"in this.audioContext))throw new Error("cannot switch audio output, setSinkId not supported");null!==(s=(o=this.options).audioOutput)&&void 0!==s||(o.audioOutput={});const e=this.options.audioOutput.deviceId;this.options.audioOutput.deviceId=i,a=e!==d;try{this.options.expWebAudioMix?null===(r=this.audioContext)||void 0===r||r.setSinkId(i):yield Promise.all(Array.from(this.participants.values()).map((e=>e.setAudioOutput({deviceId:i}))))}catch(t){throw this.options.audioOutput.deviceId=e,t}}return a&&c&&(this.localParticipant.activeDeviceMap.set(t,i),this.emit(e.RoomEvent.ActiveDeviceChanged,t,i)),c}))}setupLocalParticipantEvents(){this.localParticipant.on(e.ParticipantEvent.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).on(e.ParticipantEvent.ParticipantNameChanged,this.onLocalParticipantNameChanged).on(e.ParticipantEvent.TrackMuted,this.onLocalTrackMuted).on(e.ParticipantEvent.TrackUnmuted,this.onLocalTrackUnmuted).on(e.ParticipantEvent.LocalTrackPublished,this.onLocalTrackPublished).on(e.ParticipantEvent.LocalTrackUnpublished,this.onLocalTrackUnpublished).on(e.ParticipantEvent.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).on(e.ParticipantEvent.MediaDevicesError,this.onMediaDevicesError).on(e.ParticipantEvent.AudioStreamAcquired,this.startAudio).on(e.ParticipantEvent.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged)}recreateEngine(){var e;null===(e=this.engine)||void 0===e||e.close(),this.engine=void 0,this.participants.clear(),this.bufferedEvents=[],this.maybeCreateEngine()}onTrackAdded(t,i,n){if(this.state===e.ConnectionState.Connecting||this.state===e.ConnectionState.Reconnecting){const s=()=>{this.onTrackAdded(t,i,n),r()},r=()=>{this.off(e.RoomEvent.Reconnected,s),this.off(e.RoomEvent.Connected,s),this.off(e.RoomEvent.Disconnected,r)};return this.once(e.RoomEvent.Reconnected,s),this.once(e.RoomEvent.Connected,s),void this.once(e.RoomEvent.Disconnected,r)}if(this.state===e.ConnectionState.Disconnected)return void this.log.warn("skipping incoming track after Room disconnected",this.logContext);const s=function(e){const t=e.split("|");return t.length>1?[t[0],e.substr(t[0].length+1)]:[e,""]}(i.id),r=s[0];let o=s[1],a=t.id;if(o&&o.startsWith("TR")&&(a=o),r===this.localParticipant.sid)return void this.log.warn("tried to create RemoteParticipant for local participant",this.logContext);const c=this.participants.get(r);if(!c)return void this.log.error("Tried to add a track for a participant, that's not present. Sid: ".concat(r),this.logContext);let d;this.options.adaptiveStream&&(d="object"==typeof this.options.adaptiveStream?this.options.adaptiveStream:{}),c.addSubscribedMediaTrack(t,a,i,n,d)}handleDisconnect(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1?arguments[1]:void 0;var n;if(this.clearConnectionReconcile(),this.bufferedEvents=[],this.state!==e.ConnectionState.Disconnected){this.regionUrl=void 0;try{this.participants.forEach((e=>{e.tracks.forEach((t=>{e.unpublishTrack(t.trackSid)}))})),this.localParticipant.tracks.forEach((e=>{var i,n;e.track&&this.localParticipant.unpublishTrack(e.track,t),t&&(null===(i=e.track)||void 0===i||i.detach(),null===(n=e.track)||void 0===n||n.stop())})),this.localParticipant.off(e.ParticipantEvent.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).off(e.ParticipantEvent.ParticipantNameChanged,this.onLocalParticipantNameChanged).off(e.ParticipantEvent.TrackMuted,this.onLocalTrackMuted).off(e.ParticipantEvent.TrackUnmuted,this.onLocalTrackUnmuted).off(e.ParticipantEvent.LocalTrackPublished,this.onLocalTrackPublished).off(e.ParticipantEvent.LocalTrackUnpublished,this.onLocalTrackUnpublished).off(e.ParticipantEvent.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).off(e.ParticipantEvent.MediaDevicesError,this.onMediaDevicesError).off(e.ParticipantEvent.AudioStreamAcquired,this.startAudio).off(e.ParticipantEvent.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged),this.localParticipant.tracks.clear(),this.localParticipant.videoTracks.clear(),this.localParticipant.audioTracks.clear(),this.participants.clear(),this.activeSpeakers=[],this.audioContext&&"boolean"==typeof this.options.expWebAudioMix&&(this.audioContext.close(),this.audioContext=void 0),Hs()&&(window.removeEventListener("beforeunload",this.onPageLeave),window.removeEventListener("pagehide",this.onPageLeave),window.removeEventListener("freeze",this.onPageLeave),null===(n=navigator.mediaDevices)||void 0===n||n.removeEventListener("devicechange",this.handleDeviceChange))}finally{this.setAndEmitConnectionState(e.ConnectionState.Disconnected),this.emit(e.RoomEvent.Disconnected,i)}}}handleParticipantDisconnected(t,i){this.participants.delete(t),i&&(this.identityToSid.delete(i.identity),i.tracks.forEach((e=>{i.unpublishTrack(e.trackSid,!0)})),this.emit(e.RoomEvent.ParticipantDisconnected,i))}acquireAudioContext(){var t,i;return Ct(this,void 0,void 0,(function*(){if("boolean"!=typeof this.options.expWebAudioMix&&this.options.expWebAudioMix.audioContext?this.audioContext=this.options.expWebAudioMix.audioContext:this.audioContext&&"closed"!==this.audioContext.state||(this.audioContext=null!==(t=Os())&&void 0!==t?t:void 0),this.audioContext&&"suspended"===this.audioContext.state)try{yield this.audioContext.resume()}catch(e){this.log.warn("Could not resume audio context",Object.assign(Object.assign({},this.logContext),{error:e}))}this.options.expWebAudioMix&&this.participants.forEach((e=>e.setAudioContext(this.audioContext))),this.localParticipant.setAudioContext(this.audioContext);const n="running"===(null===(i=this.audioContext)||void 0===i?void 0:i.state);n!==this.canPlaybackAudio&&(this.audioEnabled=n,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,n))}))}createParticipant(e,t){var i;let n;return n=t?Lo.fromParticipantInfo(this.engine.client,t):new Lo(this.engine.client,e,"",void 0,void 0,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}),this.options.expWebAudioMix&&n.setAudioContext(this.audioContext),(null===(i=this.options.audioOutput)||void 0===i?void 0:i.deviceId)&&n.setAudioOutput(this.options.audioOutput).catch((e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext))),n}getOrCreateParticipant(t,i){if(this.participants.has(t))return this.participants.get(t);const n=this.createParticipant(t,i);return this.participants.set(t,n),this.identityToSid.set(i.identity,i.sid),this.emitWhenConnected(e.RoomEvent.ParticipantConnected,n),n.on(e.ParticipantEvent.TrackPublished,(t=>{this.emitWhenConnected(e.RoomEvent.TrackPublished,t,n)})).on(e.ParticipantEvent.TrackSubscribed,((t,i)=>{t.kind===Cs.Kind.Audio?(t.on(e.TrackEvent.AudioPlaybackStarted,this.handleAudioPlaybackStarted),t.on(e.TrackEvent.AudioPlaybackFailed,this.handleAudioPlaybackFailed)):t.kind===Cs.Kind.Video&&(t.on(e.TrackEvent.VideoPlaybackFailed,this.handleVideoPlaybackFailed),t.on(e.TrackEvent.VideoPlaybackStarted,this.handleVideoPlaybackStarted)),this.emit(e.RoomEvent.TrackSubscribed,t,i,n)})).on(e.ParticipantEvent.TrackUnpublished,(t=>{this.emit(e.RoomEvent.TrackUnpublished,t,n)})).on(e.ParticipantEvent.TrackUnsubscribed,((t,i)=>{this.emit(e.RoomEvent.TrackUnsubscribed,t,i,n)})).on(e.ParticipantEvent.TrackSubscriptionFailed,(t=>{this.emit(e.RoomEvent.TrackSubscriptionFailed,t,n)})).on(e.ParticipantEvent.TrackMuted,(t=>{this.emitWhenConnected(e.RoomEvent.TrackMuted,t,n)})).on(e.ParticipantEvent.TrackUnmuted,(t=>{this.emitWhenConnected(e.RoomEvent.TrackUnmuted,t,n)})).on(e.ParticipantEvent.ParticipantMetadataChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantMetadataChanged,t,n)})).on(e.ParticipantEvent.ParticipantNameChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantNameChanged,t,n)})).on(e.ParticipantEvent.ConnectionQualityChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ConnectionQualityChanged,t,n)})).on(e.ParticipantEvent.ParticipantPermissionsChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantPermissionsChanged,t,n)})).on(e.ParticipantEvent.TrackSubscriptionStatusChanged,((t,i)=>{this.emitWhenConnected(e.RoomEvent.TrackSubscriptionStatusChanged,t,i,n)})).on(e.ParticipantEvent.TrackSubscriptionFailed,((t,i)=>{this.emit(e.RoomEvent.TrackSubscriptionFailed,t,n,i)})).on(e.ParticipantEvent.TrackSubscriptionPermissionChanged,((t,i)=>{this.emitWhenConnected(e.RoomEvent.TrackSubscriptionPermissionChanged,t,i,n)})),i&&n.updateInfo(i),n}sendSyncState(){const e=Array.from(this.participants.values()).reduce(((e,t)=>(e.push(...t.getTracks()),e)),[]),t=this.localParticipant.getTracks();this.engine.sendSyncState(e,t)}updateSubscriptions(){for(const e of this.participants.values())for(const t of e.videoTracks.values())t.isSubscribed&&t instanceof Mo&&t.emitTrackUpdate()}registerConnectionReconcile(){this.clearConnectionReconcile();let t=0;this.connectionReconcileInterval=Pn.setInterval((()=>{this.engine&&!this.engine.isClosed&&this.engine.verifyTransport()?t=0:(t++,this.log.warn("detected connection state mismatch",Object.assign(Object.assign({},this.logContext),{numFailures:t,engine:{closed:this.engine.isClosed,transportsConnected:this.engine.verifyTransport()}})),t>=3&&(this.recreateEngine(),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e.DisconnectReason.STATE_MISMATCH)))}),2e3)}clearConnectionReconcile(){this.connectionReconcileInterval&&Pn.clearInterval(this.connectionReconcileInterval)}setAndEmitConnectionState(t){return t!==this.state&&(this.state=t,this.emit(e.RoomEvent.ConnectionStateChanged,this.state),!0)}emitBufferedEvents(){this.bufferedEvents.forEach((e=>{let[t,i]=e;this.emit(t,...i)})),this.bufferedEvents=[]}emitWhenConnected(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return this.state===e.ConnectionState.Connected?this.emit(t,...n):(this.state===e.ConnectionState.Reconnecting&&this.bufferedEvents.push([t,n]),!1)}simulateParticipants(t){var i,n;return Ct(this,void 0,void 0,(function*(){const s=Object.assign({audio:!0,video:!0,useRealTracks:!1},t.publish),r=Object.assign({count:9,audio:!1,video:!0,aspectRatios:[1.66,1.7,1.3]},t.participants);if(this.handleDisconnect(),this.roomInfo=new He({sid:"RM_SIMULATED",name:"simulated-room",emptyTimeout:0,maxParticipants:0,creationTime:j.parse((new Date).getTime()),metadata:"",numParticipants:1,numPublishers:1,turnPassword:"",enabledCodecs:[],activeRecording:!1}),this.localParticipant.updateInfo(new Xe({identity:"simulated-local",name:"local-name"})),this.setupLocalParticipantEvents(),this.emit(e.RoomEvent.SignalConnected),this.emit(e.RoomEvent.Connected),this.setAndEmitConnectionState(e.ConnectionState.Connected),s.video){const t=new No(Cs.Kind.Video,new rt({source:je.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:Ue.AUDIO,name:"video-dummy"}),new So(s.useRealTracks?(yield window.navigator.mediaDevices.getUserMedia({video:!0})).getVideoTracks()[0]:cr(160*(null!==(i=r.aspectRatios[0])&&void 0!==i?i:1),160,!0,!0),void 0,!1,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(t),this.localParticipant.emit(e.ParticipantEvent.LocalTrackPublished,t)}if(s.audio){const t=new No(Cs.Kind.Audio,new rt({source:je.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:Ue.AUDIO}),new ao(s.useRealTracks?(yield navigator.mediaDevices.getUserMedia({audio:!0})).getAudioTracks()[0]:dr(),void 0,!1,this.audioContext,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(t),this.localParticipant.emit(e.ParticipantEvent.LocalTrackPublished,t)}for(let e=0;e<r.count-1;e+=1){let t=new Xe({sid:Math.floor(1e4*Math.random()).toString(),identity:"simulated-".concat(e),state:Ze.ACTIVE,tracks:[],joinedAt:j.parse(Date.now())});const i=this.getOrCreateParticipant(t.identity,t);if(r.video){const s=cr(160*(null!==(n=r.aspectRatios[e%r.aspectRatios.length])&&void 0!==n?n:1),160,!1,!0),o=new rt({source:je.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:Ue.AUDIO});i.addSubscribedMediaTrack(s,o.sid,new MediaStream([s])),t.tracks=[...t.tracks,o]}if(r.audio){const e=dr(),n=new rt({source:je.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:Ue.AUDIO});i.addSubscribedMediaTrack(e,n.sid,new MediaStream([e])),t.tracks=[...t.tracks,n]}i.updateInfo(t)}}))}emit(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];if(t!==e.RoomEvent.ActiveSpeakersChanged){const e=Fo(n).filter((e=>void 0!==e));this.log.debug("room event ".concat(t),Object.assign(Object.assign({},this.logContext),{event:t,args:e}))}return super.emit(t,...n)}}function Fo(e){return e.map((e=>{if(e)return Array.isArray(e)?Fo(e):"object"==typeof e?"logContext"in e&&e.logContext:e}))}var Jo;!function(e){e[e.IDLE=0]="IDLE",e[e.RUNNING=1]="RUNNING",e[e.SKIPPED=2]="SKIPPED",e[e.SUCCESS=3]="SUCCESS",e[e.FAILED=4]="FAILED"}(Jo||(Jo={}));class Vo extends Vt.EventEmitter{constructor(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super(),this.status=Jo.IDLE,this.logs=[],this.errorsAsWarnings=!1,this.url=e,this.token=t,this.name=this.constructor.name,this.room=new Bo(i.roomOptions),this.connectOptions=i.connectOptions,i.errorsAsWarnings&&(this.errorsAsWarnings=i.errorsAsWarnings)}run(e){return Ct(this,void 0,void 0,(function*(){if(this.status!==Jo.IDLE)throw Error("check is running already");this.setStatus(Jo.RUNNING);try{yield this.perform()}catch(e){e instanceof Error&&(this.errorsAsWarnings?this.appendWarning(e.message):this.appendError(e.message))}return yield this.disconnect(),yield new Promise((e=>setTimeout(e,500))),this.status!==Jo.SKIPPED&&this.setStatus(this.isSuccess()?Jo.SUCCESS:Jo.FAILED),e&&e(),this.getInfo()}))}isSuccess(){return!this.logs.some((e=>"error"===e.level))}connect(){return Ct(this,void 0,void 0,(function*(){return this.room.state===e.ConnectionState.Connected||(yield this.room.connect(this.url,this.token)),this.room}))}disconnect(){return Ct(this,void 0,void 0,(function*(){this.room&&this.room.state!==e.ConnectionState.Disconnected&&(yield this.room.disconnect(),yield new Promise((e=>setTimeout(e,500))))}))}skip(){this.setStatus(Jo.SKIPPED)}appendMessage(e){this.logs.push({level:"info",message:e}),this.emit("update",this.getInfo())}appendWarning(e){this.logs.push({level:"warning",message:e}),this.emit("update",this.getInfo())}appendError(e){this.logs.push({level:"error",message:e}),this.emit("update",this.getInfo())}setStatus(e){this.status=e,this.emit("update",this.getInfo())}get engine(){var e;return null===(e=this.room)||void 0===e?void 0:e.engine}getInfo(){return{logs:this.logs,name:this.name,status:this.status,description:this.description}}}function qo(e){var t,i;return Ct(this,void 0,void 0,(function*(){null!=e||(e={}),null!==(t=e.audio)&&void 0!==t||(e.audio=!0),null!==(i=e.video)&&void 0!==i||(e.video=!0);const n=Is(Ps(e,Gr,Hr)),s=navigator.mediaDevices.getUserMedia(n);e.audio&&(gr.userMediaPromiseMap.set("audioinput",s),s.catch((()=>gr.userMediaPromiseMap.delete("audioinput")))),e.video&&(gr.userMediaPromiseMap.set("videoinput",s),s.catch((()=>gr.userMediaPromiseMap.delete("videoinput"))));const r=yield s;return r.getTracks().map((t=>{const i="audio"===t.kind;let s;i?e.audio:e.video;const o=i?n.audio:n.video;"boolean"!=typeof o&&(s=o),s?s.deviceId=t.getSettings().deviceId:s={deviceId:t.getSettings().deviceId};const a=co(t,s);return a.kind===Cs.Kind.Video?a.source=Cs.Source.Camera:a.kind===Cs.Kind.Audio&&(a.source=Cs.Source.Microphone),a.mediaStream=r,a}))}))}function Ko(e){return Ct(this,void 0,void 0,(function*(){return(yield qo({audio:!1,video:e}))[0]}))}function Wo(e){return Ct(this,void 0,void 0,(function*(){return(yield qo({audio:e,video:!1}))[0]}))}class Go extends Vo{get description(){return"Can publish audio"}perform(){var e;return Ct(this,void 0,void 0,(function*(){const t=yield this.connect(),i=yield Wo();t.localParticipant.publishTrack(i),yield new Promise((e=>setTimeout(e,3e3)));const n=yield null===(e=i.sender)||void 0===e?void 0:e.getStats();if(!n)throw new Error("Could not get RTCStats");let s=0;if(n.forEach((e=>{"outbound-rtp"===e.type&&"audio"===e.mediaType&&(s=e.packetsSent)})),0===s)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(s," audio packets"))}))}}class Ho extends Vo{get description(){return"Can publish video"}perform(){var e;return Ct(this,void 0,void 0,(function*(){const t=yield this.connect(),i=yield Ko();t.localParticipant.publishTrack(i),yield new Promise((e=>setTimeout(e,3e3)));const n=yield null===(e=i.sender)||void 0===e?void 0:e.getStats();if(!n)throw new Error("Could not get RTCStats");let s=0;if(n.forEach((e=>{"outbound-rtp"===e.type&&"video"===e.mediaType&&(s=e.packetsSent)})),0===s)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(s," video packets"))}))}}class zo extends Vo{get description(){return"Resuming connection after interruption"}perform(){var t;return Ct(this,void 0,void 0,(function*(){const i=yield this.connect();let n,s=!1,r=!1;const o=new Promise((e=>{setTimeout(e,5e3),n=e}));i.on(e.RoomEvent.Reconnecting,(()=>{s=!0})).on(e.RoomEvent.Reconnected,(()=>{r=!0,n(!0)})),null===(t=i.engine.client.ws)||void 0===t||t.close();const a=i.engine.client.onClose;if(a&&a(""),yield o,!s)throw new Error("Did not attempt to reconnect");if(!r||i.state!==e.ConnectionState.Connected)throw this.appendWarning("reconnection is only possible in Redis-based configurations"),new Error("Not able to reconnect")}))}}class Qo extends Vo{get description(){return"Can connect via TURN"}perform(){var e,t;return Ct(this,void 0,void 0,(function*(){const i=new Sr,n=yield i.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});let s=!1,r=!1,o=!1;for(let e of n.iceServers)for(let t of e.urls)t.startsWith("turn:")?(r=!0,o=!0):t.startsWith("turns:")&&(r=!0,o=!0,s=!0),t.startsWith("stun:")&&(o=!0);o?r&&!s&&this.appendWarning("TURN is configured server side, but TURN/TLS is unavailable."):this.appendWarning("No STUN servers configured on server side."),yield i.close(),(null===(t=null===(e=this.connectOptions)||void 0===e?void 0:e.rtcConfig)||void 0===t?void 0:t.iceServers)||r?yield this.room.connect(this.url,this.token,{rtcConfig:{iceTransportPolicy:"relay"}}):(this.appendWarning("No TURN servers configured."),this.skip(),yield new Promise((e=>setTimeout(e,0))))}))}}class Yo extends Vo{get description(){return"Establishing WebRTC connection"}perform(){return Ct(this,void 0,void 0,(function*(){let t=!1,i=!1;this.room.on(e.RoomEvent.SignalConnected,(()=>{const e=this.room.engine.client.onTrickle;this.room.engine.client.onTrickle=(n,s)=>{if(n.candidate){const e=new RTCIceCandidate(n);let s="".concat(e.protocol," ").concat(e.address,":").concat(e.port," ").concat(e.type);e.address&&(!function(e){const t=e.split(".");if(4===t.length){if("10"===t[0])return!0;if("192"===t[0]&&"168"===t[1])return!0;if("172"===t[0]){const e=parseInt(t[1],10);if(e>=16&&e<=31)return!0}}return!1}(e.address)?"tcp"===e.protocol&&"passive"===e.tcpType?(t=!0,s+=" (passive)"):"udp"===e.protocol&&(i=!0):s+=" (private)"),this.appendMessage(s)}e&&e(n,s)},this.room.engine.pcManager&&(this.room.engine.pcManager.subscriber.onIceCandidateError=e=>{e instanceof RTCPeerConnectionIceErrorEvent&&this.appendWarning("error with ICE candidate: ".concat(e.errorCode," ").concat(e.errorText," ").concat(e.url))})}));try{yield this.connect(),c.info("now the room is connected")}catch(e){throw this.appendWarning("ports need to be open on firewall in order to connect."),e}t||this.appendWarning("Server is not configured for ICE/TCP"),i||this.appendWarning("No public IPv4 UDP candidates were found. Your server is likely not configured correctly")}))}}class Xo extends Vo{get description(){return"Connecting to signal connection via WebSocket"}perform(){var e,t,i;return Ct(this,void 0,void 0,(function*(){(this.url.startsWith("ws:")||this.url.startsWith("http:"))&&this.appendWarning("Server is insecure, clients may block connections to it");let n=new Sr;const s=yield n.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});this.appendMessage("Connected to server, version ".concat(s.serverVersion,".")),(null===(e=s.serverInfo)||void 0===e?void 0:e.edition)===tt.Cloud&&(null===(t=s.serverInfo)||void 0===t?void 0:t.region)&&this.appendMessage("LiveKit Cloud: ".concat(null===(i=s.serverInfo)||void 0===i?void 0:i.region)),yield n.close()}))}}class Zo extends Vt.EventEmitter{constructor(e,t){super(),this.checkResults=new Map,this.url=e,this.token=t}getNextCheckId(){const e=this.checkResults.size;return this.checkResults.set(e,{logs:[],status:Jo.IDLE,name:"",description:""}),e}updateCheck(e,t){this.checkResults.set(e,t),this.emit("checkUpdate",e,t)}isSuccess(){return Array.from(this.checkResults.values()).every((e=>e.status!==Jo.FAILED))}getResults(){return Array.from(this.checkResults.values())}createAndRunCheck(e){return Ct(this,void 0,void 0,(function*(){const t=this.getNextCheckId(),i=new e(this.url,this.token),n=e=>{this.updateCheck(t,e)};i.on("update",n);const s=yield i.run();return i.off("update",n),s}))}checkWebsocket(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(Xo)}))}checkWebRTC(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(Yo)}))}checkTURN(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(Qo)}))}checkReconnect(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(zo)}))}checkPublishAudio(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(Go)}))}checkPublishVideo(){return Ct(this,void 0,void 0,(function*(){return this.createAndRunCheck(Ho)}))}}const $o=new Map([["obs virtual camera",{facingMode:"environment",confidence:"medium"}]]),ea=new Map([["iphone",{facingMode:"environment",confidence:"medium"}],["ipad",{facingMode:"environment",confidence:"medium"}]]);function ta(e){var t;const i=e.trim().toLowerCase();if(""!==i)return $o.has(i)?$o.get(i):null===(t=Array.from(ea.entries()).find((e=>{let[t]=e;return i.includes(t)})))||void 0===t?void 0:t[1]}e.BaseKeyProvider=on,e.ConnectionCheck=Zo,e.ConnectionError=cn,e.CriticalTimers=Pn,e.DefaultReconnectPolicy=St,e.DeviceUnsupportedError=dn,e.ExternalE2EEKeyProvider=class extends on{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(Object.assign(Object.assign({},e),{sharedKey:!0,ratchetWindowSize:0,failureTolerance:-1}))}setKey(e){return Ct(this,void 0,void 0,(function*(){const t="string"==typeof e?yield nn(e):yield sn(e);this.onSetEncryptionKey(t)}))}},e.LivekitError=an,e.LocalAudioTrack=ao,e.LocalParticipant=Ao,e.LocalTrack=fr,e.LocalTrackPublication=No,e.LocalVideoTrack=So,e.NegotiationError=pn,e.Participant=_o,e.PublishDataError=class extends an{constructor(e){super(13,null!=e?e:"unable to publish data")}},e.RemoteAudioTrack=Ro,e.RemoteParticipant=Lo,e.RemoteTrack=Po,e.RemoteTrackPublication=Mo,e.RemoteVideoTrack=Io,e.Room=Bo,e.RoomState=jo,e.ScreenSharePresets=Mn,e.Track=Cs,e.TrackInvalidError=ln,e.TrackPublication=xo,e.UnexpectedConnectionState=hn,e.UnsupportedServer=un,e.VideoPreset=Rn,e.VideoPresets=Nn,e.VideoPresets43=_n,e.attachToElement=Es,e.createAudioAnalyser=function(e,t){const i=Object.assign({cloneTrack:!1,fftSize:2048,smoothingTimeConstant:.8,minDecibels:-100,maxDecibels:-80},t),n=Os();if(!n)throw new Error("Audio Context not supported on this browser");const s=i.cloneTrack?e.mediaStreamTrack.clone():e.mediaStreamTrack,r=n.createMediaStreamSource(new MediaStream([s])),o=n.createAnalyser();o.minDecibels=i.minDecibels,o.maxDecibels=i.maxDecibels,o.fftSize=i.fftSize,o.smoothingTimeConstant=i.smoothingTimeConstant,r.connect(o);const a=new Uint8Array(o.frequencyBinCount);return{calculateVolume:()=>{o.getByteFrequencyData(a);let e=0;for(const t of a)e+=Math.pow(t/255,2);return Math.sqrt(e/a.length)},analyser:o,cleanup:()=>Ct(this,void 0,void 0,(function*(){yield n.close(),i.cloneTrack&&s.stop()}))}},e.createE2EEKey=function(){return window.crypto.getRandomValues(new Uint8Array(32))},e.createKeyMaterialFromBuffer=sn,e.createKeyMaterialFromString=nn,e.createLocalAudioTrack=Wo,e.createLocalScreenTracks=function(e){return Ct(this,void 0,void 0,(function*(){if(void 0===e&&(e={}),void 0!==e.resolution||Ws()||(e.resolution=Mn.h1080fps30.resolution),void 0===navigator.mediaDevices.getDisplayMedia)throw new dn("getDisplayMedia not supported");const t=Ds(e),i=yield navigator.mediaDevices.getDisplayMedia(t),n=i.getVideoTracks();if(0===n.length)throw new ln("no video track found");const s=new So(n[0],void 0,!1);s.source=Cs.Source.ScreenShare;const r=[s];if(i.getAudioTracks().length>0){const e=new ao(i.getAudioTracks()[0],void 0,!1);e.source=Cs.Source.ScreenShareAudio,r.push(e)}return r}))},e.createLocalTracks=qo,e.createLocalVideoTrack=Ko,e.deriveKeys=function(e,t){return Ct(this,void 0,void 0,(function*(){const i=rn(e.algorithm.name,t),n=yield crypto.subtle.deriveKey(i,e,{name:zi,length:128},!1,["encrypt","decrypt"]);return{material:e,encryptionKey:n}}))},e.detachTrack=ws,e.facingModeFromDeviceLabel=ta,e.facingModeFromLocalTrack=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var i;const n=e instanceof fr?e.mediaStreamTrack:e,s=n.getSettings();let r={facingMode:null!==(i=t.defaultFacingMode)&&void 0!==i?i:"user",confidence:"low"};if("facingMode"in s){const e=s.facingMode;c.debug("rawFacingMode",{rawFacingMode:e}),e&&"string"==typeof e&&function(e){const t=["user","environment","left","right"];return void 0===e||t.includes(e)}(e)&&(r={facingMode:e,confidence:"high"})}if(["low","medium"].includes(r.confidence)){c.debug("Try to get facing mode from device label: (".concat(n.label,")"));const e=ta(n.label);void 0!==e&&(r=e)}return r},e.getEmptyAudioStreamTrack=dr,e.getEmptyVideoStreamTrack=function(){return or||(or=cr()),or.clone()},e.getLogger=d,e.importKey=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{name:zi},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"encrypt";return Ct(this,void 0,void 0,(function*(){return crypto.subtle.importKey("raw",e,t,!1,"derive"===i?["deriveBits","deriveKey"]:["encrypt","decrypt"])}))},e.isBackupCodec=Dn,e.isBrowserSupported=function(){return As()||Us()},e.isE2EESupported=$i,e.isInsertableStreamSupported=tn,e.isScriptTransformSupported=en,e.isVideoFrame=function(e){return"type"in e},e.needsRbspUnescaping=function(e){for(var t=0;t<e.length-3;t++)if(0==e[t]&&0==e[t+1]&&3==e[t+2])return!0;return!1},e.parseRbsp=function(e){const t=[];for(var i=e.length,n=0;n<e.length;)i-n>=3&&!e[n]&&!e[n+1]&&3==e[n+2]?(t.push(e[n++]),t.push(e[n++]),n++):t.push(e[n++]);return new Uint8Array(t)},e.protocolVersion=11,e.ratchet=function(e,t){return Ct(this,void 0,void 0,(function*(){const i=rn(e.algorithm.name,t);return crypto.subtle.deriveBits(i,e,256)}))},e.setLogExtension=function(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c;const n=i.methodFactory;i.methodFactory=(i,s,r)=>{const o=n(i,s,r),a=e.LogLevel[i],c=a>=s&&a<e.LogLevel.silent;return(e,i)=>{i?o(e,i):o(e),c&&t(a,e,i)}},i.setLevel(i.getLevel())},e.setLogLevel=function(e,t){t&&a.getLogger(t).setLevel(e);for(const t of Object.entries(a.getLoggers()).filter((e=>{let[t]=e;return t.startsWith("livekit")})).map((e=>{let[,t]=e;return t})))t.setLevel(e)},e.supportsAV1=js,e.supportsAdaptiveStream=function(){return void 0!==typeof ResizeObserver&&void 0!==typeof IntersectionObserver},e.supportsDynacast=function(){return As()},e.supportsVP9=Bs,e.version=wn,e.videoCodecs=On,e.writeRbsp=function(e){const t=[];for(var i=0,n=0;n<e.length;++n){var s=e[n];s<=3&&i>=2&&(t.push(3),i=0),t.push(s),0==s?++i:i=0}return new Uint8Array(t)}}));
//# sourceMappingURL=livekit-client.umd.js.map
