#!/bin/bash

echo "🔄 Restarting LiveKit with fixed configuration..."

# Stop existing containers
echo "📋 Stopping existing containers..."
docker-compose down

# Wait a moment
sleep 2

# Start containers with new configuration
echo "📋 Starting containers with fixed configuration..."
docker-compose up -d

# Wait for startup
echo "📋 Waiting for LiveKit server to start..."
sleep 5

# Check if containers are running
echo "📋 Container status:"
docker-compose ps

# Test LiveKit connection
echo "📋 Testing LiveKit connection..."
for i in {1..10}; do
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:7880 | grep -q "200\|404\|400"; then
        echo "✅ LiveKit server is responding!"
        break
    else
        echo "⏳ Waiting for LiveKit server... (attempt $i/10)"
        sleep 2
    fi
done

echo ""
echo "📋 LiveKit logs (last 20 lines):"
docker-compose logs --tail=20 livekit

echo ""
echo "✅ LiveKit restart complete!"
echo "🎯 You can now start the agent with:"
echo "   cd agent && source venv/bin/activate && python simple_agent.py"
