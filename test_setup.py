#!/usr/bin/env python3

"""
Test Setup Script for LiveKit + Pipecat Demo

This script:
1. Validates the environment setup
2. Tests LiveKit connectivity
3. Tests OpenAI API access
4. Provides setup recommendations
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {package_name}")
            return True
        else:
            print(f"❌ {package_name} not found")
            return False
    except ImportError:
        print(f"❌ {package_name} not found")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        ("livekit", "livekit"),
        ("openai", "openai"),
        ("numpy", "numpy"),
        ("websockets", "websockets"),
    ]
    
    all_installed = True
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            all_installed = False
    
    return all_installed

def check_config():
    """Check if configuration is properly set up"""
    print("\n⚙️  Checking configuration...")
    
    # Add agent directory to path
    agent_dir = os.path.join(os.path.dirname(__file__), 'agent')
    sys.path.insert(0, agent_dir)
    
    try:
        import config
        print("✅ config.py found")
        
        # Check LiveKit configuration
        if hasattr(config, 'LIVEKIT_URL') and config.LIVEKIT_URL:
            if config.LIVEKIT_URL != "wss://your-project.livekit.cloud":
                print(f"✅ LiveKit URL: {config.LIVEKIT_URL}")
            else:
                print("⚠️  LiveKit URL not configured (using template)")
        else:
            print("❌ LIVEKIT_URL not set")
            return False
        
        # Check API keys
        if hasattr(config, 'OPENAI_API_KEY') and config.OPENAI_API_KEY:
            if config.OPENAI_API_KEY != "your-openai-api-key":
                print("✅ OpenAI API key configured")
            else:
                print("❌ OpenAI API key not configured")
                return False
        else:
            print("❌ OPENAI_API_KEY not set")
            return False
        
        return True
        
    except ImportError:
        print("❌ config.py not found")
        print("   Please copy agent/config.py.template to agent/config.py")
        return False

def test_openai_connection():
    """Test OpenAI API connection"""
    print("\n🤖 Testing OpenAI connection...")
    
    try:
        # Add agent directory to path
        agent_dir = os.path.join(os.path.dirname(__file__), 'agent')
        sys.path.insert(0, agent_dir)
        
        import config
        import openai
        
        client = openai.OpenAI(api_key=config.OPENAI_API_KEY)
        
        # Test with a simple completion
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=5
        )
        
        print("✅ OpenAI API connection successful")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API connection failed: {e}")
        return False

def test_livekit_connection():
    """Test LiveKit connection"""
    print("\n🎥 Testing LiveKit connection...")
    
    try:
        # Add agent directory to path
        agent_dir = os.path.join(os.path.dirname(__file__), 'agent')
        sys.path.insert(0, agent_dir)
        
        import config
        from livekit import api
        
        # Try to create an access token (this validates the credentials)
        token = api.AccessToken(config.LIVEKIT_API_KEY, config.LIVEKIT_API_SECRET) \
            .with_identity("test-participant") \
            .with_grants(api.VideoGrants(room_join=True, room="test-room"))
        
        jwt_token = token.to_jwt()
        
        if jwt_token:
            print("✅ LiveKit credentials valid")
            return True
        else:
            print("❌ Failed to generate LiveKit token")
            return False
        
    except Exception as e:
        print(f"❌ LiveKit connection test failed: {e}")
        return False

def check_docker():
    """Check if Docker is available for local LiveKit server"""
    print("\n🐳 Checking Docker availability...")
    
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ Docker available: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker not available")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Docker not found")
        return False

def provide_recommendations():
    """Provide setup recommendations"""
    print("\n💡 Setup Recommendations:")
    print("1. For production: Use LiveKit Cloud (https://livekit.io)")
    print("2. For development: Use local Docker setup")
    print("3. Ensure microphone permissions in browser")
    print("4. Use Chrome/Firefox for best WebRTC support")
    print("5. Test with good internet connection for low latency")

def main():
    """Main test function"""
    print("🧪 LiveKit + Pipecat Demo Setup Test")
    print("=" * 50)
    
    all_good = True
    
    # Check Python version
    if not check_python_version():
        all_good = False
    
    # Check dependencies
    if not check_dependencies():
        all_good = False
        print("\n📥 To install missing dependencies:")
        print("cd agent && pip install -r requirements.txt")
    
    # Check configuration
    if not check_config():
        all_good = False
    
    # Test connections if config is good
    if all_good:
        if not test_openai_connection():
            all_good = False
        
        if not test_livekit_connection():
            all_good = False
    
    # Check Docker
    check_docker()
    
    # Provide recommendations
    provide_recommendations()
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 All tests passed! You're ready to run the demo.")
        print("\nNext steps:")
        print("1. Start LiveKit server: docker-compose up -d")
        print("2. Start agent: cd agent && python simple_agent.py")
        print("3. Open client/index.html in browser")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
