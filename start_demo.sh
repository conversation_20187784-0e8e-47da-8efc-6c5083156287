#!/bin/bash

# LiveKit + Pipecat Demo Startup Script
echo "🚀 Starting LiveKit + Pipecat Demo"
echo "=================================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Please install Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose first."
    exit 1
fi

echo "📋 Checking Docker containers..."

# Check if containers are running
LIVEKIT_RUNNING=$(docker ps --filter "name=livekit-pipecat-demo-livekit" --format "{{.Names}}" | wc -l)
REDIS_RUNNING=$(docker ps --filter "name=livekit-pipecat-demo-redis" --format "{{.Names}}" | wc -l)

if [ "$LIVEKIT_RUNNING" -eq 0 ] || [ "$REDIS_RUNNING" -eq 0 ]; then
    echo "📋 Starting Docker containers..."
    docker-compose up -d
    
    echo "📋 Waiting for LiveKit server to be ready..."
    sleep 5
    
    # Test LiveKit connection
    for i in {1..10}; do
        if curl -s http://localhost:7880 > /dev/null 2>&1; then
            echo "✅ LiveKit server is ready!"
            break
        else
            echo "⏳ Waiting for LiveKit server... (attempt $i/10)"
            sleep 2
        fi
    done
else
    echo "✅ Docker containers are already running"
fi

echo ""
echo "📋 Container Status:"
docker ps --filter "name=livekit-pipecat-demo" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "📋 Starting Python agent..."

# Check if virtual environment exists
if [ ! -d "agent/venv" ]; then
    echo "❌ Virtual environment not found. Please run ./deploy.sh first."
    exit 1
fi

# Start the agent in background
cd agent
source venv/bin/activate
echo "🤖 Starting agent..."
python simple_agent.py &
AGENT_PID=$!

echo "✅ Agent started with PID: $AGENT_PID"
echo ""
echo "🎯 Demo is ready!"
echo "=================="
echo "📱 Web Client: http://localhost:8000"
echo "🎙️ Simple Demo: http://localhost:8000/simple.html"
echo "🧪 SDK Test: http://localhost:8000/test.html"
echo "📊 Status: http://localhost:8000/status.html"
echo ""
echo "💡 Instructions:"
echo "1. Open http://localhost:8000 in your browser"
echo "2. Grant microphone permission when prompted"
echo "3. Click 'Join Room' to start the demo"
echo "4. Speak and hear the AI echo your words with '...got it'"
echo ""
echo "🛑 To stop the demo:"
echo "   Press Ctrl+C to stop this script"
echo "   Run: docker-compose down"
echo ""

# Wait for user to stop
echo "Press Ctrl+C to stop the demo..."
trap "echo ''; echo '🛑 Stopping demo...'; kill $AGENT_PID 2>/dev/null; docker-compose down; echo '✅ Demo stopped'; exit 0" INT

# Keep script running
while true; do
    sleep 1
done
