#!/bin/bash

# LiveKit + Pipecat Demo Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DEMO_NAME="LiveKit + Pipecat Demo"
AGENT_DIR="agent"
CLIENT_DIR="client"
PYTHON_VERSION="3.8"

echo -e "${PURPLE}🚀 ${DEMO_NAME} Deployment${NC}"
echo "=" * 50

# Function to print status
print_status() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Check Python version
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "Python not found. Please install Python ${PYTHON_VERSION}+"
        exit 1
    fi
    
    PYTHON_VER=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_success "Python ${PYTHON_VER} found"
}

# Check Docker
check_docker() {
    print_status "Checking Docker..."
    
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            print_success "Docker is running"
            DOCKER_AVAILABLE=true
        else
            print_warning "Docker found but not running"
            DOCKER_AVAILABLE=false
        fi
    else
        print_warning "Docker not found"
        DOCKER_AVAILABLE=false
    fi
}

# Setup Python environment
setup_python_env() {
    print_status "Setting up Python environment..."
    
    cd $AGENT_DIR
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating virtual environment..."
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install requirements
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    print_success "Python environment ready"
    cd ..
}

# Setup configuration
setup_config() {
    print_status "Setting up configuration..."
    
    if [ ! -f "$AGENT_DIR/config.py" ]; then
        print_status "Creating config.py from template..."
        cp "$AGENT_DIR/config.py.template" "$AGENT_DIR/config.py"
        print_warning "Please edit $AGENT_DIR/config.py with your credentials"
        CONFIG_NEEDS_EDIT=true
    else
        print_success "config.py already exists"
        CONFIG_NEEDS_EDIT=false
    fi
}

# Start LiveKit server
start_livekit() {
    if [ "$DOCKER_AVAILABLE" = true ]; then
        print_status "Starting LiveKit server..."
        
        # Check if already running
        if docker-compose ps | grep -q "livekit.*Up"; then
            print_success "LiveKit server already running"
        else
            docker-compose up -d
            
            # Wait for server to be ready
            print_status "Waiting for LiveKit server to be ready..."
            sleep 5
            
            if docker-compose ps | grep -q "livekit.*Up"; then
                print_success "LiveKit server started successfully"
                print_status "LiveKit server available at: ws://localhost:7880"
            else
                print_error "Failed to start LiveKit server"
                return 1
            fi
        fi
    else
        print_warning "Docker not available. Please use LiveKit Cloud or install Docker."
        print_status "To use LiveKit Cloud:"
        print_status "1. Sign up at https://livekit.io"
        print_status "2. Create a project"
        print_status "3. Update config.py with your credentials"
    fi
}

# Test setup
test_setup() {
    print_status "Checking configuration..."

    # Check if config.py exists and has required settings
    if [ ! -f "agent/config.py" ]; then
        print_error "config.py not found. Please run: cp agent/config.py.template agent/config.py"
        return 1
    fi

    # Check if OpenAI API key is set
    if grep -q "your-openai-api-key-here" agent/config.py; then
        print_warning "OpenAI API key not configured in agent/config.py"
        print_status "Demo will work but agent responses will be limited"
    fi

    print_success "Configuration check passed"
    return 0
}

# Start agent
start_agent() {
    print_status "Starting agent..."
    
    cd $AGENT_DIR
    source venv/bin/activate
    
    # Check if config is properly set up
    if grep -q "your-openai-api-key" config.py; then
        print_error "Please configure your OpenAI API key in config.py"
        cd ..
        return 1
    fi
    
    print_status "Agent starting in background..."
    nohup $PYTHON_CMD spawn_agent.py > agent.log 2>&1 &
    AGENT_PID=$!
    echo $AGENT_PID > agent.pid
    
    sleep 3
    
    if kill -0 $AGENT_PID 2>/dev/null; then
        print_success "Agent started successfully (PID: $AGENT_PID)"
        print_status "Agent logs: tail -f $AGENT_DIR/agent.log"
    else
        print_error "Failed to start agent"
        cd ..
        return 1
    fi
    
    cd ..
}

# Start web server for client
start_client() {
    print_status "Starting web client..."
    
    # Check if Python http.server is available
    if $PYTHON_CMD -m http.server --help &> /dev/null; then
        cd $CLIENT_DIR
        print_status "Starting HTTP server on port 8000..."
        nohup $PYTHON_CMD -m http.server 8000 > ../client.log 2>&1 &
        CLIENT_PID=$!
        echo $CLIENT_PID > ../client.pid
        
        sleep 2
        
        if kill -0 $CLIENT_PID 2>/dev/null; then
            print_success "Web client started successfully (PID: $CLIENT_PID)"
            print_status "Client available at: http://localhost:8000"
        else
            print_error "Failed to start web client"
            cd ..
            return 1
        fi
        
        cd ..
    else
        print_warning "Python http.server not available"
        print_status "Please serve the client directory with your preferred web server"
        print_status "Or open client/index.html directly in your browser"
    fi
}

# Show status
show_status() {
    echo ""
    echo -e "${PURPLE}🎉 Deployment Complete!${NC}"
    echo "=" * 50
    
    echo -e "${GREEN}Services Status:${NC}"
    
    # Check LiveKit
    if [ "$DOCKER_AVAILABLE" = true ] && docker-compose ps | grep -q "livekit.*Up"; then
        echo "✅ LiveKit Server: Running (ws://localhost:7880)"
    else
        echo "⚠️  LiveKit Server: Not running locally (use LiveKit Cloud)"
    fi
    
    # Check Agent
    if [ -f "$AGENT_DIR/agent.pid" ] && kill -0 $(cat "$AGENT_DIR/agent.pid") 2>/dev/null; then
        echo "✅ AI Agent: Running (PID: $(cat "$AGENT_DIR/agent.pid"))"
    else
        echo "❌ AI Agent: Not running"
    fi
    
    # Check Client
    if [ -f "client.pid" ] && kill -0 $(cat "client.pid") 2>/dev/null; then
        echo "✅ Web Client: Running (http://localhost:8000)"
    else
        echo "⚠️  Web Client: Serve manually or open client/index.html"
    fi
    
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Open http://localhost:8000 in your browser"
    echo "2. Click 'Join Room' to connect"
    echo "3. Start speaking to test the echo agent"
    echo "4. Use 'Send Beep' to test latency"
    
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo "• View agent logs: tail -f $AGENT_DIR/agent.log"
    echo "• Stop agent: kill \$(cat $AGENT_DIR/agent.pid)"
    echo "• Stop client: kill \$(cat client.pid)"
    echo "• Stop LiveKit: docker-compose down"
    
    if [ "$CONFIG_NEEDS_EDIT" = true ]; then
        echo ""
        print_warning "Don't forget to edit $AGENT_DIR/config.py with your credentials!"
    fi
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    # Stop agent
    if [ -f "$AGENT_DIR/agent.pid" ]; then
        kill $(cat "$AGENT_DIR/agent.pid") 2>/dev/null || true
        rm "$AGENT_DIR/agent.pid"
    fi
    
    # Stop client
    if [ -f "client.pid" ]; then
        kill $(cat "client.pid") 2>/dev/null || true
        rm "client.pid"
    fi
    
    print_success "Cleanup complete"
}

# Handle script interruption
trap cleanup EXIT

# Main deployment flow
main() {
    case "${1:-deploy}" in
        "deploy")
            check_python
            check_docker
            setup_python_env
            setup_config
            start_livekit
            
            if test_setup; then
                start_agent
                start_client
                show_status
            else
                print_error "Setup tests failed. Please fix configuration and try again."
                exit 1
            fi
            ;;
        "test")
            check_python
            setup_python_env
            test_setup
            ;;
        "stop")
            cleanup
            if [ "$DOCKER_AVAILABLE" = true ]; then
                docker-compose down
            fi
            print_success "All services stopped"
            ;;
        "status")
            show_status
            ;;
        *)
            echo "Usage: $0 {deploy|test|stop|status}"
            echo ""
            echo "Commands:"
            echo "  deploy  - Full deployment (default)"
            echo "  test    - Run setup tests only"
            echo "  stop    - Stop all services"
            echo "  status  - Show service status"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
