"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
from . import handle_pb2
import sys
from . import track_pb2
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SoxResamplerDataType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SoxResamplerDataTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SoxResamplerDataType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SOXR_DATATYPE_INT16I: _SoxResamplerDataType.ValueType  # 0
    """TODO(theomonnom): support other datatypes (shouldn't really be needed)"""
    SOXR_DATATYPE_INT16S: _SoxResamplerDataType.ValueType  # 1

class SoxResamplerDataType(_SoxResamplerDataType, metaclass=_SoxResamplerDataTypeEnumTypeWrapper): ...

SOXR_DATATYPE_INT16I: SoxResamplerDataType.ValueType  # 0
"""TODO(theomonnom): support other datatypes (shouldn't really be needed)"""
SOXR_DATATYPE_INT16S: SoxResamplerDataType.ValueType  # 1
global___SoxResamplerDataType = SoxResamplerDataType

class _SoxQualityRecipe:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SoxQualityRecipeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SoxQualityRecipe.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SOXR_QUALITY_QUICK: _SoxQualityRecipe.ValueType  # 0
    SOXR_QUALITY_LOW: _SoxQualityRecipe.ValueType  # 1
    SOXR_QUALITY_MEDIUM: _SoxQualityRecipe.ValueType  # 2
    SOXR_QUALITY_HIGH: _SoxQualityRecipe.ValueType  # 3
    SOXR_QUALITY_VERYHIGH: _SoxQualityRecipe.ValueType  # 4

class SoxQualityRecipe(_SoxQualityRecipe, metaclass=_SoxQualityRecipeEnumTypeWrapper): ...

SOXR_QUALITY_QUICK: SoxQualityRecipe.ValueType  # 0
SOXR_QUALITY_LOW: SoxQualityRecipe.ValueType  # 1
SOXR_QUALITY_MEDIUM: SoxQualityRecipe.ValueType  # 2
SOXR_QUALITY_HIGH: SoxQualityRecipe.ValueType  # 3
SOXR_QUALITY_VERYHIGH: SoxQualityRecipe.ValueType  # 4
global___SoxQualityRecipe = SoxQualityRecipe

class _SoxFlagBits:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SoxFlagBitsEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SoxFlagBits.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SOXR_ROLLOFF_SMALL: _SoxFlagBits.ValueType  # 0
    """1 << 0"""
    SOXR_ROLLOFF_MEDIUM: _SoxFlagBits.ValueType  # 1
    """1 << 1"""
    SOXR_ROLLOFF_NONE: _SoxFlagBits.ValueType  # 2
    """1 << 2"""
    SOXR_HIGH_PREC_CLOCK: _SoxFlagBits.ValueType  # 3
    """1 << 3"""
    SOXR_DOUBLE_PRECISION: _SoxFlagBits.ValueType  # 4
    """1 << 4"""
    SOXR_VR: _SoxFlagBits.ValueType  # 5
    """1 << 5"""

class SoxFlagBits(_SoxFlagBits, metaclass=_SoxFlagBitsEnumTypeWrapper): ...

SOXR_ROLLOFF_SMALL: SoxFlagBits.ValueType  # 0
"""1 << 0"""
SOXR_ROLLOFF_MEDIUM: SoxFlagBits.ValueType  # 1
"""1 << 1"""
SOXR_ROLLOFF_NONE: SoxFlagBits.ValueType  # 2
"""1 << 2"""
SOXR_HIGH_PREC_CLOCK: SoxFlagBits.ValueType  # 3
"""1 << 3"""
SOXR_DOUBLE_PRECISION: SoxFlagBits.ValueType  # 4
"""1 << 4"""
SOXR_VR: SoxFlagBits.ValueType  # 5
"""1 << 5"""
global___SoxFlagBits = SoxFlagBits

class _AudioStreamType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AudioStreamTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AudioStreamType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    AUDIO_STREAM_NATIVE: _AudioStreamType.ValueType  # 0
    AUDIO_STREAM_HTML: _AudioStreamType.ValueType  # 1

class AudioStreamType(_AudioStreamType, metaclass=_AudioStreamTypeEnumTypeWrapper):
    """
    AudioStream
    """

AUDIO_STREAM_NATIVE: AudioStreamType.ValueType  # 0
AUDIO_STREAM_HTML: AudioStreamType.ValueType  # 1
global___AudioStreamType = AudioStreamType

class _AudioSourceType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AudioSourceTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AudioSourceType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    AUDIO_SOURCE_NATIVE: _AudioSourceType.ValueType  # 0

class AudioSourceType(_AudioSourceType, metaclass=_AudioSourceTypeEnumTypeWrapper): ...

AUDIO_SOURCE_NATIVE: AudioSourceType.ValueType  # 0
global___AudioSourceType = AudioSourceType

@typing.final
class NewAudioStreamRequest(google.protobuf.message.Message):
    """Create a new AudioStream
    AudioStream is used to receive audio frames from a track
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_HANDLE_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    AUDIO_FILTER_MODULE_ID_FIELD_NUMBER: builtins.int
    AUDIO_FILTER_OPTIONS_FIELD_NUMBER: builtins.int
    track_handle: builtins.int
    type: global___AudioStreamType.ValueType
    sample_rate: builtins.int
    num_channels: builtins.int
    audio_filter_module_id: builtins.str
    """Unique identifier passed in LoadAudioFilterPluginRequest"""
    audio_filter_options: builtins.str
    def __init__(
        self,
        *,
        track_handle: builtins.int | None = ...,
        type: global___AudioStreamType.ValueType | None = ...,
        sample_rate: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
        audio_filter_module_id: builtins.str | None = ...,
        audio_filter_options: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_filter_module_id", b"audio_filter_module_id", "audio_filter_options", b"audio_filter_options", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "track_handle", b"track_handle", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_filter_module_id", b"audio_filter_module_id", "audio_filter_options", b"audio_filter_options", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "track_handle", b"track_handle", "type", b"type"]) -> None: ...

global___NewAudioStreamRequest = NewAudioStreamRequest

@typing.final
class NewAudioStreamResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STREAM_FIELD_NUMBER: builtins.int
    @property
    def stream(self) -> global___OwnedAudioStream: ...
    def __init__(
        self,
        *,
        stream: global___OwnedAudioStream | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["stream", b"stream"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["stream", b"stream"]) -> None: ...

global___NewAudioStreamResponse = NewAudioStreamResponse

@typing.final
class AudioStreamFromParticipantRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    TRACK_SOURCE_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    AUDIO_FILTER_MODULE_ID_FIELD_NUMBER: builtins.int
    AUDIO_FILTER_OPTIONS_FIELD_NUMBER: builtins.int
    participant_handle: builtins.int
    type: global___AudioStreamType.ValueType
    track_source: track_pb2.TrackSource.ValueType
    sample_rate: builtins.int
    num_channels: builtins.int
    audio_filter_module_id: builtins.str
    audio_filter_options: builtins.str
    def __init__(
        self,
        *,
        participant_handle: builtins.int | None = ...,
        type: global___AudioStreamType.ValueType | None = ...,
        track_source: track_pb2.TrackSource.ValueType | None = ...,
        sample_rate: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
        audio_filter_module_id: builtins.str | None = ...,
        audio_filter_options: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_filter_module_id", b"audio_filter_module_id", "audio_filter_options", b"audio_filter_options", "num_channels", b"num_channels", "participant_handle", b"participant_handle", "sample_rate", b"sample_rate", "track_source", b"track_source", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_filter_module_id", b"audio_filter_module_id", "audio_filter_options", b"audio_filter_options", "num_channels", b"num_channels", "participant_handle", b"participant_handle", "sample_rate", b"sample_rate", "track_source", b"track_source", "type", b"type"]) -> None: ...

global___AudioStreamFromParticipantRequest = AudioStreamFromParticipantRequest

@typing.final
class AudioStreamFromParticipantResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STREAM_FIELD_NUMBER: builtins.int
    @property
    def stream(self) -> global___OwnedAudioStream: ...
    def __init__(
        self,
        *,
        stream: global___OwnedAudioStream | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["stream", b"stream"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["stream", b"stream"]) -> None: ...

global___AudioStreamFromParticipantResponse = AudioStreamFromParticipantResponse

@typing.final
class NewAudioSourceRequest(google.protobuf.message.Message):
    """Create a new AudioSource"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    QUEUE_SIZE_MS_FIELD_NUMBER: builtins.int
    type: global___AudioSourceType.ValueType
    sample_rate: builtins.int
    num_channels: builtins.int
    queue_size_ms: builtins.int
    @property
    def options(self) -> global___AudioSourceOptions: ...
    def __init__(
        self,
        *,
        type: global___AudioSourceType.ValueType | None = ...,
        options: global___AudioSourceOptions | None = ...,
        sample_rate: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
        queue_size_ms: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["num_channels", b"num_channels", "options", b"options", "queue_size_ms", b"queue_size_ms", "sample_rate", b"sample_rate", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["num_channels", b"num_channels", "options", b"options", "queue_size_ms", b"queue_size_ms", "sample_rate", b"sample_rate", "type", b"type"]) -> None: ...

global___NewAudioSourceRequest = NewAudioSourceRequest

@typing.final
class NewAudioSourceResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOURCE_FIELD_NUMBER: builtins.int
    @property
    def source(self) -> global___OwnedAudioSource: ...
    def __init__(
        self,
        *,
        source: global___OwnedAudioSource | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["source", b"source"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["source", b"source"]) -> None: ...

global___NewAudioSourceResponse = NewAudioSourceResponse

@typing.final
class CaptureAudioFrameRequest(google.protobuf.message.Message):
    """Push a frame to an AudioSource 
    The data provided must be available as long as the client receive the callback.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOURCE_HANDLE_FIELD_NUMBER: builtins.int
    BUFFER_FIELD_NUMBER: builtins.int
    source_handle: builtins.int
    @property
    def buffer(self) -> global___AudioFrameBufferInfo: ...
    def __init__(
        self,
        *,
        source_handle: builtins.int | None = ...,
        buffer: global___AudioFrameBufferInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["buffer", b"buffer", "source_handle", b"source_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["buffer", b"buffer", "source_handle", b"source_handle"]) -> None: ...

global___CaptureAudioFrameRequest = CaptureAudioFrameRequest

@typing.final
class CaptureAudioFrameResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___CaptureAudioFrameResponse = CaptureAudioFrameResponse

@typing.final
class CaptureAudioFrameCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___CaptureAudioFrameCallback = CaptureAudioFrameCallback

@typing.final
class ClearAudioBufferRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOURCE_HANDLE_FIELD_NUMBER: builtins.int
    source_handle: builtins.int
    def __init__(
        self,
        *,
        source_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["source_handle", b"source_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["source_handle", b"source_handle"]) -> None: ...

global___ClearAudioBufferRequest = ClearAudioBufferRequest

@typing.final
class ClearAudioBufferResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ClearAudioBufferResponse = ClearAudioBufferResponse

@typing.final
class NewAudioResamplerRequest(google.protobuf.message.Message):
    """Create a new AudioResampler"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___NewAudioResamplerRequest = NewAudioResamplerRequest

@typing.final
class NewAudioResamplerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESAMPLER_FIELD_NUMBER: builtins.int
    @property
    def resampler(self) -> global___OwnedAudioResampler: ...
    def __init__(
        self,
        *,
        resampler: global___OwnedAudioResampler | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["resampler", b"resampler"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["resampler", b"resampler"]) -> None: ...

global___NewAudioResamplerResponse = NewAudioResamplerResponse

@typing.final
class RemixAndResampleRequest(google.protobuf.message.Message):
    """Remix and resample an audio frame"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESAMPLER_HANDLE_FIELD_NUMBER: builtins.int
    BUFFER_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    resampler_handle: builtins.int
    num_channels: builtins.int
    sample_rate: builtins.int
    @property
    def buffer(self) -> global___AudioFrameBufferInfo: ...
    def __init__(
        self,
        *,
        resampler_handle: builtins.int | None = ...,
        buffer: global___AudioFrameBufferInfo | None = ...,
        num_channels: builtins.int | None = ...,
        sample_rate: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["buffer", b"buffer", "num_channels", b"num_channels", "resampler_handle", b"resampler_handle", "sample_rate", b"sample_rate"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["buffer", b"buffer", "num_channels", b"num_channels", "resampler_handle", b"resampler_handle", "sample_rate", b"sample_rate"]) -> None: ...

global___RemixAndResampleRequest = RemixAndResampleRequest

@typing.final
class RemixAndResampleResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BUFFER_FIELD_NUMBER: builtins.int
    @property
    def buffer(self) -> global___OwnedAudioFrameBuffer: ...
    def __init__(
        self,
        *,
        buffer: global___OwnedAudioFrameBuffer | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["buffer", b"buffer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["buffer", b"buffer"]) -> None: ...

global___RemixAndResampleResponse = RemixAndResampleResponse

@typing.final
class NewApmRequest(google.protobuf.message.Message):
    """AEC"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ECHO_CANCELLER_ENABLED_FIELD_NUMBER: builtins.int
    GAIN_CONTROLLER_ENABLED_FIELD_NUMBER: builtins.int
    HIGH_PASS_FILTER_ENABLED_FIELD_NUMBER: builtins.int
    NOISE_SUPPRESSION_ENABLED_FIELD_NUMBER: builtins.int
    echo_canceller_enabled: builtins.bool
    gain_controller_enabled: builtins.bool
    high_pass_filter_enabled: builtins.bool
    noise_suppression_enabled: builtins.bool
    def __init__(
        self,
        *,
        echo_canceller_enabled: builtins.bool | None = ...,
        gain_controller_enabled: builtins.bool | None = ...,
        high_pass_filter_enabled: builtins.bool | None = ...,
        noise_suppression_enabled: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["echo_canceller_enabled", b"echo_canceller_enabled", "gain_controller_enabled", b"gain_controller_enabled", "high_pass_filter_enabled", b"high_pass_filter_enabled", "noise_suppression_enabled", b"noise_suppression_enabled"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["echo_canceller_enabled", b"echo_canceller_enabled", "gain_controller_enabled", b"gain_controller_enabled", "high_pass_filter_enabled", b"high_pass_filter_enabled", "noise_suppression_enabled", b"noise_suppression_enabled"]) -> None: ...

global___NewApmRequest = NewApmRequest

@typing.final
class NewApmResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APM_FIELD_NUMBER: builtins.int
    @property
    def apm(self) -> global___OwnedApm: ...
    def __init__(
        self,
        *,
        apm: global___OwnedApm | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["apm", b"apm"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["apm", b"apm"]) -> None: ...

global___NewApmResponse = NewApmResponse

@typing.final
class ApmProcessStreamRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APM_HANDLE_FIELD_NUMBER: builtins.int
    DATA_PTR_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    apm_handle: builtins.int
    data_ptr: builtins.int
    """*mut i16"""
    size: builtins.int
    """in bytes"""
    sample_rate: builtins.int
    num_channels: builtins.int
    def __init__(
        self,
        *,
        apm_handle: builtins.int | None = ...,
        data_ptr: builtins.int | None = ...,
        size: builtins.int | None = ...,
        sample_rate: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["apm_handle", b"apm_handle", "data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["apm_handle", b"apm_handle", "data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "size", b"size"]) -> None: ...

global___ApmProcessStreamRequest = ApmProcessStreamRequest

@typing.final
class ApmProcessStreamResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___ApmProcessStreamResponse = ApmProcessStreamResponse

@typing.final
class ApmProcessReverseStreamRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APM_HANDLE_FIELD_NUMBER: builtins.int
    DATA_PTR_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    apm_handle: builtins.int
    data_ptr: builtins.int
    """*mut i16"""
    size: builtins.int
    """in bytes"""
    sample_rate: builtins.int
    num_channels: builtins.int
    def __init__(
        self,
        *,
        apm_handle: builtins.int | None = ...,
        data_ptr: builtins.int | None = ...,
        size: builtins.int | None = ...,
        sample_rate: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["apm_handle", b"apm_handle", "data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["apm_handle", b"apm_handle", "data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "size", b"size"]) -> None: ...

global___ApmProcessReverseStreamRequest = ApmProcessReverseStreamRequest

@typing.final
class ApmProcessReverseStreamResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___ApmProcessReverseStreamResponse = ApmProcessReverseStreamResponse

@typing.final
class NewSoxResamplerRequest(google.protobuf.message.Message):
    """New resampler using SoX (much better quality)"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_RATE_FIELD_NUMBER: builtins.int
    OUTPUT_RATE_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    INPUT_DATA_TYPE_FIELD_NUMBER: builtins.int
    OUTPUT_DATA_TYPE_FIELD_NUMBER: builtins.int
    QUALITY_RECIPE_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    input_rate: builtins.float
    output_rate: builtins.float
    num_channels: builtins.int
    input_data_type: global___SoxResamplerDataType.ValueType
    output_data_type: global___SoxResamplerDataType.ValueType
    quality_recipe: global___SoxQualityRecipe.ValueType
    flags: builtins.int
    def __init__(
        self,
        *,
        input_rate: builtins.float | None = ...,
        output_rate: builtins.float | None = ...,
        num_channels: builtins.int | None = ...,
        input_data_type: global___SoxResamplerDataType.ValueType | None = ...,
        output_data_type: global___SoxResamplerDataType.ValueType | None = ...,
        quality_recipe: global___SoxQualityRecipe.ValueType | None = ...,
        flags: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["flags", b"flags", "input_data_type", b"input_data_type", "input_rate", b"input_rate", "num_channels", b"num_channels", "output_data_type", b"output_data_type", "output_rate", b"output_rate", "quality_recipe", b"quality_recipe"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["flags", b"flags", "input_data_type", b"input_data_type", "input_rate", b"input_rate", "num_channels", b"num_channels", "output_data_type", b"output_data_type", "output_rate", b"output_rate", "quality_recipe", b"quality_recipe"]) -> None: ...

global___NewSoxResamplerRequest = NewSoxResamplerRequest

@typing.final
class NewSoxResamplerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESAMPLER_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    @property
    def resampler(self) -> global___OwnedSoxResampler: ...
    def __init__(
        self,
        *,
        resampler: global___OwnedSoxResampler | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "message", b"message", "resampler", b"resampler"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "message", b"message", "resampler", b"resampler"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["resampler", "error"] | None: ...

global___NewSoxResamplerResponse = NewSoxResamplerResponse

@typing.final
class PushSoxResamplerRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESAMPLER_HANDLE_FIELD_NUMBER: builtins.int
    DATA_PTR_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    resampler_handle: builtins.int
    data_ptr: builtins.int
    """*const i16"""
    size: builtins.int
    """in bytes"""
    def __init__(
        self,
        *,
        resampler_handle: builtins.int | None = ...,
        data_ptr: builtins.int | None = ...,
        size: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data_ptr", b"data_ptr", "resampler_handle", b"resampler_handle", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data_ptr", b"data_ptr", "resampler_handle", b"resampler_handle", "size", b"size"]) -> None: ...

global___PushSoxResamplerRequest = PushSoxResamplerRequest

@typing.final
class PushSoxResamplerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OUTPUT_PTR_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    output_ptr: builtins.int
    """*const i16 (could be null)"""
    size: builtins.int
    """in bytes"""
    error: builtins.str
    def __init__(
        self,
        *,
        output_ptr: builtins.int | None = ...,
        size: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "output_ptr", b"output_ptr", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "output_ptr", b"output_ptr", "size", b"size"]) -> None: ...

global___PushSoxResamplerResponse = PushSoxResamplerResponse

@typing.final
class FlushSoxResamplerRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESAMPLER_HANDLE_FIELD_NUMBER: builtins.int
    resampler_handle: builtins.int
    def __init__(
        self,
        *,
        resampler_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["resampler_handle", b"resampler_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["resampler_handle", b"resampler_handle"]) -> None: ...

global___FlushSoxResamplerRequest = FlushSoxResamplerRequest

@typing.final
class FlushSoxResamplerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OUTPUT_PTR_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    output_ptr: builtins.int
    """*const i16 (could be null)"""
    size: builtins.int
    """in bytes"""
    error: builtins.str
    def __init__(
        self,
        *,
        output_ptr: builtins.int | None = ...,
        size: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "output_ptr", b"output_ptr", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "output_ptr", b"output_ptr", "size", b"size"]) -> None: ...

global___FlushSoxResamplerResponse = FlushSoxResamplerResponse

@typing.final
class AudioFrameBufferInfo(google.protobuf.message.Message):
    """
    AudioFrame buffer
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_PTR_FIELD_NUMBER: builtins.int
    NUM_CHANNELS_FIELD_NUMBER: builtins.int
    SAMPLE_RATE_FIELD_NUMBER: builtins.int
    SAMPLES_PER_CHANNEL_FIELD_NUMBER: builtins.int
    data_ptr: builtins.int
    """*const i16"""
    num_channels: builtins.int
    sample_rate: builtins.int
    samples_per_channel: builtins.int
    def __init__(
        self,
        *,
        data_ptr: builtins.int | None = ...,
        num_channels: builtins.int | None = ...,
        sample_rate: builtins.int | None = ...,
        samples_per_channel: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "samples_per_channel", b"samples_per_channel"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data_ptr", b"data_ptr", "num_channels", b"num_channels", "sample_rate", b"sample_rate", "samples_per_channel", b"samples_per_channel"]) -> None: ...

global___AudioFrameBufferInfo = AudioFrameBufferInfo

@typing.final
class OwnedAudioFrameBuffer(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___AudioFrameBufferInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___AudioFrameBufferInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedAudioFrameBuffer = OwnedAudioFrameBuffer

@typing.final
class AudioStreamInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    type: global___AudioStreamType.ValueType
    def __init__(
        self,
        *,
        type: global___AudioStreamType.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["type", b"type"]) -> None: ...

global___AudioStreamInfo = AudioStreamInfo

@typing.final
class OwnedAudioStream(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___AudioStreamInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___AudioStreamInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedAudioStream = OwnedAudioStream

@typing.final
class AudioStreamEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STREAM_HANDLE_FIELD_NUMBER: builtins.int
    FRAME_RECEIVED_FIELD_NUMBER: builtins.int
    EOS_FIELD_NUMBER: builtins.int
    stream_handle: builtins.int
    @property
    def frame_received(self) -> global___AudioFrameReceived: ...
    @property
    def eos(self) -> global___AudioStreamEOS: ...
    def __init__(
        self,
        *,
        stream_handle: builtins.int | None = ...,
        frame_received: global___AudioFrameReceived | None = ...,
        eos: global___AudioStreamEOS | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["eos", b"eos", "frame_received", b"frame_received", "message", b"message", "stream_handle", b"stream_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["eos", b"eos", "frame_received", b"frame_received", "message", b"message", "stream_handle", b"stream_handle"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["frame_received", "eos"] | None: ...

global___AudioStreamEvent = AudioStreamEvent

@typing.final
class AudioFrameReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FRAME_FIELD_NUMBER: builtins.int
    @property
    def frame(self) -> global___OwnedAudioFrameBuffer: ...
    def __init__(
        self,
        *,
        frame: global___OwnedAudioFrameBuffer | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["frame", b"frame"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["frame", b"frame"]) -> None: ...

global___AudioFrameReceived = AudioFrameReceived

@typing.final
class AudioStreamEOS(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___AudioStreamEOS = AudioStreamEOS

@typing.final
class AudioSourceOptions(google.protobuf.message.Message):
    """
    AudioSource
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ECHO_CANCELLATION_FIELD_NUMBER: builtins.int
    NOISE_SUPPRESSION_FIELD_NUMBER: builtins.int
    AUTO_GAIN_CONTROL_FIELD_NUMBER: builtins.int
    echo_cancellation: builtins.bool
    noise_suppression: builtins.bool
    auto_gain_control: builtins.bool
    def __init__(
        self,
        *,
        echo_cancellation: builtins.bool | None = ...,
        noise_suppression: builtins.bool | None = ...,
        auto_gain_control: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["auto_gain_control", b"auto_gain_control", "echo_cancellation", b"echo_cancellation", "noise_suppression", b"noise_suppression"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["auto_gain_control", b"auto_gain_control", "echo_cancellation", b"echo_cancellation", "noise_suppression", b"noise_suppression"]) -> None: ...

global___AudioSourceOptions = AudioSourceOptions

@typing.final
class AudioSourceInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    type: global___AudioSourceType.ValueType
    def __init__(
        self,
        *,
        type: global___AudioSourceType.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["type", b"type"]) -> None: ...

global___AudioSourceInfo = AudioSourceInfo

@typing.final
class OwnedAudioSource(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___AudioSourceInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___AudioSourceInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedAudioSource = OwnedAudioSource

@typing.final
class AudioResamplerInfo(google.protobuf.message.Message):
    """
    AudioResampler
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___AudioResamplerInfo = AudioResamplerInfo

@typing.final
class OwnedAudioResampler(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___AudioResamplerInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___AudioResamplerInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedAudioResampler = OwnedAudioResampler

@typing.final
class OwnedApm(google.protobuf.message.Message):
    """
    AEC
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle"]) -> None: ...

global___OwnedApm = OwnedApm

@typing.final
class SoxResamplerInfo(google.protobuf.message.Message):
    """
    Sox AudioResampler
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SoxResamplerInfo = SoxResamplerInfo

@typing.final
class OwnedSoxResampler(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___SoxResamplerInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___SoxResamplerInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedSoxResampler = OwnedSoxResampler

@typing.final
class LoadAudioFilterPluginRequest(google.protobuf.message.Message):
    """Audio Filter Plugin"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLUGIN_PATH_FIELD_NUMBER: builtins.int
    DEPENDENCIES_FIELD_NUMBER: builtins.int
    MODULE_ID_FIELD_NUMBER: builtins.int
    plugin_path: builtins.str
    """path for ffi audio filter plugin"""
    module_id: builtins.str
    """Unique identifier of the plugin"""
    @property
    def dependencies(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Optional: paths for dependency dylibs"""

    def __init__(
        self,
        *,
        plugin_path: builtins.str | None = ...,
        dependencies: collections.abc.Iterable[builtins.str] | None = ...,
        module_id: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["module_id", b"module_id", "plugin_path", b"plugin_path"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dependencies", b"dependencies", "module_id", b"module_id", "plugin_path", b"plugin_path"]) -> None: ...

global___LoadAudioFilterPluginRequest = LoadAudioFilterPluginRequest

@typing.final
class LoadAudioFilterPluginResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___LoadAudioFilterPluginResponse = LoadAudioFilterPluginResponse
