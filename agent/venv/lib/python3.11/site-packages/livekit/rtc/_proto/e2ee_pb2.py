# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: e2ee.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\ne2ee.proto\x12\rlivekit.proto\"c\n\x0c\x46rameCryptor\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\x12\x11\n\tkey_index\x18\x03 \x02(\x05\x12\x0f\n\x07\x65nabled\x18\x04 \x02(\x08\"v\n\x12KeyProviderOptions\x12\x12\n\nshared_key\x18\x01 \x01(\x0c\x12\x1b\n\x13ratchet_window_size\x18\x02 \x02(\x05\x12\x14\n\x0cratchet_salt\x18\x03 \x02(\x0c\x12\x19\n\x11\x66\x61ilure_tolerance\x18\x04 \x02(\x05\"\x86\x01\n\x0b\x45\x32\x65\x65Options\x12\x36\n\x0f\x65ncryption_type\x18\x01 \x02(\x0e\x32\x1d.livekit.proto.EncryptionType\x12?\n\x14key_provider_options\x18\x02 \x02(\x0b\x32!.livekit.proto.KeyProviderOptions\"/\n\x1c\x45\x32\x65\x65ManagerSetEnabledRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x02(\x08\"\x1f\n\x1d\x45\x32\x65\x65ManagerSetEnabledResponse\"$\n\"E2eeManagerGetFrameCryptorsRequest\"Z\n#E2eeManagerGetFrameCryptorsResponse\x12\x33\n\x0e\x66rame_cryptors\x18\x01 \x03(\x0b\x32\x1b.livekit.proto.FrameCryptor\"a\n\x1d\x46rameCryptorSetEnabledRequest\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\x12\x0f\n\x07\x65nabled\x18\x03 \x02(\x08\" \n\x1e\x46rameCryptorSetEnabledResponse\"d\n\x1e\x46rameCryptorSetKeyIndexRequest\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\x12\x11\n\tkey_index\x18\x03 \x02(\x05\"!\n\x1f\x46rameCryptorSetKeyIndexResponse\"<\n\x13SetSharedKeyRequest\x12\x12\n\nshared_key\x18\x01 \x02(\x0c\x12\x11\n\tkey_index\x18\x02 \x02(\x05\"\x16\n\x14SetSharedKeyResponse\",\n\x17RatchetSharedKeyRequest\x12\x11\n\tkey_index\x18\x01 \x02(\x05\"+\n\x18RatchetSharedKeyResponse\x12\x0f\n\x07new_key\x18\x01 \x01(\x0c\"(\n\x13GetSharedKeyRequest\x12\x11\n\tkey_index\x18\x01 \x02(\x05\"#\n\x14GetSharedKeyResponse\x12\x0b\n\x03key\x18\x01 \x01(\x0c\"M\n\rSetKeyRequest\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x0b\n\x03key\x18\x02 \x02(\x0c\x12\x11\n\tkey_index\x18\x03 \x02(\x05\"\x10\n\x0eSetKeyResponse\"D\n\x11RatchetKeyRequest\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\tkey_index\x18\x02 \x02(\x05\"%\n\x12RatchetKeyResponse\x12\x0f\n\x07new_key\x18\x01 \x01(\x0c\"@\n\rGetKeyRequest\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\tkey_index\x18\x02 \x02(\x05\"\x1d\n\x0eGetKeyResponse\x12\x0b\n\x03key\x18\x01 \x01(\x0c\"\xcc\x05\n\x0b\x45\x32\x65\x65Request\x12\x13\n\x0broom_handle\x18\x01 \x02(\x04\x12J\n\x13manager_set_enabled\x18\x02 \x01(\x0b\x32+.livekit.proto.E2eeManagerSetEnabledRequestH\x00\x12W\n\x1amanager_get_frame_cryptors\x18\x03 \x01(\x0b\x32\x31.livekit.proto.E2eeManagerGetFrameCryptorsRequestH\x00\x12K\n\x13\x63ryptor_set_enabled\x18\x04 \x01(\x0b\x32,.livekit.proto.FrameCryptorSetEnabledRequestH\x00\x12N\n\x15\x63ryptor_set_key_index\x18\x05 \x01(\x0b\x32-.livekit.proto.FrameCryptorSetKeyIndexRequestH\x00\x12<\n\x0eset_shared_key\x18\x06 \x01(\x0b\x32\".livekit.proto.SetSharedKeyRequestH\x00\x12\x44\n\x12ratchet_shared_key\x18\x07 \x01(\x0b\x32&.livekit.proto.RatchetSharedKeyRequestH\x00\x12<\n\x0eget_shared_key\x18\x08 \x01(\x0b\x32\".livekit.proto.GetSharedKeyRequestH\x00\x12/\n\x07set_key\x18\t \x01(\x0b\x32\x1c.livekit.proto.SetKeyRequestH\x00\x12\x37\n\x0bratchet_key\x18\n \x01(\x0b\x32 .livekit.proto.RatchetKeyRequestH\x00\x12/\n\x07get_key\x18\x0b \x01(\x0b\x32\x1c.livekit.proto.GetKeyRequestH\x00\x42\t\n\x07message\"\xc2\x05\n\x0c\x45\x32\x65\x65Response\x12K\n\x13manager_set_enabled\x18\x01 \x01(\x0b\x32,.livekit.proto.E2eeManagerSetEnabledResponseH\x00\x12X\n\x1amanager_get_frame_cryptors\x18\x02 \x01(\x0b\x32\x32.livekit.proto.E2eeManagerGetFrameCryptorsResponseH\x00\x12L\n\x13\x63ryptor_set_enabled\x18\x03 \x01(\x0b\x32-.livekit.proto.FrameCryptorSetEnabledResponseH\x00\x12O\n\x15\x63ryptor_set_key_index\x18\x04 \x01(\x0b\x32..livekit.proto.FrameCryptorSetKeyIndexResponseH\x00\x12=\n\x0eset_shared_key\x18\x05 \x01(\x0b\x32#.livekit.proto.SetSharedKeyResponseH\x00\x12\x45\n\x12ratchet_shared_key\x18\x06 \x01(\x0b\x32\'.livekit.proto.RatchetSharedKeyResponseH\x00\x12=\n\x0eget_shared_key\x18\x07 \x01(\x0b\x32#.livekit.proto.GetSharedKeyResponseH\x00\x12\x30\n\x07set_key\x18\x08 \x01(\x0b\x32\x1d.livekit.proto.SetKeyResponseH\x00\x12\x38\n\x0bratchet_key\x18\t \x01(\x0b\x32!.livekit.proto.RatchetKeyResponseH\x00\x12\x30\n\x07get_key\x18\n \x01(\x0b\x32\x1d.livekit.proto.GetKeyResponseH\x00\x42\t\n\x07message*/\n\x0e\x45ncryptionType\x12\x08\n\x04NONE\x10\x00\x12\x07\n\x03GCM\x10\x01\x12\n\n\x06\x43USTOM\x10\x02*\x88\x01\n\x0f\x45ncryptionState\x12\x07\n\x03NEW\x10\x00\x12\x06\n\x02OK\x10\x01\x12\x15\n\x11\x45NCRYPTION_FAILED\x10\x02\x12\x15\n\x11\x44\x45\x43RYPTION_FAILED\x10\x03\x12\x0f\n\x0bMISSING_KEY\x10\x04\x12\x11\n\rKEY_RATCHETED\x10\x05\x12\x12\n\x0eINTERNAL_ERROR\x10\x06\x42\x10\xaa\x02\rLiveKit.Proto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'e2ee_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\252\002\rLiveKit.Proto'
  _globals['_ENCRYPTIONTYPE']._serialized_start=2856
  _globals['_ENCRYPTIONTYPE']._serialized_end=2903
  _globals['_ENCRYPTIONSTATE']._serialized_start=2906
  _globals['_ENCRYPTIONSTATE']._serialized_end=3042
  _globals['_FRAMECRYPTOR']._serialized_start=29
  _globals['_FRAMECRYPTOR']._serialized_end=128
  _globals['_KEYPROVIDEROPTIONS']._serialized_start=130
  _globals['_KEYPROVIDEROPTIONS']._serialized_end=248
  _globals['_E2EEOPTIONS']._serialized_start=251
  _globals['_E2EEOPTIONS']._serialized_end=385
  _globals['_E2EEMANAGERSETENABLEDREQUEST']._serialized_start=387
  _globals['_E2EEMANAGERSETENABLEDREQUEST']._serialized_end=434
  _globals['_E2EEMANAGERSETENABLEDRESPONSE']._serialized_start=436
  _globals['_E2EEMANAGERSETENABLEDRESPONSE']._serialized_end=467
  _globals['_E2EEMANAGERGETFRAMECRYPTORSREQUEST']._serialized_start=469
  _globals['_E2EEMANAGERGETFRAMECRYPTORSREQUEST']._serialized_end=505
  _globals['_E2EEMANAGERGETFRAMECRYPTORSRESPONSE']._serialized_start=507
  _globals['_E2EEMANAGERGETFRAMECRYPTORSRESPONSE']._serialized_end=597
  _globals['_FRAMECRYPTORSETENABLEDREQUEST']._serialized_start=599
  _globals['_FRAMECRYPTORSETENABLEDREQUEST']._serialized_end=696
  _globals['_FRAMECRYPTORSETENABLEDRESPONSE']._serialized_start=698
  _globals['_FRAMECRYPTORSETENABLEDRESPONSE']._serialized_end=730
  _globals['_FRAMECRYPTORSETKEYINDEXREQUEST']._serialized_start=732
  _globals['_FRAMECRYPTORSETKEYINDEXREQUEST']._serialized_end=832
  _globals['_FRAMECRYPTORSETKEYINDEXRESPONSE']._serialized_start=834
  _globals['_FRAMECRYPTORSETKEYINDEXRESPONSE']._serialized_end=867
  _globals['_SETSHAREDKEYREQUEST']._serialized_start=869
  _globals['_SETSHAREDKEYREQUEST']._serialized_end=929
  _globals['_SETSHAREDKEYRESPONSE']._serialized_start=931
  _globals['_SETSHAREDKEYRESPONSE']._serialized_end=953
  _globals['_RATCHETSHAREDKEYREQUEST']._serialized_start=955
  _globals['_RATCHETSHAREDKEYREQUEST']._serialized_end=999
  _globals['_RATCHETSHAREDKEYRESPONSE']._serialized_start=1001
  _globals['_RATCHETSHAREDKEYRESPONSE']._serialized_end=1044
  _globals['_GETSHAREDKEYREQUEST']._serialized_start=1046
  _globals['_GETSHAREDKEYREQUEST']._serialized_end=1086
  _globals['_GETSHAREDKEYRESPONSE']._serialized_start=1088
  _globals['_GETSHAREDKEYRESPONSE']._serialized_end=1123
  _globals['_SETKEYREQUEST']._serialized_start=1125
  _globals['_SETKEYREQUEST']._serialized_end=1202
  _globals['_SETKEYRESPONSE']._serialized_start=1204
  _globals['_SETKEYRESPONSE']._serialized_end=1220
  _globals['_RATCHETKEYREQUEST']._serialized_start=1222
  _globals['_RATCHETKEYREQUEST']._serialized_end=1290
  _globals['_RATCHETKEYRESPONSE']._serialized_start=1292
  _globals['_RATCHETKEYRESPONSE']._serialized_end=1329
  _globals['_GETKEYREQUEST']._serialized_start=1331
  _globals['_GETKEYREQUEST']._serialized_end=1395
  _globals['_GETKEYRESPONSE']._serialized_start=1397
  _globals['_GETKEYRESPONSE']._serialized_end=1426
  _globals['_E2EEREQUEST']._serialized_start=1429
  _globals['_E2EEREQUEST']._serialized_end=2145
  _globals['_E2EERESPONSE']._serialized_start=2148
  _globals['_E2EERESPONSE']._serialized_end=2854
# @@protoc_insertion_point(module_scope)
