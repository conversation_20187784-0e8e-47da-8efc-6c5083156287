"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from . import audio_frame_pb2
import builtins
import collections.abc
from . import e2ee_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
from . import room_pb2
from . import rpc_pb2
import sys
from . import track_pb2
from . import track_publication_pb2
import typing
from . import video_frame_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _LogLevel:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _LogLevelEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_LogLevel.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    LOG_ERROR: _LogLevel.ValueType  # 0
    LOG_WARN: _LogLevel.ValueType  # 1
    LOG_INFO: _LogLevel.ValueType  # 2
    LOG_DEBUG: _LogLevel.ValueType  # 3
    LOG_TRACE: _LogLevel.ValueType  # 4

class LogLevel(_LogLevel, metaclass=_LogLevelEnumTypeWrapper): ...

LOG_ERROR: LogLevel.ValueType  # 0
LOG_WARN: LogLevel.ValueType  # 1
LOG_INFO: LogLevel.ValueType  # 2
LOG_DEBUG: LogLevel.ValueType  # 3
LOG_TRACE: LogLevel.ValueType  # 4
global___LogLevel = LogLevel

@typing.final
class FfiRequest(google.protobuf.message.Message):
    """**How is the livekit-ffi working:
    We refer as the ffi server the Rust server that is running the LiveKit client implementation, and we
    refer as the ffi client the foreign language that commumicates with the ffi server. (e.g Python SDK, Unity SDK, etc...)

    We expose the Rust client implementation of livekit using the protocol defined here.
    Everything starts with a FfiRequest, which is a oneof message that contains all the possible
    requests that can be made to the ffi server.
    The server will then respond with a FfiResponse, which is also a oneof message that contains
    all the possible responses.
    The first request sent to the server must be an InitializeRequest, which contains the a pointer
    to the callback function that will be used to send events and async responses to the ffi client.
    (e.g participant joined, track published, etc...)

    **Useful things know when collaborating on the protocol:**
    Everything is subject to discussion and change :-)

    - The ffi client implementation must never forget to correctly dispose all the owned handles
      that it receives from the server.

    Therefore, the ffi client is easier to implement if there is less handles to manage.

    - We are mainly using FfiHandle on info messages (e.g: RoomInfo, TrackInfo, etc...)
      For this reason, info are only sent once, at creation (We're not using them for updates, we can infer them from
      events on the client implementation).
      e.g: set speaking to true when we receive a ActiveSpeakerChanged event.

    This is the input of livekit_ffi_request function
    We always expect a response (FFIResponse, even if it's empty)
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISPOSE_FIELD_NUMBER: builtins.int
    CONNECT_FIELD_NUMBER: builtins.int
    DISCONNECT_FIELD_NUMBER: builtins.int
    PUBLISH_TRACK_FIELD_NUMBER: builtins.int
    UNPUBLISH_TRACK_FIELD_NUMBER: builtins.int
    PUBLISH_DATA_FIELD_NUMBER: builtins.int
    SET_SUBSCRIBED_FIELD_NUMBER: builtins.int
    SET_LOCAL_METADATA_FIELD_NUMBER: builtins.int
    SET_LOCAL_NAME_FIELD_NUMBER: builtins.int
    SET_LOCAL_ATTRIBUTES_FIELD_NUMBER: builtins.int
    GET_SESSION_STATS_FIELD_NUMBER: builtins.int
    PUBLISH_TRANSCRIPTION_FIELD_NUMBER: builtins.int
    PUBLISH_SIP_DTMF_FIELD_NUMBER: builtins.int
    CREATE_VIDEO_TRACK_FIELD_NUMBER: builtins.int
    CREATE_AUDIO_TRACK_FIELD_NUMBER: builtins.int
    LOCAL_TRACK_MUTE_FIELD_NUMBER: builtins.int
    ENABLE_REMOTE_TRACK_FIELD_NUMBER: builtins.int
    GET_STATS_FIELD_NUMBER: builtins.int
    SET_TRACK_SUBSCRIPTION_PERMISSIONS_FIELD_NUMBER: builtins.int
    NEW_VIDEO_STREAM_FIELD_NUMBER: builtins.int
    NEW_VIDEO_SOURCE_FIELD_NUMBER: builtins.int
    CAPTURE_VIDEO_FRAME_FIELD_NUMBER: builtins.int
    VIDEO_CONVERT_FIELD_NUMBER: builtins.int
    VIDEO_STREAM_FROM_PARTICIPANT_FIELD_NUMBER: builtins.int
    NEW_AUDIO_STREAM_FIELD_NUMBER: builtins.int
    NEW_AUDIO_SOURCE_FIELD_NUMBER: builtins.int
    CAPTURE_AUDIO_FRAME_FIELD_NUMBER: builtins.int
    CLEAR_AUDIO_BUFFER_FIELD_NUMBER: builtins.int
    NEW_AUDIO_RESAMPLER_FIELD_NUMBER: builtins.int
    REMIX_AND_RESAMPLE_FIELD_NUMBER: builtins.int
    E2EE_FIELD_NUMBER: builtins.int
    AUDIO_STREAM_FROM_PARTICIPANT_FIELD_NUMBER: builtins.int
    NEW_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    PUSH_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    FLUSH_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    SEND_CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    EDIT_CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    PERFORM_RPC_FIELD_NUMBER: builtins.int
    REGISTER_RPC_METHOD_FIELD_NUMBER: builtins.int
    UNREGISTER_RPC_METHOD_FIELD_NUMBER: builtins.int
    RPC_METHOD_INVOCATION_RESPONSE_FIELD_NUMBER: builtins.int
    ENABLE_REMOTE_TRACK_PUBLICATION_FIELD_NUMBER: builtins.int
    UPDATE_REMOTE_TRACK_PUBLICATION_DIMENSION_FIELD_NUMBER: builtins.int
    SEND_STREAM_HEADER_FIELD_NUMBER: builtins.int
    SEND_STREAM_CHUNK_FIELD_NUMBER: builtins.int
    SEND_STREAM_TRAILER_FIELD_NUMBER: builtins.int
    SET_DATA_CHANNEL_BUFFERED_AMOUNT_LOW_THRESHOLD_FIELD_NUMBER: builtins.int
    LOAD_AUDIO_FILTER_PLUGIN_FIELD_NUMBER: builtins.int
    NEW_APM_FIELD_NUMBER: builtins.int
    APM_PROCESS_STREAM_FIELD_NUMBER: builtins.int
    APM_PROCESS_REVERSE_STREAM_FIELD_NUMBER: builtins.int
    @property
    def dispose(self) -> global___DisposeRequest: ...
    @property
    def connect(self) -> room_pb2.ConnectRequest:
        """Room"""

    @property
    def disconnect(self) -> room_pb2.DisconnectRequest: ...
    @property
    def publish_track(self) -> room_pb2.PublishTrackRequest: ...
    @property
    def unpublish_track(self) -> room_pb2.UnpublishTrackRequest: ...
    @property
    def publish_data(self) -> room_pb2.PublishDataRequest: ...
    @property
    def set_subscribed(self) -> room_pb2.SetSubscribedRequest: ...
    @property
    def set_local_metadata(self) -> room_pb2.SetLocalMetadataRequest: ...
    @property
    def set_local_name(self) -> room_pb2.SetLocalNameRequest: ...
    @property
    def set_local_attributes(self) -> room_pb2.SetLocalAttributesRequest: ...
    @property
    def get_session_stats(self) -> room_pb2.GetSessionStatsRequest: ...
    @property
    def publish_transcription(self) -> room_pb2.PublishTranscriptionRequest: ...
    @property
    def publish_sip_dtmf(self) -> room_pb2.PublishSipDtmfRequest: ...
    @property
    def create_video_track(self) -> track_pb2.CreateVideoTrackRequest:
        """Track"""

    @property
    def create_audio_track(self) -> track_pb2.CreateAudioTrackRequest: ...
    @property
    def local_track_mute(self) -> track_pb2.LocalTrackMuteRequest: ...
    @property
    def enable_remote_track(self) -> track_pb2.EnableRemoteTrackRequest: ...
    @property
    def get_stats(self) -> track_pb2.GetStatsRequest: ...
    @property
    def set_track_subscription_permissions(self) -> track_pb2.SetTrackSubscriptionPermissionsRequest: ...
    @property
    def new_video_stream(self) -> video_frame_pb2.NewVideoStreamRequest:
        """Video"""

    @property
    def new_video_source(self) -> video_frame_pb2.NewVideoSourceRequest: ...
    @property
    def capture_video_frame(self) -> video_frame_pb2.CaptureVideoFrameRequest: ...
    @property
    def video_convert(self) -> video_frame_pb2.VideoConvertRequest: ...
    @property
    def video_stream_from_participant(self) -> video_frame_pb2.VideoStreamFromParticipantRequest: ...
    @property
    def new_audio_stream(self) -> audio_frame_pb2.NewAudioStreamRequest:
        """Audio"""

    @property
    def new_audio_source(self) -> audio_frame_pb2.NewAudioSourceRequest: ...
    @property
    def capture_audio_frame(self) -> audio_frame_pb2.CaptureAudioFrameRequest: ...
    @property
    def clear_audio_buffer(self) -> audio_frame_pb2.ClearAudioBufferRequest: ...
    @property
    def new_audio_resampler(self) -> audio_frame_pb2.NewAudioResamplerRequest: ...
    @property
    def remix_and_resample(self) -> audio_frame_pb2.RemixAndResampleRequest: ...
    @property
    def e2ee(self) -> e2ee_pb2.E2eeRequest: ...
    @property
    def audio_stream_from_participant(self) -> audio_frame_pb2.AudioStreamFromParticipantRequest: ...
    @property
    def new_sox_resampler(self) -> audio_frame_pb2.NewSoxResamplerRequest: ...
    @property
    def push_sox_resampler(self) -> audio_frame_pb2.PushSoxResamplerRequest: ...
    @property
    def flush_sox_resampler(self) -> audio_frame_pb2.FlushSoxResamplerRequest: ...
    @property
    def send_chat_message(self) -> room_pb2.SendChatMessageRequest: ...
    @property
    def edit_chat_message(self) -> room_pb2.EditChatMessageRequest: ...
    @property
    def perform_rpc(self) -> rpc_pb2.PerformRpcRequest:
        """RPC"""

    @property
    def register_rpc_method(self) -> rpc_pb2.RegisterRpcMethodRequest: ...
    @property
    def unregister_rpc_method(self) -> rpc_pb2.UnregisterRpcMethodRequest: ...
    @property
    def rpc_method_invocation_response(self) -> rpc_pb2.RpcMethodInvocationResponseRequest: ...
    @property
    def enable_remote_track_publication(self) -> track_publication_pb2.EnableRemoteTrackPublicationRequest:
        """Track Publication"""

    @property
    def update_remote_track_publication_dimension(self) -> track_publication_pb2.UpdateRemoteTrackPublicationDimensionRequest: ...
    @property
    def send_stream_header(self) -> room_pb2.SendStreamHeaderRequest:
        """Data Streams"""

    @property
    def send_stream_chunk(self) -> room_pb2.SendStreamChunkRequest: ...
    @property
    def send_stream_trailer(self) -> room_pb2.SendStreamTrailerRequest: ...
    @property
    def set_data_channel_buffered_amount_low_threshold(self) -> room_pb2.SetDataChannelBufferedAmountLowThresholdRequest:
        """Data Channel"""

    @property
    def load_audio_filter_plugin(self) -> audio_frame_pb2.LoadAudioFilterPluginRequest:
        """Audio Filter Plugin"""

    @property
    def new_apm(self) -> audio_frame_pb2.NewApmRequest: ...
    @property
    def apm_process_stream(self) -> audio_frame_pb2.ApmProcessStreamRequest: ...
    @property
    def apm_process_reverse_stream(self) -> audio_frame_pb2.ApmProcessReverseStreamRequest: ...
    def __init__(
        self,
        *,
        dispose: global___DisposeRequest | None = ...,
        connect: room_pb2.ConnectRequest | None = ...,
        disconnect: room_pb2.DisconnectRequest | None = ...,
        publish_track: room_pb2.PublishTrackRequest | None = ...,
        unpublish_track: room_pb2.UnpublishTrackRequest | None = ...,
        publish_data: room_pb2.PublishDataRequest | None = ...,
        set_subscribed: room_pb2.SetSubscribedRequest | None = ...,
        set_local_metadata: room_pb2.SetLocalMetadataRequest | None = ...,
        set_local_name: room_pb2.SetLocalNameRequest | None = ...,
        set_local_attributes: room_pb2.SetLocalAttributesRequest | None = ...,
        get_session_stats: room_pb2.GetSessionStatsRequest | None = ...,
        publish_transcription: room_pb2.PublishTranscriptionRequest | None = ...,
        publish_sip_dtmf: room_pb2.PublishSipDtmfRequest | None = ...,
        create_video_track: track_pb2.CreateVideoTrackRequest | None = ...,
        create_audio_track: track_pb2.CreateAudioTrackRequest | None = ...,
        local_track_mute: track_pb2.LocalTrackMuteRequest | None = ...,
        enable_remote_track: track_pb2.EnableRemoteTrackRequest | None = ...,
        get_stats: track_pb2.GetStatsRequest | None = ...,
        set_track_subscription_permissions: track_pb2.SetTrackSubscriptionPermissionsRequest | None = ...,
        new_video_stream: video_frame_pb2.NewVideoStreamRequest | None = ...,
        new_video_source: video_frame_pb2.NewVideoSourceRequest | None = ...,
        capture_video_frame: video_frame_pb2.CaptureVideoFrameRequest | None = ...,
        video_convert: video_frame_pb2.VideoConvertRequest | None = ...,
        video_stream_from_participant: video_frame_pb2.VideoStreamFromParticipantRequest | None = ...,
        new_audio_stream: audio_frame_pb2.NewAudioStreamRequest | None = ...,
        new_audio_source: audio_frame_pb2.NewAudioSourceRequest | None = ...,
        capture_audio_frame: audio_frame_pb2.CaptureAudioFrameRequest | None = ...,
        clear_audio_buffer: audio_frame_pb2.ClearAudioBufferRequest | None = ...,
        new_audio_resampler: audio_frame_pb2.NewAudioResamplerRequest | None = ...,
        remix_and_resample: audio_frame_pb2.RemixAndResampleRequest | None = ...,
        e2ee: e2ee_pb2.E2eeRequest | None = ...,
        audio_stream_from_participant: audio_frame_pb2.AudioStreamFromParticipantRequest | None = ...,
        new_sox_resampler: audio_frame_pb2.NewSoxResamplerRequest | None = ...,
        push_sox_resampler: audio_frame_pb2.PushSoxResamplerRequest | None = ...,
        flush_sox_resampler: audio_frame_pb2.FlushSoxResamplerRequest | None = ...,
        send_chat_message: room_pb2.SendChatMessageRequest | None = ...,
        edit_chat_message: room_pb2.EditChatMessageRequest | None = ...,
        perform_rpc: rpc_pb2.PerformRpcRequest | None = ...,
        register_rpc_method: rpc_pb2.RegisterRpcMethodRequest | None = ...,
        unregister_rpc_method: rpc_pb2.UnregisterRpcMethodRequest | None = ...,
        rpc_method_invocation_response: rpc_pb2.RpcMethodInvocationResponseRequest | None = ...,
        enable_remote_track_publication: track_publication_pb2.EnableRemoteTrackPublicationRequest | None = ...,
        update_remote_track_publication_dimension: track_publication_pb2.UpdateRemoteTrackPublicationDimensionRequest | None = ...,
        send_stream_header: room_pb2.SendStreamHeaderRequest | None = ...,
        send_stream_chunk: room_pb2.SendStreamChunkRequest | None = ...,
        send_stream_trailer: room_pb2.SendStreamTrailerRequest | None = ...,
        set_data_channel_buffered_amount_low_threshold: room_pb2.SetDataChannelBufferedAmountLowThresholdRequest | None = ...,
        load_audio_filter_plugin: audio_frame_pb2.LoadAudioFilterPluginRequest | None = ...,
        new_apm: audio_frame_pb2.NewApmRequest | None = ...,
        apm_process_stream: audio_frame_pb2.ApmProcessStreamRequest | None = ...,
        apm_process_reverse_stream: audio_frame_pb2.ApmProcessReverseStreamRequest | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["apm_process_reverse_stream", b"apm_process_reverse_stream", "apm_process_stream", b"apm_process_stream", "audio_stream_from_participant", b"audio_stream_from_participant", "capture_audio_frame", b"capture_audio_frame", "capture_video_frame", b"capture_video_frame", "clear_audio_buffer", b"clear_audio_buffer", "connect", b"connect", "create_audio_track", b"create_audio_track", "create_video_track", b"create_video_track", "disconnect", b"disconnect", "dispose", b"dispose", "e2ee", b"e2ee", "edit_chat_message", b"edit_chat_message", "enable_remote_track", b"enable_remote_track", "enable_remote_track_publication", b"enable_remote_track_publication", "flush_sox_resampler", b"flush_sox_resampler", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "load_audio_filter_plugin", b"load_audio_filter_plugin", "local_track_mute", b"local_track_mute", "message", b"message", "new_apm", b"new_apm", "new_audio_resampler", b"new_audio_resampler", "new_audio_source", b"new_audio_source", "new_audio_stream", b"new_audio_stream", "new_sox_resampler", b"new_sox_resampler", "new_video_source", b"new_video_source", "new_video_stream", b"new_video_stream", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "push_sox_resampler", b"push_sox_resampler", "register_rpc_method", b"register_rpc_method", "remix_and_resample", b"remix_and_resample", "rpc_method_invocation_response", b"rpc_method_invocation_response", "send_chat_message", b"send_chat_message", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", b"set_data_channel_buffered_amount_low_threshold", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "set_subscribed", b"set_subscribed", "set_track_subscription_permissions", b"set_track_subscription_permissions", "unpublish_track", b"unpublish_track", "unregister_rpc_method", b"unregister_rpc_method", "update_remote_track_publication_dimension", b"update_remote_track_publication_dimension", "video_convert", b"video_convert", "video_stream_from_participant", b"video_stream_from_participant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["apm_process_reverse_stream", b"apm_process_reverse_stream", "apm_process_stream", b"apm_process_stream", "audio_stream_from_participant", b"audio_stream_from_participant", "capture_audio_frame", b"capture_audio_frame", "capture_video_frame", b"capture_video_frame", "clear_audio_buffer", b"clear_audio_buffer", "connect", b"connect", "create_audio_track", b"create_audio_track", "create_video_track", b"create_video_track", "disconnect", b"disconnect", "dispose", b"dispose", "e2ee", b"e2ee", "edit_chat_message", b"edit_chat_message", "enable_remote_track", b"enable_remote_track", "enable_remote_track_publication", b"enable_remote_track_publication", "flush_sox_resampler", b"flush_sox_resampler", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "load_audio_filter_plugin", b"load_audio_filter_plugin", "local_track_mute", b"local_track_mute", "message", b"message", "new_apm", b"new_apm", "new_audio_resampler", b"new_audio_resampler", "new_audio_source", b"new_audio_source", "new_audio_stream", b"new_audio_stream", "new_sox_resampler", b"new_sox_resampler", "new_video_source", b"new_video_source", "new_video_stream", b"new_video_stream", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "push_sox_resampler", b"push_sox_resampler", "register_rpc_method", b"register_rpc_method", "remix_and_resample", b"remix_and_resample", "rpc_method_invocation_response", b"rpc_method_invocation_response", "send_chat_message", b"send_chat_message", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", b"set_data_channel_buffered_amount_low_threshold", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "set_subscribed", b"set_subscribed", "set_track_subscription_permissions", b"set_track_subscription_permissions", "unpublish_track", b"unpublish_track", "unregister_rpc_method", b"unregister_rpc_method", "update_remote_track_publication_dimension", b"update_remote_track_publication_dimension", "video_convert", b"video_convert", "video_stream_from_participant", b"video_stream_from_participant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["dispose", "connect", "disconnect", "publish_track", "unpublish_track", "publish_data", "set_subscribed", "set_local_metadata", "set_local_name", "set_local_attributes", "get_session_stats", "publish_transcription", "publish_sip_dtmf", "create_video_track", "create_audio_track", "local_track_mute", "enable_remote_track", "get_stats", "set_track_subscription_permissions", "new_video_stream", "new_video_source", "capture_video_frame", "video_convert", "video_stream_from_participant", "new_audio_stream", "new_audio_source", "capture_audio_frame", "clear_audio_buffer", "new_audio_resampler", "remix_and_resample", "e2ee", "audio_stream_from_participant", "new_sox_resampler", "push_sox_resampler", "flush_sox_resampler", "send_chat_message", "edit_chat_message", "perform_rpc", "register_rpc_method", "unregister_rpc_method", "rpc_method_invocation_response", "enable_remote_track_publication", "update_remote_track_publication_dimension", "send_stream_header", "send_stream_chunk", "send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", "load_audio_filter_plugin", "new_apm", "apm_process_stream", "apm_process_reverse_stream"] | None: ...

global___FfiRequest = FfiRequest

@typing.final
class FfiResponse(google.protobuf.message.Message):
    """This is the output of livekit_ffi_request function."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISPOSE_FIELD_NUMBER: builtins.int
    CONNECT_FIELD_NUMBER: builtins.int
    DISCONNECT_FIELD_NUMBER: builtins.int
    PUBLISH_TRACK_FIELD_NUMBER: builtins.int
    UNPUBLISH_TRACK_FIELD_NUMBER: builtins.int
    PUBLISH_DATA_FIELD_NUMBER: builtins.int
    SET_SUBSCRIBED_FIELD_NUMBER: builtins.int
    SET_LOCAL_METADATA_FIELD_NUMBER: builtins.int
    SET_LOCAL_NAME_FIELD_NUMBER: builtins.int
    SET_LOCAL_ATTRIBUTES_FIELD_NUMBER: builtins.int
    GET_SESSION_STATS_FIELD_NUMBER: builtins.int
    PUBLISH_TRANSCRIPTION_FIELD_NUMBER: builtins.int
    PUBLISH_SIP_DTMF_FIELD_NUMBER: builtins.int
    CREATE_VIDEO_TRACK_FIELD_NUMBER: builtins.int
    CREATE_AUDIO_TRACK_FIELD_NUMBER: builtins.int
    LOCAL_TRACK_MUTE_FIELD_NUMBER: builtins.int
    ENABLE_REMOTE_TRACK_FIELD_NUMBER: builtins.int
    GET_STATS_FIELD_NUMBER: builtins.int
    SET_TRACK_SUBSCRIPTION_PERMISSIONS_FIELD_NUMBER: builtins.int
    NEW_VIDEO_STREAM_FIELD_NUMBER: builtins.int
    NEW_VIDEO_SOURCE_FIELD_NUMBER: builtins.int
    CAPTURE_VIDEO_FRAME_FIELD_NUMBER: builtins.int
    VIDEO_CONVERT_FIELD_NUMBER: builtins.int
    VIDEO_STREAM_FROM_PARTICIPANT_FIELD_NUMBER: builtins.int
    NEW_AUDIO_STREAM_FIELD_NUMBER: builtins.int
    NEW_AUDIO_SOURCE_FIELD_NUMBER: builtins.int
    CAPTURE_AUDIO_FRAME_FIELD_NUMBER: builtins.int
    CLEAR_AUDIO_BUFFER_FIELD_NUMBER: builtins.int
    NEW_AUDIO_RESAMPLER_FIELD_NUMBER: builtins.int
    REMIX_AND_RESAMPLE_FIELD_NUMBER: builtins.int
    AUDIO_STREAM_FROM_PARTICIPANT_FIELD_NUMBER: builtins.int
    E2EE_FIELD_NUMBER: builtins.int
    NEW_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    PUSH_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    FLUSH_SOX_RESAMPLER_FIELD_NUMBER: builtins.int
    SEND_CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    PERFORM_RPC_FIELD_NUMBER: builtins.int
    REGISTER_RPC_METHOD_FIELD_NUMBER: builtins.int
    UNREGISTER_RPC_METHOD_FIELD_NUMBER: builtins.int
    RPC_METHOD_INVOCATION_RESPONSE_FIELD_NUMBER: builtins.int
    ENABLE_REMOTE_TRACK_PUBLICATION_FIELD_NUMBER: builtins.int
    UPDATE_REMOTE_TRACK_PUBLICATION_DIMENSION_FIELD_NUMBER: builtins.int
    SEND_STREAM_HEADER_FIELD_NUMBER: builtins.int
    SEND_STREAM_CHUNK_FIELD_NUMBER: builtins.int
    SEND_STREAM_TRAILER_FIELD_NUMBER: builtins.int
    SET_DATA_CHANNEL_BUFFERED_AMOUNT_LOW_THRESHOLD_FIELD_NUMBER: builtins.int
    LOAD_AUDIO_FILTER_PLUGIN_FIELD_NUMBER: builtins.int
    NEW_APM_FIELD_NUMBER: builtins.int
    APM_PROCESS_STREAM_FIELD_NUMBER: builtins.int
    APM_PROCESS_REVERSE_STREAM_FIELD_NUMBER: builtins.int
    @property
    def dispose(self) -> global___DisposeResponse: ...
    @property
    def connect(self) -> room_pb2.ConnectResponse:
        """Room"""

    @property
    def disconnect(self) -> room_pb2.DisconnectResponse: ...
    @property
    def publish_track(self) -> room_pb2.PublishTrackResponse: ...
    @property
    def unpublish_track(self) -> room_pb2.UnpublishTrackResponse: ...
    @property
    def publish_data(self) -> room_pb2.PublishDataResponse: ...
    @property
    def set_subscribed(self) -> room_pb2.SetSubscribedResponse: ...
    @property
    def set_local_metadata(self) -> room_pb2.SetLocalMetadataResponse: ...
    @property
    def set_local_name(self) -> room_pb2.SetLocalNameResponse: ...
    @property
    def set_local_attributes(self) -> room_pb2.SetLocalAttributesResponse: ...
    @property
    def get_session_stats(self) -> room_pb2.GetSessionStatsResponse: ...
    @property
    def publish_transcription(self) -> room_pb2.PublishTranscriptionResponse: ...
    @property
    def publish_sip_dtmf(self) -> room_pb2.PublishSipDtmfResponse: ...
    @property
    def create_video_track(self) -> track_pb2.CreateVideoTrackResponse:
        """Track"""

    @property
    def create_audio_track(self) -> track_pb2.CreateAudioTrackResponse: ...
    @property
    def local_track_mute(self) -> track_pb2.LocalTrackMuteResponse: ...
    @property
    def enable_remote_track(self) -> track_pb2.EnableRemoteTrackResponse: ...
    @property
    def get_stats(self) -> track_pb2.GetStatsResponse: ...
    @property
    def set_track_subscription_permissions(self) -> track_pb2.SetTrackSubscriptionPermissionsResponse: ...
    @property
    def new_video_stream(self) -> video_frame_pb2.NewVideoStreamResponse:
        """Video"""

    @property
    def new_video_source(self) -> video_frame_pb2.NewVideoSourceResponse: ...
    @property
    def capture_video_frame(self) -> video_frame_pb2.CaptureVideoFrameResponse: ...
    @property
    def video_convert(self) -> video_frame_pb2.VideoConvertResponse: ...
    @property
    def video_stream_from_participant(self) -> video_frame_pb2.VideoStreamFromParticipantResponse: ...
    @property
    def new_audio_stream(self) -> audio_frame_pb2.NewAudioStreamResponse:
        """Audio"""

    @property
    def new_audio_source(self) -> audio_frame_pb2.NewAudioSourceResponse: ...
    @property
    def capture_audio_frame(self) -> audio_frame_pb2.CaptureAudioFrameResponse: ...
    @property
    def clear_audio_buffer(self) -> audio_frame_pb2.ClearAudioBufferResponse: ...
    @property
    def new_audio_resampler(self) -> audio_frame_pb2.NewAudioResamplerResponse: ...
    @property
    def remix_and_resample(self) -> audio_frame_pb2.RemixAndResampleResponse: ...
    @property
    def audio_stream_from_participant(self) -> audio_frame_pb2.AudioStreamFromParticipantResponse: ...
    @property
    def e2ee(self) -> e2ee_pb2.E2eeResponse: ...
    @property
    def new_sox_resampler(self) -> audio_frame_pb2.NewSoxResamplerResponse: ...
    @property
    def push_sox_resampler(self) -> audio_frame_pb2.PushSoxResamplerResponse: ...
    @property
    def flush_sox_resampler(self) -> audio_frame_pb2.FlushSoxResamplerResponse: ...
    @property
    def send_chat_message(self) -> room_pb2.SendChatMessageResponse: ...
    @property
    def perform_rpc(self) -> rpc_pb2.PerformRpcResponse:
        """RPC"""

    @property
    def register_rpc_method(self) -> rpc_pb2.RegisterRpcMethodResponse: ...
    @property
    def unregister_rpc_method(self) -> rpc_pb2.UnregisterRpcMethodResponse: ...
    @property
    def rpc_method_invocation_response(self) -> rpc_pb2.RpcMethodInvocationResponseResponse: ...
    @property
    def enable_remote_track_publication(self) -> track_publication_pb2.EnableRemoteTrackPublicationResponse:
        """Track Publication"""

    @property
    def update_remote_track_publication_dimension(self) -> track_publication_pb2.UpdateRemoteTrackPublicationDimensionResponse: ...
    @property
    def send_stream_header(self) -> room_pb2.SendStreamHeaderResponse:
        """Data Streams"""

    @property
    def send_stream_chunk(self) -> room_pb2.SendStreamChunkResponse: ...
    @property
    def send_stream_trailer(self) -> room_pb2.SendStreamTrailerResponse: ...
    @property
    def set_data_channel_buffered_amount_low_threshold(self) -> room_pb2.SetDataChannelBufferedAmountLowThresholdResponse:
        """Data Channel"""

    @property
    def load_audio_filter_plugin(self) -> audio_frame_pb2.LoadAudioFilterPluginResponse:
        """Audio Filter Plugin"""

    @property
    def new_apm(self) -> audio_frame_pb2.NewApmResponse: ...
    @property
    def apm_process_stream(self) -> audio_frame_pb2.ApmProcessStreamResponse: ...
    @property
    def apm_process_reverse_stream(self) -> audio_frame_pb2.ApmProcessReverseStreamResponse: ...
    def __init__(
        self,
        *,
        dispose: global___DisposeResponse | None = ...,
        connect: room_pb2.ConnectResponse | None = ...,
        disconnect: room_pb2.DisconnectResponse | None = ...,
        publish_track: room_pb2.PublishTrackResponse | None = ...,
        unpublish_track: room_pb2.UnpublishTrackResponse | None = ...,
        publish_data: room_pb2.PublishDataResponse | None = ...,
        set_subscribed: room_pb2.SetSubscribedResponse | None = ...,
        set_local_metadata: room_pb2.SetLocalMetadataResponse | None = ...,
        set_local_name: room_pb2.SetLocalNameResponse | None = ...,
        set_local_attributes: room_pb2.SetLocalAttributesResponse | None = ...,
        get_session_stats: room_pb2.GetSessionStatsResponse | None = ...,
        publish_transcription: room_pb2.PublishTranscriptionResponse | None = ...,
        publish_sip_dtmf: room_pb2.PublishSipDtmfResponse | None = ...,
        create_video_track: track_pb2.CreateVideoTrackResponse | None = ...,
        create_audio_track: track_pb2.CreateAudioTrackResponse | None = ...,
        local_track_mute: track_pb2.LocalTrackMuteResponse | None = ...,
        enable_remote_track: track_pb2.EnableRemoteTrackResponse | None = ...,
        get_stats: track_pb2.GetStatsResponse | None = ...,
        set_track_subscription_permissions: track_pb2.SetTrackSubscriptionPermissionsResponse | None = ...,
        new_video_stream: video_frame_pb2.NewVideoStreamResponse | None = ...,
        new_video_source: video_frame_pb2.NewVideoSourceResponse | None = ...,
        capture_video_frame: video_frame_pb2.CaptureVideoFrameResponse | None = ...,
        video_convert: video_frame_pb2.VideoConvertResponse | None = ...,
        video_stream_from_participant: video_frame_pb2.VideoStreamFromParticipantResponse | None = ...,
        new_audio_stream: audio_frame_pb2.NewAudioStreamResponse | None = ...,
        new_audio_source: audio_frame_pb2.NewAudioSourceResponse | None = ...,
        capture_audio_frame: audio_frame_pb2.CaptureAudioFrameResponse | None = ...,
        clear_audio_buffer: audio_frame_pb2.ClearAudioBufferResponse | None = ...,
        new_audio_resampler: audio_frame_pb2.NewAudioResamplerResponse | None = ...,
        remix_and_resample: audio_frame_pb2.RemixAndResampleResponse | None = ...,
        audio_stream_from_participant: audio_frame_pb2.AudioStreamFromParticipantResponse | None = ...,
        e2ee: e2ee_pb2.E2eeResponse | None = ...,
        new_sox_resampler: audio_frame_pb2.NewSoxResamplerResponse | None = ...,
        push_sox_resampler: audio_frame_pb2.PushSoxResamplerResponse | None = ...,
        flush_sox_resampler: audio_frame_pb2.FlushSoxResamplerResponse | None = ...,
        send_chat_message: room_pb2.SendChatMessageResponse | None = ...,
        perform_rpc: rpc_pb2.PerformRpcResponse | None = ...,
        register_rpc_method: rpc_pb2.RegisterRpcMethodResponse | None = ...,
        unregister_rpc_method: rpc_pb2.UnregisterRpcMethodResponse | None = ...,
        rpc_method_invocation_response: rpc_pb2.RpcMethodInvocationResponseResponse | None = ...,
        enable_remote_track_publication: track_publication_pb2.EnableRemoteTrackPublicationResponse | None = ...,
        update_remote_track_publication_dimension: track_publication_pb2.UpdateRemoteTrackPublicationDimensionResponse | None = ...,
        send_stream_header: room_pb2.SendStreamHeaderResponse | None = ...,
        send_stream_chunk: room_pb2.SendStreamChunkResponse | None = ...,
        send_stream_trailer: room_pb2.SendStreamTrailerResponse | None = ...,
        set_data_channel_buffered_amount_low_threshold: room_pb2.SetDataChannelBufferedAmountLowThresholdResponse | None = ...,
        load_audio_filter_plugin: audio_frame_pb2.LoadAudioFilterPluginResponse | None = ...,
        new_apm: audio_frame_pb2.NewApmResponse | None = ...,
        apm_process_stream: audio_frame_pb2.ApmProcessStreamResponse | None = ...,
        apm_process_reverse_stream: audio_frame_pb2.ApmProcessReverseStreamResponse | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["apm_process_reverse_stream", b"apm_process_reverse_stream", "apm_process_stream", b"apm_process_stream", "audio_stream_from_participant", b"audio_stream_from_participant", "capture_audio_frame", b"capture_audio_frame", "capture_video_frame", b"capture_video_frame", "clear_audio_buffer", b"clear_audio_buffer", "connect", b"connect", "create_audio_track", b"create_audio_track", "create_video_track", b"create_video_track", "disconnect", b"disconnect", "dispose", b"dispose", "e2ee", b"e2ee", "enable_remote_track", b"enable_remote_track", "enable_remote_track_publication", b"enable_remote_track_publication", "flush_sox_resampler", b"flush_sox_resampler", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "load_audio_filter_plugin", b"load_audio_filter_plugin", "local_track_mute", b"local_track_mute", "message", b"message", "new_apm", b"new_apm", "new_audio_resampler", b"new_audio_resampler", "new_audio_source", b"new_audio_source", "new_audio_stream", b"new_audio_stream", "new_sox_resampler", b"new_sox_resampler", "new_video_source", b"new_video_source", "new_video_stream", b"new_video_stream", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "push_sox_resampler", b"push_sox_resampler", "register_rpc_method", b"register_rpc_method", "remix_and_resample", b"remix_and_resample", "rpc_method_invocation_response", b"rpc_method_invocation_response", "send_chat_message", b"send_chat_message", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", b"set_data_channel_buffered_amount_low_threshold", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "set_subscribed", b"set_subscribed", "set_track_subscription_permissions", b"set_track_subscription_permissions", "unpublish_track", b"unpublish_track", "unregister_rpc_method", b"unregister_rpc_method", "update_remote_track_publication_dimension", b"update_remote_track_publication_dimension", "video_convert", b"video_convert", "video_stream_from_participant", b"video_stream_from_participant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["apm_process_reverse_stream", b"apm_process_reverse_stream", "apm_process_stream", b"apm_process_stream", "audio_stream_from_participant", b"audio_stream_from_participant", "capture_audio_frame", b"capture_audio_frame", "capture_video_frame", b"capture_video_frame", "clear_audio_buffer", b"clear_audio_buffer", "connect", b"connect", "create_audio_track", b"create_audio_track", "create_video_track", b"create_video_track", "disconnect", b"disconnect", "dispose", b"dispose", "e2ee", b"e2ee", "enable_remote_track", b"enable_remote_track", "enable_remote_track_publication", b"enable_remote_track_publication", "flush_sox_resampler", b"flush_sox_resampler", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "load_audio_filter_plugin", b"load_audio_filter_plugin", "local_track_mute", b"local_track_mute", "message", b"message", "new_apm", b"new_apm", "new_audio_resampler", b"new_audio_resampler", "new_audio_source", b"new_audio_source", "new_audio_stream", b"new_audio_stream", "new_sox_resampler", b"new_sox_resampler", "new_video_source", b"new_video_source", "new_video_stream", b"new_video_stream", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "push_sox_resampler", b"push_sox_resampler", "register_rpc_method", b"register_rpc_method", "remix_and_resample", b"remix_and_resample", "rpc_method_invocation_response", b"rpc_method_invocation_response", "send_chat_message", b"send_chat_message", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", b"set_data_channel_buffered_amount_low_threshold", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "set_subscribed", b"set_subscribed", "set_track_subscription_permissions", b"set_track_subscription_permissions", "unpublish_track", b"unpublish_track", "unregister_rpc_method", b"unregister_rpc_method", "update_remote_track_publication_dimension", b"update_remote_track_publication_dimension", "video_convert", b"video_convert", "video_stream_from_participant", b"video_stream_from_participant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["dispose", "connect", "disconnect", "publish_track", "unpublish_track", "publish_data", "set_subscribed", "set_local_metadata", "set_local_name", "set_local_attributes", "get_session_stats", "publish_transcription", "publish_sip_dtmf", "create_video_track", "create_audio_track", "local_track_mute", "enable_remote_track", "get_stats", "set_track_subscription_permissions", "new_video_stream", "new_video_source", "capture_video_frame", "video_convert", "video_stream_from_participant", "new_audio_stream", "new_audio_source", "capture_audio_frame", "clear_audio_buffer", "new_audio_resampler", "remix_and_resample", "audio_stream_from_participant", "e2ee", "new_sox_resampler", "push_sox_resampler", "flush_sox_resampler", "send_chat_message", "perform_rpc", "register_rpc_method", "unregister_rpc_method", "rpc_method_invocation_response", "enable_remote_track_publication", "update_remote_track_publication_dimension", "send_stream_header", "send_stream_chunk", "send_stream_trailer", "set_data_channel_buffered_amount_low_threshold", "load_audio_filter_plugin", "new_apm", "apm_process_stream", "apm_process_reverse_stream"] | None: ...

global___FfiResponse = FfiResponse

@typing.final
class FfiEvent(google.protobuf.message.Message):
    """To minimize complexity, participant events are not included in the protocol.
    It is easily deducible from the room events and it turned out that is is easier to implement
    on the ffi client side.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROOM_EVENT_FIELD_NUMBER: builtins.int
    TRACK_EVENT_FIELD_NUMBER: builtins.int
    VIDEO_STREAM_EVENT_FIELD_NUMBER: builtins.int
    AUDIO_STREAM_EVENT_FIELD_NUMBER: builtins.int
    CONNECT_FIELD_NUMBER: builtins.int
    DISCONNECT_FIELD_NUMBER: builtins.int
    DISPOSE_FIELD_NUMBER: builtins.int
    PUBLISH_TRACK_FIELD_NUMBER: builtins.int
    UNPUBLISH_TRACK_FIELD_NUMBER: builtins.int
    PUBLISH_DATA_FIELD_NUMBER: builtins.int
    PUBLISH_TRANSCRIPTION_FIELD_NUMBER: builtins.int
    CAPTURE_AUDIO_FRAME_FIELD_NUMBER: builtins.int
    SET_LOCAL_METADATA_FIELD_NUMBER: builtins.int
    SET_LOCAL_NAME_FIELD_NUMBER: builtins.int
    SET_LOCAL_ATTRIBUTES_FIELD_NUMBER: builtins.int
    GET_STATS_FIELD_NUMBER: builtins.int
    LOGS_FIELD_NUMBER: builtins.int
    GET_SESSION_STATS_FIELD_NUMBER: builtins.int
    PANIC_FIELD_NUMBER: builtins.int
    PUBLISH_SIP_DTMF_FIELD_NUMBER: builtins.int
    CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    PERFORM_RPC_FIELD_NUMBER: builtins.int
    RPC_METHOD_INVOCATION_FIELD_NUMBER: builtins.int
    SEND_STREAM_HEADER_FIELD_NUMBER: builtins.int
    SEND_STREAM_CHUNK_FIELD_NUMBER: builtins.int
    SEND_STREAM_TRAILER_FIELD_NUMBER: builtins.int
    @property
    def room_event(self) -> room_pb2.RoomEvent: ...
    @property
    def track_event(self) -> track_pb2.TrackEvent: ...
    @property
    def video_stream_event(self) -> video_frame_pb2.VideoStreamEvent: ...
    @property
    def audio_stream_event(self) -> audio_frame_pb2.AudioStreamEvent: ...
    @property
    def connect(self) -> room_pb2.ConnectCallback: ...
    @property
    def disconnect(self) -> room_pb2.DisconnectCallback: ...
    @property
    def dispose(self) -> global___DisposeCallback: ...
    @property
    def publish_track(self) -> room_pb2.PublishTrackCallback: ...
    @property
    def unpublish_track(self) -> room_pb2.UnpublishTrackCallback: ...
    @property
    def publish_data(self) -> room_pb2.PublishDataCallback: ...
    @property
    def publish_transcription(self) -> room_pb2.PublishTranscriptionCallback: ...
    @property
    def capture_audio_frame(self) -> audio_frame_pb2.CaptureAudioFrameCallback: ...
    @property
    def set_local_metadata(self) -> room_pb2.SetLocalMetadataCallback: ...
    @property
    def set_local_name(self) -> room_pb2.SetLocalNameCallback: ...
    @property
    def set_local_attributes(self) -> room_pb2.SetLocalAttributesCallback: ...
    @property
    def get_stats(self) -> track_pb2.GetStatsCallback: ...
    @property
    def logs(self) -> global___LogBatch: ...
    @property
    def get_session_stats(self) -> room_pb2.GetSessionStatsCallback: ...
    @property
    def panic(self) -> global___Panic: ...
    @property
    def publish_sip_dtmf(self) -> room_pb2.PublishSipDtmfCallback: ...
    @property
    def chat_message(self) -> room_pb2.SendChatMessageCallback: ...
    @property
    def perform_rpc(self) -> rpc_pb2.PerformRpcCallback: ...
    @property
    def rpc_method_invocation(self) -> rpc_pb2.RpcMethodInvocationEvent: ...
    @property
    def send_stream_header(self) -> room_pb2.SendStreamHeaderCallback: ...
    @property
    def send_stream_chunk(self) -> room_pb2.SendStreamChunkCallback: ...
    @property
    def send_stream_trailer(self) -> room_pb2.SendStreamTrailerCallback: ...
    def __init__(
        self,
        *,
        room_event: room_pb2.RoomEvent | None = ...,
        track_event: track_pb2.TrackEvent | None = ...,
        video_stream_event: video_frame_pb2.VideoStreamEvent | None = ...,
        audio_stream_event: audio_frame_pb2.AudioStreamEvent | None = ...,
        connect: room_pb2.ConnectCallback | None = ...,
        disconnect: room_pb2.DisconnectCallback | None = ...,
        dispose: global___DisposeCallback | None = ...,
        publish_track: room_pb2.PublishTrackCallback | None = ...,
        unpublish_track: room_pb2.UnpublishTrackCallback | None = ...,
        publish_data: room_pb2.PublishDataCallback | None = ...,
        publish_transcription: room_pb2.PublishTranscriptionCallback | None = ...,
        capture_audio_frame: audio_frame_pb2.CaptureAudioFrameCallback | None = ...,
        set_local_metadata: room_pb2.SetLocalMetadataCallback | None = ...,
        set_local_name: room_pb2.SetLocalNameCallback | None = ...,
        set_local_attributes: room_pb2.SetLocalAttributesCallback | None = ...,
        get_stats: track_pb2.GetStatsCallback | None = ...,
        logs: global___LogBatch | None = ...,
        get_session_stats: room_pb2.GetSessionStatsCallback | None = ...,
        panic: global___Panic | None = ...,
        publish_sip_dtmf: room_pb2.PublishSipDtmfCallback | None = ...,
        chat_message: room_pb2.SendChatMessageCallback | None = ...,
        perform_rpc: rpc_pb2.PerformRpcCallback | None = ...,
        rpc_method_invocation: rpc_pb2.RpcMethodInvocationEvent | None = ...,
        send_stream_header: room_pb2.SendStreamHeaderCallback | None = ...,
        send_stream_chunk: room_pb2.SendStreamChunkCallback | None = ...,
        send_stream_trailer: room_pb2.SendStreamTrailerCallback | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_stream_event", b"audio_stream_event", "capture_audio_frame", b"capture_audio_frame", "chat_message", b"chat_message", "connect", b"connect", "disconnect", b"disconnect", "dispose", b"dispose", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "logs", b"logs", "message", b"message", "panic", b"panic", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "room_event", b"room_event", "rpc_method_invocation", b"rpc_method_invocation", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "track_event", b"track_event", "unpublish_track", b"unpublish_track", "video_stream_event", b"video_stream_event"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_stream_event", b"audio_stream_event", "capture_audio_frame", b"capture_audio_frame", "chat_message", b"chat_message", "connect", b"connect", "disconnect", b"disconnect", "dispose", b"dispose", "get_session_stats", b"get_session_stats", "get_stats", b"get_stats", "logs", b"logs", "message", b"message", "panic", b"panic", "perform_rpc", b"perform_rpc", "publish_data", b"publish_data", "publish_sip_dtmf", b"publish_sip_dtmf", "publish_track", b"publish_track", "publish_transcription", b"publish_transcription", "room_event", b"room_event", "rpc_method_invocation", b"rpc_method_invocation", "send_stream_chunk", b"send_stream_chunk", "send_stream_header", b"send_stream_header", "send_stream_trailer", b"send_stream_trailer", "set_local_attributes", b"set_local_attributes", "set_local_metadata", b"set_local_metadata", "set_local_name", b"set_local_name", "track_event", b"track_event", "unpublish_track", b"unpublish_track", "video_stream_event", b"video_stream_event"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["room_event", "track_event", "video_stream_event", "audio_stream_event", "connect", "disconnect", "dispose", "publish_track", "unpublish_track", "publish_data", "publish_transcription", "capture_audio_frame", "set_local_metadata", "set_local_name", "set_local_attributes", "get_stats", "logs", "get_session_stats", "panic", "publish_sip_dtmf", "chat_message", "perform_rpc", "rpc_method_invocation", "send_stream_header", "send_stream_chunk", "send_stream_trailer"] | None: ...

global___FfiEvent = FfiEvent

@typing.final
class DisposeRequest(google.protobuf.message.Message):
    """Stop all rooms synchronously (Do we need async here?).
    e.g: This is used for the Unity Editor after each assemblies reload.
    TODO(theomonnom): Implement a debug mode where we can find all leaked handles?
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_FIELD_NUMBER: builtins.int
    def __init__(
        self,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async", b"async"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async", b"async"]) -> None: ...

global___DisposeRequest = DisposeRequest

@typing.final
class DisposeResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    """None if sync"""
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___DisposeResponse = DisposeResponse

@typing.final
class DisposeCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___DisposeCallback = DisposeCallback

@typing.final
class LogRecord(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LEVEL_FIELD_NUMBER: builtins.int
    TARGET_FIELD_NUMBER: builtins.int
    MODULE_PATH_FIELD_NUMBER: builtins.int
    FILE_FIELD_NUMBER: builtins.int
    LINE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    level: global___LogLevel.ValueType
    target: builtins.str
    """e.g "livekit", "libwebrtc", "tokio-tungstenite", etc..."""
    module_path: builtins.str
    file: builtins.str
    line: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        level: global___LogLevel.ValueType | None = ...,
        target: builtins.str | None = ...,
        module_path: builtins.str | None = ...,
        file: builtins.str | None = ...,
        line: builtins.int | None = ...,
        message: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["file", b"file", "level", b"level", "line", b"line", "message", b"message", "module_path", b"module_path", "target", b"target"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["file", b"file", "level", b"level", "line", b"line", "message", b"message", "module_path", b"module_path", "target", b"target"]) -> None: ...

global___LogRecord = LogRecord

@typing.final
class LogBatch(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RECORDS_FIELD_NUMBER: builtins.int
    @property
    def records(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LogRecord]: ...
    def __init__(
        self,
        *,
        records: collections.abc.Iterable[global___LogRecord] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["records", b"records"]) -> None: ...

global___LogBatch = LogBatch

@typing.final
class Panic(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    message: builtins.str
    def __init__(
        self,
        *,
        message: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["message", b"message"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["message", b"message"]) -> None: ...

global___Panic = Panic
