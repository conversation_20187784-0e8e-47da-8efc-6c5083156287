"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class RpcError(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CODE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    code: builtins.int
    message: builtins.str
    data: builtins.str
    def __init__(
        self,
        *,
        code: builtins.int | None = ...,
        message: builtins.str | None = ...,
        data: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["code", b"code", "data", b"data", "message", b"message"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["code", b"code", "data", b"data", "message", b"message"]) -> None: ...

global___RpcError = RpcError

@typing.final
class PerformRpcRequest(google.protobuf.message.Message):
    """FFI Requests"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITY_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    RESPONSE_TIMEOUT_MS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    destination_identity: builtins.str
    method: builtins.str
    payload: builtins.str
    response_timeout_ms: builtins.int
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        destination_identity: builtins.str | None = ...,
        method: builtins.str | None = ...,
        payload: builtins.str | None = ...,
        response_timeout_ms: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["destination_identity", b"destination_identity", "local_participant_handle", b"local_participant_handle", "method", b"method", "payload", b"payload", "response_timeout_ms", b"response_timeout_ms"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["destination_identity", b"destination_identity", "local_participant_handle", b"local_participant_handle", "method", b"method", "payload", b"payload", "response_timeout_ms", b"response_timeout_ms"]) -> None: ...

global___PerformRpcRequest = PerformRpcRequest

@typing.final
class RegisterRpcMethodRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    method: builtins.str
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        method: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "method", b"method"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "method", b"method"]) -> None: ...

global___RegisterRpcMethodRequest = RegisterRpcMethodRequest

@typing.final
class UnregisterRpcMethodRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    method: builtins.str
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        method: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "method", b"method"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "method", b"method"]) -> None: ...

global___UnregisterRpcMethodRequest = UnregisterRpcMethodRequest

@typing.final
class RpcMethodInvocationResponseRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    INVOCATION_ID_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    invocation_id: builtins.int
    payload: builtins.str
    @property
    def error(self) -> global___RpcError: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        invocation_id: builtins.int | None = ...,
        payload: builtins.str | None = ...,
        error: global___RpcError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "invocation_id", b"invocation_id", "local_participant_handle", b"local_participant_handle", "payload", b"payload"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "invocation_id", b"invocation_id", "local_participant_handle", b"local_participant_handle", "payload", b"payload"]) -> None: ...

global___RpcMethodInvocationResponseRequest = RpcMethodInvocationResponseRequest

@typing.final
class PerformRpcResponse(google.protobuf.message.Message):
    """FFI Responses"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___PerformRpcResponse = PerformRpcResponse

@typing.final
class RegisterRpcMethodResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___RegisterRpcMethodResponse = RegisterRpcMethodResponse

@typing.final
class UnregisterRpcMethodResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___UnregisterRpcMethodResponse = UnregisterRpcMethodResponse

@typing.final
class RpcMethodInvocationResponseResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___RpcMethodInvocationResponseResponse = RpcMethodInvocationResponseResponse

@typing.final
class PerformRpcCallback(google.protobuf.message.Message):
    """FFI Callbacks"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    payload: builtins.str
    @property
    def error(self) -> global___RpcError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        payload: builtins.str | None = ...,
        error: global___RpcError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "payload", b"payload"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "payload", b"payload"]) -> None: ...

global___PerformRpcCallback = PerformRpcCallback

@typing.final
class RpcMethodInvocationEvent(google.protobuf.message.Message):
    """FFI Events"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    INVOCATION_ID_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    REQUEST_ID_FIELD_NUMBER: builtins.int
    CALLER_IDENTITY_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    RESPONSE_TIMEOUT_MS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    invocation_id: builtins.int
    method: builtins.str
    request_id: builtins.str
    caller_identity: builtins.str
    payload: builtins.str
    response_timeout_ms: builtins.int
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        invocation_id: builtins.int | None = ...,
        method: builtins.str | None = ...,
        request_id: builtins.str | None = ...,
        caller_identity: builtins.str | None = ...,
        payload: builtins.str | None = ...,
        response_timeout_ms: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["caller_identity", b"caller_identity", "invocation_id", b"invocation_id", "local_participant_handle", b"local_participant_handle", "method", b"method", "payload", b"payload", "request_id", b"request_id", "response_timeout_ms", b"response_timeout_ms"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["caller_identity", b"caller_identity", "invocation_id", b"invocation_id", "local_participant_handle", b"local_participant_handle", "method", b"method", "payload", b"payload", "request_id", b"request_id", "response_timeout_ms", b"response_timeout_ms"]) -> None: ...

global___RpcMethodInvocationEvent = RpcMethodInvocationEvent
