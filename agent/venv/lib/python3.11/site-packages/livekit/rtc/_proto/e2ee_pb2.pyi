"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _EncryptionType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _EncryptionTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_EncryptionType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    NONE: _EncryptionType.ValueType  # 0
    GCM: _EncryptionType.ValueType  # 1
    CUSTOM: _EncryptionType.ValueType  # 2

class EncryptionType(_EncryptionType, metaclass=_EncryptionTypeEnumTypeWrapper):
    """TODO(theomonnom): Should FrameCryptor be stateful on the client side and have their own handle?"""

NONE: EncryptionType.ValueType  # 0
GCM: EncryptionType.ValueType  # 1
CUSTOM: EncryptionType.ValueType  # 2
global___EncryptionType = EncryptionType

class _EncryptionState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _EncryptionStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_EncryptionState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    NEW: _EncryptionState.ValueType  # 0
    OK: _EncryptionState.ValueType  # 1
    ENCRYPTION_FAILED: _EncryptionState.ValueType  # 2
    DECRYPTION_FAILED: _EncryptionState.ValueType  # 3
    MISSING_KEY: _EncryptionState.ValueType  # 4
    KEY_RATCHETED: _EncryptionState.ValueType  # 5
    INTERNAL_ERROR: _EncryptionState.ValueType  # 6

class EncryptionState(_EncryptionState, metaclass=_EncryptionStateEnumTypeWrapper): ...

NEW: EncryptionState.ValueType  # 0
OK: EncryptionState.ValueType  # 1
ENCRYPTION_FAILED: EncryptionState.ValueType  # 2
DECRYPTION_FAILED: EncryptionState.ValueType  # 3
MISSING_KEY: EncryptionState.ValueType  # 4
KEY_RATCHETED: EncryptionState.ValueType  # 5
INTERNAL_ERROR: EncryptionState.ValueType  # 6
global___EncryptionState = EncryptionState

@typing.final
class FrameCryptor(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    ENABLED_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    key_index: builtins.int
    enabled: builtins.bool
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
        key_index: builtins.int | None = ...,
        enabled: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["enabled", b"enabled", "key_index", b"key_index", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["enabled", b"enabled", "key_index", b"key_index", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___FrameCryptor = FrameCryptor

@typing.final
class KeyProviderOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_KEY_FIELD_NUMBER: builtins.int
    RATCHET_WINDOW_SIZE_FIELD_NUMBER: builtins.int
    RATCHET_SALT_FIELD_NUMBER: builtins.int
    FAILURE_TOLERANCE_FIELD_NUMBER: builtins.int
    shared_key: builtins.bytes
    """Only specify if you want to use a shared_key"""
    ratchet_window_size: builtins.int
    ratchet_salt: builtins.bytes
    failure_tolerance: builtins.int
    """-1 = no tolerance"""
    def __init__(
        self,
        *,
        shared_key: builtins.bytes | None = ...,
        ratchet_window_size: builtins.int | None = ...,
        ratchet_salt: builtins.bytes | None = ...,
        failure_tolerance: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["failure_tolerance", b"failure_tolerance", "ratchet_salt", b"ratchet_salt", "ratchet_window_size", b"ratchet_window_size", "shared_key", b"shared_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["failure_tolerance", b"failure_tolerance", "ratchet_salt", b"ratchet_salt", "ratchet_window_size", b"ratchet_window_size", "shared_key", b"shared_key"]) -> None: ...

global___KeyProviderOptions = KeyProviderOptions

@typing.final
class E2eeOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENCRYPTION_TYPE_FIELD_NUMBER: builtins.int
    KEY_PROVIDER_OPTIONS_FIELD_NUMBER: builtins.int
    encryption_type: global___EncryptionType.ValueType
    @property
    def key_provider_options(self) -> global___KeyProviderOptions: ...
    def __init__(
        self,
        *,
        encryption_type: global___EncryptionType.ValueType | None = ...,
        key_provider_options: global___KeyProviderOptions | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["encryption_type", b"encryption_type", "key_provider_options", b"key_provider_options"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["encryption_type", b"encryption_type", "key_provider_options", b"key_provider_options"]) -> None: ...

global___E2eeOptions = E2eeOptions

@typing.final
class E2eeManagerSetEnabledRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENABLED_FIELD_NUMBER: builtins.int
    enabled: builtins.bool
    def __init__(
        self,
        *,
        enabled: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["enabled", b"enabled"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["enabled", b"enabled"]) -> None: ...

global___E2eeManagerSetEnabledRequest = E2eeManagerSetEnabledRequest

@typing.final
class E2eeManagerSetEnabledResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___E2eeManagerSetEnabledResponse = E2eeManagerSetEnabledResponse

@typing.final
class E2eeManagerGetFrameCryptorsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___E2eeManagerGetFrameCryptorsRequest = E2eeManagerGetFrameCryptorsRequest

@typing.final
class E2eeManagerGetFrameCryptorsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FRAME_CRYPTORS_FIELD_NUMBER: builtins.int
    @property
    def frame_cryptors(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FrameCryptor]: ...
    def __init__(
        self,
        *,
        frame_cryptors: collections.abc.Iterable[global___FrameCryptor] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["frame_cryptors", b"frame_cryptors"]) -> None: ...

global___E2eeManagerGetFrameCryptorsResponse = E2eeManagerGetFrameCryptorsResponse

@typing.final
class FrameCryptorSetEnabledRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    ENABLED_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    enabled: builtins.bool
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
        enabled: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["enabled", b"enabled", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["enabled", b"enabled", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___FrameCryptorSetEnabledRequest = FrameCryptorSetEnabledRequest

@typing.final
class FrameCryptorSetEnabledResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___FrameCryptorSetEnabledResponse = FrameCryptorSetEnabledResponse

@typing.final
class FrameCryptorSetKeyIndexRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    key_index: builtins.int
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___FrameCryptorSetKeyIndexRequest = FrameCryptorSetKeyIndexRequest

@typing.final
class FrameCryptorSetKeyIndexResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___FrameCryptorSetKeyIndexResponse = FrameCryptorSetKeyIndexResponse

@typing.final
class SetSharedKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_KEY_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    shared_key: builtins.bytes
    key_index: builtins.int
    def __init__(
        self,
        *,
        shared_key: builtins.bytes | None = ...,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index", "shared_key", b"shared_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index", "shared_key", b"shared_key"]) -> None: ...

global___SetSharedKeyRequest = SetSharedKeyRequest

@typing.final
class SetSharedKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SetSharedKeyResponse = SetSharedKeyResponse

@typing.final
class RatchetSharedKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_INDEX_FIELD_NUMBER: builtins.int
    key_index: builtins.int
    def __init__(
        self,
        *,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index"]) -> None: ...

global___RatchetSharedKeyRequest = RatchetSharedKeyRequest

@typing.final
class RatchetSharedKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NEW_KEY_FIELD_NUMBER: builtins.int
    new_key: builtins.bytes
    def __init__(
        self,
        *,
        new_key: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["new_key", b"new_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["new_key", b"new_key"]) -> None: ...

global___RatchetSharedKeyResponse = RatchetSharedKeyResponse

@typing.final
class GetSharedKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_INDEX_FIELD_NUMBER: builtins.int
    key_index: builtins.int
    def __init__(
        self,
        *,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index"]) -> None: ...

global___GetSharedKeyRequest = GetSharedKeyRequest

@typing.final
class GetSharedKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    key: builtins.bytes
    def __init__(
        self,
        *,
        key: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key", b"key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key", b"key"]) -> None: ...

global___GetSharedKeyResponse = GetSharedKeyResponse

@typing.final
class SetKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    KEY_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    key: builtins.bytes
    key_index: builtins.int
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        key: builtins.bytes | None = ...,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key", b"key", "key_index", b"key_index", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key", b"key", "key_index", b"key_index", "participant_identity", b"participant_identity"]) -> None: ...

global___SetKeyRequest = SetKeyRequest

@typing.final
class SetKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SetKeyResponse = SetKeyResponse

@typing.final
class RatchetKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    key_index: builtins.int
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity"]) -> None: ...

global___RatchetKeyRequest = RatchetKeyRequest

@typing.final
class RatchetKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NEW_KEY_FIELD_NUMBER: builtins.int
    new_key: builtins.bytes
    def __init__(
        self,
        *,
        new_key: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["new_key", b"new_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["new_key", b"new_key"]) -> None: ...

global___RatchetKeyResponse = RatchetKeyResponse

@typing.final
class GetKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    KEY_INDEX_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    key_index: builtins.int
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        key_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_index", b"key_index", "participant_identity", b"participant_identity"]) -> None: ...

global___GetKeyRequest = GetKeyRequest

@typing.final
class GetKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    key: builtins.bytes
    def __init__(
        self,
        *,
        key: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key", b"key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key", b"key"]) -> None: ...

global___GetKeyResponse = GetKeyResponse

@typing.final
class E2eeRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROOM_HANDLE_FIELD_NUMBER: builtins.int
    MANAGER_SET_ENABLED_FIELD_NUMBER: builtins.int
    MANAGER_GET_FRAME_CRYPTORS_FIELD_NUMBER: builtins.int
    CRYPTOR_SET_ENABLED_FIELD_NUMBER: builtins.int
    CRYPTOR_SET_KEY_INDEX_FIELD_NUMBER: builtins.int
    SET_SHARED_KEY_FIELD_NUMBER: builtins.int
    RATCHET_SHARED_KEY_FIELD_NUMBER: builtins.int
    GET_SHARED_KEY_FIELD_NUMBER: builtins.int
    SET_KEY_FIELD_NUMBER: builtins.int
    RATCHET_KEY_FIELD_NUMBER: builtins.int
    GET_KEY_FIELD_NUMBER: builtins.int
    room_handle: builtins.int
    @property
    def manager_set_enabled(self) -> global___E2eeManagerSetEnabledRequest: ...
    @property
    def manager_get_frame_cryptors(self) -> global___E2eeManagerGetFrameCryptorsRequest: ...
    @property
    def cryptor_set_enabled(self) -> global___FrameCryptorSetEnabledRequest: ...
    @property
    def cryptor_set_key_index(self) -> global___FrameCryptorSetKeyIndexRequest: ...
    @property
    def set_shared_key(self) -> global___SetSharedKeyRequest: ...
    @property
    def ratchet_shared_key(self) -> global___RatchetSharedKeyRequest: ...
    @property
    def get_shared_key(self) -> global___GetSharedKeyRequest: ...
    @property
    def set_key(self) -> global___SetKeyRequest: ...
    @property
    def ratchet_key(self) -> global___RatchetKeyRequest: ...
    @property
    def get_key(self) -> global___GetKeyRequest: ...
    def __init__(
        self,
        *,
        room_handle: builtins.int | None = ...,
        manager_set_enabled: global___E2eeManagerSetEnabledRequest | None = ...,
        manager_get_frame_cryptors: global___E2eeManagerGetFrameCryptorsRequest | None = ...,
        cryptor_set_enabled: global___FrameCryptorSetEnabledRequest | None = ...,
        cryptor_set_key_index: global___FrameCryptorSetKeyIndexRequest | None = ...,
        set_shared_key: global___SetSharedKeyRequest | None = ...,
        ratchet_shared_key: global___RatchetSharedKeyRequest | None = ...,
        get_shared_key: global___GetSharedKeyRequest | None = ...,
        set_key: global___SetKeyRequest | None = ...,
        ratchet_key: global___RatchetKeyRequest | None = ...,
        get_key: global___GetKeyRequest | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cryptor_set_enabled", b"cryptor_set_enabled", "cryptor_set_key_index", b"cryptor_set_key_index", "get_key", b"get_key", "get_shared_key", b"get_shared_key", "manager_get_frame_cryptors", b"manager_get_frame_cryptors", "manager_set_enabled", b"manager_set_enabled", "message", b"message", "ratchet_key", b"ratchet_key", "ratchet_shared_key", b"ratchet_shared_key", "room_handle", b"room_handle", "set_key", b"set_key", "set_shared_key", b"set_shared_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cryptor_set_enabled", b"cryptor_set_enabled", "cryptor_set_key_index", b"cryptor_set_key_index", "get_key", b"get_key", "get_shared_key", b"get_shared_key", "manager_get_frame_cryptors", b"manager_get_frame_cryptors", "manager_set_enabled", b"manager_set_enabled", "message", b"message", "ratchet_key", b"ratchet_key", "ratchet_shared_key", b"ratchet_shared_key", "room_handle", b"room_handle", "set_key", b"set_key", "set_shared_key", b"set_shared_key"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["manager_set_enabled", "manager_get_frame_cryptors", "cryptor_set_enabled", "cryptor_set_key_index", "set_shared_key", "ratchet_shared_key", "get_shared_key", "set_key", "ratchet_key", "get_key"] | None: ...

global___E2eeRequest = E2eeRequest

@typing.final
class E2eeResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MANAGER_SET_ENABLED_FIELD_NUMBER: builtins.int
    MANAGER_GET_FRAME_CRYPTORS_FIELD_NUMBER: builtins.int
    CRYPTOR_SET_ENABLED_FIELD_NUMBER: builtins.int
    CRYPTOR_SET_KEY_INDEX_FIELD_NUMBER: builtins.int
    SET_SHARED_KEY_FIELD_NUMBER: builtins.int
    RATCHET_SHARED_KEY_FIELD_NUMBER: builtins.int
    GET_SHARED_KEY_FIELD_NUMBER: builtins.int
    SET_KEY_FIELD_NUMBER: builtins.int
    RATCHET_KEY_FIELD_NUMBER: builtins.int
    GET_KEY_FIELD_NUMBER: builtins.int
    @property
    def manager_set_enabled(self) -> global___E2eeManagerSetEnabledResponse: ...
    @property
    def manager_get_frame_cryptors(self) -> global___E2eeManagerGetFrameCryptorsResponse: ...
    @property
    def cryptor_set_enabled(self) -> global___FrameCryptorSetEnabledResponse: ...
    @property
    def cryptor_set_key_index(self) -> global___FrameCryptorSetKeyIndexResponse: ...
    @property
    def set_shared_key(self) -> global___SetSharedKeyResponse: ...
    @property
    def ratchet_shared_key(self) -> global___RatchetSharedKeyResponse: ...
    @property
    def get_shared_key(self) -> global___GetSharedKeyResponse: ...
    @property
    def set_key(self) -> global___SetKeyResponse: ...
    @property
    def ratchet_key(self) -> global___RatchetKeyResponse: ...
    @property
    def get_key(self) -> global___GetKeyResponse: ...
    def __init__(
        self,
        *,
        manager_set_enabled: global___E2eeManagerSetEnabledResponse | None = ...,
        manager_get_frame_cryptors: global___E2eeManagerGetFrameCryptorsResponse | None = ...,
        cryptor_set_enabled: global___FrameCryptorSetEnabledResponse | None = ...,
        cryptor_set_key_index: global___FrameCryptorSetKeyIndexResponse | None = ...,
        set_shared_key: global___SetSharedKeyResponse | None = ...,
        ratchet_shared_key: global___RatchetSharedKeyResponse | None = ...,
        get_shared_key: global___GetSharedKeyResponse | None = ...,
        set_key: global___SetKeyResponse | None = ...,
        ratchet_key: global___RatchetKeyResponse | None = ...,
        get_key: global___GetKeyResponse | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cryptor_set_enabled", b"cryptor_set_enabled", "cryptor_set_key_index", b"cryptor_set_key_index", "get_key", b"get_key", "get_shared_key", b"get_shared_key", "manager_get_frame_cryptors", b"manager_get_frame_cryptors", "manager_set_enabled", b"manager_set_enabled", "message", b"message", "ratchet_key", b"ratchet_key", "ratchet_shared_key", b"ratchet_shared_key", "set_key", b"set_key", "set_shared_key", b"set_shared_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cryptor_set_enabled", b"cryptor_set_enabled", "cryptor_set_key_index", b"cryptor_set_key_index", "get_key", b"get_key", "get_shared_key", b"get_shared_key", "manager_get_frame_cryptors", b"manager_get_frame_cryptors", "manager_set_enabled", b"manager_set_enabled", "message", b"message", "ratchet_key", b"ratchet_key", "ratchet_shared_key", b"ratchet_shared_key", "set_key", b"set_key", "set_shared_key", b"set_shared_key"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["manager_set_enabled", "manager_get_frame_cryptors", "cryptor_set_enabled", "cryptor_set_key_index", "set_shared_key", "ratchet_shared_key", "get_shared_key", "set_key", "ratchet_key", "get_key"] | None: ...

global___E2eeResponse = E2eeResponse
