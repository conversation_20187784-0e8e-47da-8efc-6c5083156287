home = /Users/<USER>/.pyenv/versions/3.11.10/Library/Frameworks/Python.framework/Versions/3.11/bin
include-system-site-packages = false
version = 3.11.10
executable = /Users/<USER>/.pyenv/versions/3.11.10/Library/Frameworks/Python.framework/Versions/3.11/bin/python3.11
command = /Users/<USER>/.pyenv/versions/3.11.10/Library/Frameworks/Python.framework/Versions/3.11/bin/python3 -m venv /Users/<USER>/repos/livekit-pipecat-demo/agent/venv
