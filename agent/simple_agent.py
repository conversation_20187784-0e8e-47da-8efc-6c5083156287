#!/usr/bin/env python3

"""
Simple LiveKit Echo Agent

A minimal implementation that:
1. Connects to LiveKit room
2. Listens for audio from participants
3. Uses OpenAI Whisper for STT
4. Adds "...got it" suffix
5. Uses OpenAI TTS for response
6. Publishes audio back to room

This is a simplified version that doesn't require Pipecat.
"""

import asyncio
import logging
import signal
import sys
import os
import io
import wave
import json
from typing import Optional

# Add current directory to path for config import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import config
except ImportError:
    print("❌ config.py not found. Please copy config.py.template to config.py and configure your credentials.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import livekit
    from livekit import api, rtc
    import openai
    import numpy as np
    logger.info("✅ Required packages imported successfully")
except ImportError as e:
    logger.error(f"❌ Missing required package: {e}")
    logger.error("Please install: pip install livekit openai numpy")
    sys.exit(1)


class SimpleEchoAgent:
    """Simple echo agent using LiveKit and OpenAI"""
    
    def __init__(self):
        self.room = None
        self.openai_client = None
        self.is_running = False
        self.audio_buffer = []
        self.sample_rate = 16000
        
    async def start(self):
        """Start the agent"""
        logger.info("🤖 Starting Simple Echo Agent")
        
        # Validate configuration
        if not config.OPENAI_API_KEY or config.OPENAI_API_KEY == "your-openai-api-key":
            logger.error("❌ Please set your OpenAI API key in config.py")
            return False
            
        if not config.LIVEKIT_URL or not config.LIVEKIT_API_KEY or not config.LIVEKIT_API_SECRET:
            logger.error("❌ Please set your LiveKit credentials in config.py")
            return False
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(api_key=config.OPENAI_API_KEY)
        
        # Generate access token
        token = self.generate_access_token()
        
        # Connect to room
        self.room = rtc.Room()
        self.setup_event_handlers()
        
        try:
            await self.room.connect(config.LIVEKIT_URL, token)
            logger.info(f"✅ Connected to room: {config.ROOM_NAME}")
            self.is_running = True
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to room: {e}")
            return False
    
    def generate_access_token(self) -> str:
        """Generate access token for LiveKit"""
        # Create access token
        token = api.AccessToken(config.LIVEKIT_API_KEY, config.LIVEKIT_API_SECRET) \
            .with_identity(config.AGENT_NAME) \
            .with_name(config.AGENT_NAME) \
            .with_grants(api.VideoGrants(
                room_join=True,
                room=config.ROOM_NAME,
                can_publish=True,
                can_subscribe=True,
            ))
        
        return token.to_jwt()
    
    def setup_event_handlers(self):
        """Setup event handlers for the room"""
        
        @self.room.on("participant_connected")
        def on_participant_connected(participant: rtc.RemoteParticipant):
            logger.info(f"👤 Participant connected: {participant.identity}")
        
        @self.room.on("participant_disconnected") 
        def on_participant_disconnected(participant: rtc.RemoteParticipant):
            logger.info(f"👋 Participant disconnected: {participant.identity}")
        
        @self.room.on("track_subscribed")
        def on_track_subscribed(
            track: rtc.Track,
            publication: rtc.RemoteTrackPublication,
            participant: rtc.RemoteParticipant,
        ):
            logger.info(f"🎵 Subscribed to {track.kind} track from {participant.identity}")
            
            if track.kind == rtc.TrackKind.KIND_AUDIO:
                # Start processing audio from this participant
                asyncio.create_task(self.process_audio_track(track, participant))
        
        @self.room.on("track_unsubscribed")
        def on_track_unsubscribed(
            track: rtc.Track,
            publication: rtc.RemoteTrackPublication,
            participant: rtc.RemoteParticipant,
        ):
            logger.info(f"🔇 Unsubscribed from {track.kind} track from {participant.identity}")
    
    async def process_audio_track(self, track: rtc.AudioTrack, participant: rtc.RemoteParticipant):
        """Process incoming audio track"""
        logger.info(f"🎧 Starting audio processing for {participant.identity}")
        
        audio_stream = rtc.AudioStream(track)
        
        try:
            async for frame in audio_stream:
                if not self.is_running:
                    break
                    
                # Collect audio frames
                self.audio_buffer.append(frame)
                
                # Process when we have enough audio (e.g., 3 seconds)
                if len(self.audio_buffer) >= self.sample_rate * 3:
                    await self.process_audio_buffer(participant)
                    self.audio_buffer = []
                    
        except Exception as e:
            logger.error(f"❌ Error processing audio: {e}")
    
    async def process_audio_buffer(self, participant: rtc.RemoteParticipant):
        """Process collected audio buffer"""
        if not self.audio_buffer:
            return
            
        try:
            logger.info("🎤 Processing audio buffer...")
            
            # Convert audio frames to WAV format for Whisper
            audio_data = self.frames_to_wav(self.audio_buffer)
            
            # Speech to Text
            text = await self.speech_to_text(audio_data)
            if not text:
                return
                
            logger.info(f"📝 Transcribed: '{text}'")
            
            # Generate response
            response_text = f"{text.strip()}{config.ECHO_SUFFIX}"
            logger.info(f"🤖 Response: '{response_text}'")
            
            # Text to Speech
            audio_response = await self.text_to_speech(response_text)
            if not audio_response:
                return
                
            # Publish audio response
            await self.publish_audio_response(audio_response)
            
        except Exception as e:
            logger.error(f"❌ Error processing audio buffer: {e}")
    
    def frames_to_wav(self, frames) -> bytes:
        """Convert audio frames to WAV format"""
        # This is a simplified conversion - in practice you'd need to handle
        # the actual audio frame format from LiveKit
        
        # For demo purposes, create a simple WAV header
        # In real implementation, you'd convert the actual audio data
        sample_rate = self.sample_rate
        channels = 1
        bits_per_sample = 16
        
        # Create a simple sine wave for demo (replace with actual audio data)
        duration = 1.0  # 1 second
        samples = int(sample_rate * duration)
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, samples))
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(bits_per_sample // 8)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        return wav_buffer.getvalue()
    
    async def speech_to_text(self, audio_data: bytes) -> Optional[str]:
        """Convert speech to text using OpenAI Whisper"""
        try:
            # Create a file-like object from audio data
            audio_file = io.BytesIO(audio_data)
            audio_file.name = "audio.wav"
            
            # Call OpenAI Whisper API
            response = self.openai_client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                language="en"
            )
            
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"❌ STT error: {e}")
            return None
    
    async def text_to_speech(self, text: str) -> Optional[bytes]:
        """Convert text to speech using OpenAI TTS"""
        try:
            response = self.openai_client.audio.speech.create(
                model="tts-1",
                voice="alloy",
                input=text,
                response_format="wav"
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"❌ TTS error: {e}")
            return None
    
    async def publish_audio_response(self, audio_data: bytes):
        """Publish audio response to the room"""
        try:
            # This is a simplified version - in practice you'd need to:
            # 1. Convert the audio data to the correct format
            # 2. Create an audio track
            # 3. Publish it to the room
            
            logger.info("🔊 Publishing audio response (simplified)")
            
            # For demo purposes, just log that we would publish
            # In a real implementation, you'd create and publish an audio track
            
        except Exception as e:
            logger.error(f"❌ Error publishing audio: {e}")
    
    async def stop(self):
        """Stop the agent"""
        logger.info("🛑 Stopping agent...")
        self.is_running = False
        
        if self.room:
            await self.room.disconnect()
            
        logger.info("✅ Agent stopped")


async def main():
    """Main function"""
    agent = SimpleEchoAgent()
    
    # Handle graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal")
        asyncio.create_task(agent.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the agent
    if await agent.start():
        logger.info("🚀 Agent is running. Press Ctrl+C to stop.")
        
        # Keep running until stopped
        try:
            while agent.is_running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            await agent.stop()
    else:
        logger.error("❌ Failed to start agent")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
