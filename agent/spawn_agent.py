#!/usr/bin/env python3

"""
LiveKit + Pipecat Integration Demo Agent

This agent:
1. Connects to a LiveKit room as a participant
2. Listens for user audio input
3. Converts speech to text (STT)
4. Adds "...got it" suffix to the text
5. Converts back to speech (TTS)
6. Publishes audio response to the room

Features:
- Real-time audio processing
- Barge-in support (user can interrupt)
- Latency optimization
- Error handling and reconnection
"""

import asyncio
import logging
import signal
import sys
import os
from typing import Optional

# Add current directory to path for config import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import config
except ImportError:
    print("❌ config.py not found. Please copy config.py.template to config.py and configure your credentials.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check if we have the required packages
try:
    from pipecat.frames.frames import Frame, AudioRawFrame, TextFrame
    from pipecat.pipeline.pipeline import Pipeline
    from pipecat.pipeline.runner import PipelineRunner
    from pipecat.pipeline.task import PipelineTask
    from pipecat.services.openai import OpenAITTSService, OpenAISTTService
    from pipecat.transports.services.livekit import LiveKitTransport, LiveKitParams
    from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
    from pipecat.processors.frame_processor import FrameDirection, FrameProcessor

    logger.info("✅ Pipecat imports successful")

except ImportError as e:
    logger.error(f"❌ Failed to import Pipecat: {e}")
    logger.error("Please install Pipecat: pip install pipecat-ai")
    sys.exit(1)


class EchoProcessor(FrameProcessor):
    """Simple processor that adds 'got it' to user input"""

    def __init__(self):
        super().__init__()
        self.response_suffix = config.ECHO_SUFFIX

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        """Process incoming frames"""
        if isinstance(frame, TextFrame):
            # Process text input from STT
            user_text = frame.text.strip()
            if user_text:
                # Create response with suffix
                response_text = f"{user_text}{self.response_suffix}"
                logger.info(f"User: '{user_text}' -> Agent: '{response_text}'")

                # Create new text frame with response
                response_frame = TextFrame(response_text)
                await self.push_frame(response_frame, direction)
            else:
                # Handle empty input
                response_frame = TextFrame("I didn't catch that. Could you repeat?")
                await self.push_frame(response_frame, direction)
        else:
            # Pass through other frame types
            await self.push_frame(frame, direction)


async def main():
    """Main function to start the agent"""
    logger.info("🤖 Starting LiveKit + Pipecat Demo Agent")

    # Validate configuration
    if not config.OPENAI_API_KEY or config.OPENAI_API_KEY == "your-openai-api-key":
        logger.error("❌ Please set your OpenAI API key in config.py")
        return

    if not config.LIVEKIT_URL or not config.LIVEKIT_API_KEY or not config.LIVEKIT_API_SECRET:
        logger.error("❌ Please set your LiveKit credentials in config.py")
        return

    try:
        # Initialize transport
        transport = LiveKitTransport(
            params=LiveKitParams(
                api_key=config.LIVEKIT_API_KEY,
                api_secret=config.LIVEKIT_API_SECRET,
                url=config.LIVEKIT_URL,
                room_name=config.ROOM_NAME,
                participant_name=config.AGENT_NAME,
            )
        )

        # Initialize STT service
        stt = OpenAISTTService(
            api_key=config.OPENAI_API_KEY,
            model="whisper-1",
        )

        # Initialize TTS service
        tts = OpenAITTSService(
            api_key=config.OPENAI_API_KEY,
            voice="alloy",
        )

        # Initialize our echo processor
        echo_processor = EchoProcessor()

        # Create pipeline
        pipeline = Pipeline([
            transport.input(),   # Audio input from LiveKit
            stt,                # Speech to text
            echo_processor,     # Our echo logic
            tts,                # Text to speech
            transport.output(), # Audio output to LiveKit
        ])

        # Create and run the task
        task = PipelineTask(pipeline)

        logger.info(f"🚀 Agent connecting to room: {config.ROOM_NAME}")
        logger.info("💬 Ready to receive audio and respond with echo + 'got it'")

        # Run the pipeline
        runner = PipelineRunner()
        await runner.run(task)

    except KeyboardInterrupt:
        logger.info("👋 Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Agent failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # Handle graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal, cleaning up...")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Run the async main function
    asyncio.run(main())
