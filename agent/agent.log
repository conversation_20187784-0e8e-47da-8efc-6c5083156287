2025-09-13 13:43:18,823 - __main__ - INFO - ✅ Required packages imported successfully
2025-09-13 13:43:18,823 - __main__ - INFO - 🤖 Starting Simple Echo Agent
2025-09-13 13:43:18,846 - livekit - INFO - livekit_ffi::server:125:livekit_ffi::server - initializing ffi server v0.5.0
2025-09-13 13:43:18,847 - livekit - INFO - livekit_ffi::cabi:27:livekit_ffi::cabi - initializing ffi server v0.5.0
2025-09-13 13:43:18,860 - livekit - INFO - livekit_api::signal_client::signal_stream:88:livekit_api::signal_client::signal_stream - connecting to ws://localhost:7880/rtc?sdk=rust&protocol=9&auto_subscribe=1&adaptive_stream=0&access_token=...
2025-09-13 13:43:18,990 - __main__ - ERROR - ❌ Failed to connect to room: engine: signal failure: ws failure: IO error: Connection reset by peer (os error 54)
2025-09-13 13:43:18,990 - __main__ - ERROR - ❌ Failed to start agent
2025-09-13 13:43:18,990 - livekit - ERROR - livekit_ffi::server::room:200:livekit_ffi::server::room - error while connecting to a room: engine: signal failure: ws failure: IO error: Connection reset by peer (os error 54)
