# LiveKit + Pipecat Demo Configuration
# Copy this file to config.py and fill in your credentials

# LiveKit Configuration
# Option A: LiveKit Cloud (Recommended)
LIVEKIT_URL = "wss://your-project.livekit.cloud"
LIVEKIT_API_KEY = "your-api-key"
LIVEKIT_API_SECRET = "your-api-secret"

# Option B: Local LiveKit Server (if using docker-compose)
# LIVEKIT_URL = "ws://localhost:7880"
# LIVEKIT_API_KEY = "devkey"
# LIVEKIT_API_SECRET = "secret"

# AI Service Configuration
OPENAI_API_KEY = "your-openai-api-key"

# Optional: ElevenLabs for better TTS (comment out to use OpenAI TTS)
# ELEVENLABS_API_KEY = "your-elevenlabs-api-key"
# ELEVENLABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice

# Room Configuration
ROOM_NAME = "pipecat-demo"
AGENT_NAME = "PipecatAgent"

# Audio Configuration
SAMPLE_RATE = 16000
CHANNELS = 1

# Agent Behavior
ECHO_SUFFIX = "...got it"
RESPONSE_TIMEOUT = 5.0  # seconds
BARGE_IN_ENABLED = True

# Logging
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
